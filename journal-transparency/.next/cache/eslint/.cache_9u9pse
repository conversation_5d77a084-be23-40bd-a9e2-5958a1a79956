[{"/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/DataTable.tsx": "1", "/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/FilterPanel.tsx": "2", "/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/Footer.tsx": "3", "/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/Header.tsx": "4", "/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/LoadingSkeleton.tsx": "5", "/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/Recommendations.tsx": "6", "/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/ScatterPlot.tsx": "7", "/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/SelectedJournals.tsx": "8", "/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/layout.tsx": "9", "/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/page.tsx": "10", "/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/store.ts": "11", "/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/types.ts": "12"}, {"size": 8201, "mtime": 1752956764336, "results": "13", "hashOfConfig": "14"}, {"size": 6419, "mtime": 1752957454911, "results": "15", "hashOfConfig": "14"}, {"size": 792, "mtime": 1752954549162, "results": "16", "hashOfConfig": "14"}, {"size": 3475, "mtime": 1752954228667, "results": "17", "hashOfConfig": "14"}, {"size": 5983, "mtime": 1752947093898, "results": "18", "hashOfConfig": "14"}, {"size": 5989, "mtime": 1752958566972, "results": "19", "hashOfConfig": "14"}, {"size": 5294, "mtime": 1752958475489, "results": "20", "hashOfConfig": "14"}, {"size": 8638, "mtime": 1752958609232, "results": "21", "hashOfConfig": "14"}, {"size": 689, "mtime": 1752946257184, "results": "22", "hashOfConfig": "14"}, {"size": 2590, "mtime": 1752956673088, "results": "23", "hashOfConfig": "14"}, {"size": 3168, "mtime": 1752958448125, "results": "24", "hashOfConfig": "14"}, {"size": 783, "mtime": 1752957598883, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "cbmjej", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/DataTable.tsx", ["62"], [], "/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/FilterPanel.tsx", [], [], "/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/Footer.tsx", [], [], "/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/Header.tsx", [], [], "/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/LoadingSkeleton.tsx", [], [], "/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/Recommendations.tsx", ["63", "64", "65"], [], "/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/ScatterPlot.tsx", ["66", "67"], [], "/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/SelectedJournals.tsx", [], [], "/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/layout.tsx", [], [], "/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/page.tsx", [], [], "/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/store.ts", [], [], "/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/types.ts", [], [], {"ruleId": "68", "severity": 1, "message": "69", "line": 172, "column": 5, "nodeType": "70", "endLine": 172, "endColumn": 7, "suggestions": "71"}, {"ruleId": "72", "severity": 2, "message": "73", "line": 54, "column": 31, "nodeType": "74", "messageId": "75", "endLine": 54, "endColumn": 34, "suggestions": "76"}, {"ruleId": "77", "severity": 2, "message": "78", "line": 96, "column": 32, "nodeType": "79", "messageId": "80", "suggestions": "81"}, {"ruleId": "77", "severity": 2, "message": "78", "line": 96, "column": 40, "nodeType": "79", "messageId": "80", "suggestions": "82"}, {"ruleId": "72", "severity": 2, "message": "73", "line": 74, "column": 47, "nodeType": "74", "messageId": "75", "endLine": 74, "endColumn": 50, "suggestions": "83"}, {"ruleId": "72", "severity": 2, "message": "73", "line": 97, "column": 35, "nodeType": "74", "messageId": "75", "endLine": 97, "endColumn": 38, "suggestions": "84"}, "react-hooks/exhaustive-deps", "React Hook useMemo has a missing dependency: 'filters.searchTerm'. Either include it or remove the dependency array.", "ArrayExpression", ["85"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["86", "87"], "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["88", "89", "90", "91"], ["92", "93", "94", "95"], ["96", "97"], ["98", "99"], {"desc": "100", "fix": "101"}, {"messageId": "102", "fix": "103", "desc": "104"}, {"messageId": "105", "fix": "106", "desc": "107"}, {"messageId": "108", "data": "109", "fix": "110", "desc": "111"}, {"messageId": "108", "data": "112", "fix": "113", "desc": "114"}, {"messageId": "108", "data": "115", "fix": "116", "desc": "117"}, {"messageId": "108", "data": "118", "fix": "119", "desc": "120"}, {"messageId": "108", "data": "121", "fix": "122", "desc": "111"}, {"messageId": "108", "data": "123", "fix": "124", "desc": "114"}, {"messageId": "108", "data": "125", "fix": "126", "desc": "117"}, {"messageId": "108", "data": "127", "fix": "128", "desc": "120"}, {"messageId": "102", "fix": "129", "desc": "104"}, {"messageId": "105", "fix": "130", "desc": "107"}, {"messageId": "102", "fix": "131", "desc": "104"}, {"messageId": "105", "fix": "132", "desc": "107"}, "Update the dependencies array to be: [filters.searchTerm]", {"range": "133", "text": "134"}, "suggestUnknown", {"range": "135", "text": "136"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "137", "text": "138"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "replaceWithAlt", {"alt": "139"}, {"range": "140", "text": "141"}, "Replace with `&quot;`.", {"alt": "142"}, {"range": "143", "text": "144"}, "Replace with `&ldquo;`.", {"alt": "145"}, {"range": "146", "text": "147"}, "Replace with `&#34;`.", {"alt": "148"}, {"range": "149", "text": "150"}, "Replace with `&rdquo;`.", {"alt": "139"}, {"range": "151", "text": "152"}, {"alt": "142"}, {"range": "153", "text": "154"}, {"alt": "145"}, {"range": "155", "text": "156"}, {"alt": "148"}, {"range": "157", "text": "158"}, {"range": "159", "text": "136"}, {"range": "160", "text": "138"}, {"range": "161", "text": "136"}, {"range": "162", "text": "138"}, [5019, 5021], "[filters.searchTerm]", [1878, 1881], "unknown", [1878, 1881], "never", "&quot;", [3660, 3759], " &quot;N/A APC\" means pricing information is unavailable - this is NOT the same as free journals.\n      ", "&ldquo;", [3660, 3759], " &ldquo;N/A APC\" means pricing information is unavailable - this is NOT the same as free journals.\n      ", "&#34;", [3660, 3759], " &#34;N/A APC\" means pricing information is unavailable - this is NOT the same as free journals.\n      ", "&rdquo;", [3660, 3759], " &rdquo;N/A APC\" means pricing information is unavailable - this is NOT the same as free journals.\n      ", [3660, 3759], " \"N/A APC&quot; means pricing information is unavailable - this is NOT the same as free journals.\n      ", [3660, 3759], " \"N/A APC&ldquo; means pricing information is unavailable - this is NOT the same as free journals.\n      ", [3660, 3759], " \"N/A APC&#34; means pricing information is unavailable - this is NOT the same as free journals.\n      ", [3660, 3759], " \"N/A APC&rdquo; means pricing information is unavailable - this is NOT the same as free journals.\n      ", [2357, 2360], [2357, 2360], [3286, 3289], [3286, 3289]]