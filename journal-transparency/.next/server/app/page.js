(()=>{var a={};a.id=974,a.ids=[974],a.modules={15:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.noop=function(){}},144:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(7509);b.property=function(a){return function(b){return d.get(b,a)}}},175:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isPlainObject=function(a){if(!a||"object"!=typeof a)return!1;let b=Object.getPrototypeOf(a);return(null===b||b===Object.prototype||null===Object.getPrototypeOf(b))&&"[object Object]"===Object.prototype.toString.call(a)}},261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},324:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isPlainObject=function(a){if("object"!=typeof a||null==a)return!1;if(null===Object.getPrototypeOf(a))return!0;if("[object Object]"!==Object.prototype.toString.call(a)){let b=a[Symbol.toStringTag];return null!=b&&!!Object.getOwnPropertyDescriptor(a,Symbol.toStringTag)?.writable&&a.toString()===`[object ${b}]`}let b=a;for(;null!==Object.getPrototypeOf(b);)b=Object.getPrototypeOf(b);return Object.getPrototypeOf(a)===b}},415:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(2066),e=c(3038),f=c(9138),g=c(7509),h=c(7841);b.matchesProperty=function(a,b){switch(typeof a){case"object":Object.is(a?.valueOf(),-0)&&(a="-0");break;case"number":a=e.toKey(a)}return b=f.cloneDeep(b),function(c){let e=g.get(c,a);return void 0===e?h.has(c,a):void 0===b?void 0===e:d.isMatch(e,b)}}},440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(1658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},830:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(8730),e=c(9899);b.isArrayLikeObject=function(a){return e.isObjectLike(a)&&d.isArrayLike(a)}},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},907:(a,b,c)=>{"use strict";var d=c(3210),e=c(9760),f="function"==typeof Object.is?Object.is:function(a,b){return a===b&&(0!==a||1/a==1/b)||a!=a&&b!=b},g=e.useSyncExternalStore,h=d.useRef,i=d.useEffect,j=d.useMemo,k=d.useDebugValue;b.useSyncExternalStoreWithSelector=function(a,b,c,d,e){var l=h(null);if(null===l.current){var m={hasValue:!1,value:null};l.current=m}else m=l.current;var n=g(a,(l=j(function(){function a(a){if(!i){if(i=!0,g=a,a=d(a),void 0!==e&&m.hasValue){var b=m.value;if(e(b,a))return h=b}return h=a}if(b=h,f(g,a))return b;var c=d(a);return void 0!==e&&e(b,c)?(g=a,b):(g=a,h=c)}var g,h,i=!1,j=void 0===c?null:c;return[function(){return a(b())},null===j?void 0:function(){return a(j())}]},[b,c,d,e]))[0],l[1]);return i(function(){m.hasValue=!0,m.value=n},[n]),k(n),n}},911:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.debounce=function(a,b=0,c={}){let d;"object"!=typeof c&&(c={});let e=null,f=null,g=null,h=0,i=null,{leading:j=!1,trailing:k=!0,maxWait:l}=c,m="maxWait"in c,n=m?Math.max(Number(l)||0,b):0,o=b=>(null!==e&&(d=a.apply(f,e)),e=f=null,h=b,d),p=a=>(i=null,k&&null!==e)?o(a):d,q=a=>{if(null===g)return!0;let c=a-g,d=m&&a-h>=n;return c>=b||c<0||d},r=()=>{let a=Date.now();if(q(a))return p(a);i=setTimeout(r,(a=>{let c=b-(null===g?0:a-g),d=n-(a-h);return m?Math.min(c,d):c})(a))},s=function(...a){let c=Date.now(),k=q(c);if(e=a,f=this,g=c,k){if(null===i)return(h=c,i=setTimeout(r,b),j&&null!==e)?o(c):d;if(m)return clearTimeout(i),i=setTimeout(r,b),o(c)}return null===i&&(i=setTimeout(r,b)),d};return s.cancel=()=>{null!==i&&clearTimeout(i),h=0,g=e=f=i=null},s.flush=()=>null===i?d:p(Date.now()),s}},921:(a,b,c)=>{a.exports=c(1337).range},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},1117:a=>{"use strict";var b=Object.prototype.hasOwnProperty,c="~";function d(){}function e(a,b,c){this.fn=a,this.context=b,this.once=c||!1}function f(a,b,d,f,g){if("function"!=typeof d)throw TypeError("The listener must be a function");var h=new e(d,f||a,g),i=c?c+b:b;return a._events[i]?a._events[i].fn?a._events[i]=[a._events[i],h]:a._events[i].push(h):(a._events[i]=h,a._eventsCount++),a}function g(a,b){0==--a._eventsCount?a._events=new d:delete a._events[b]}function h(){this._events=new d,this._eventsCount=0}Object.create&&(d.prototype=Object.create(null),new d().__proto__||(c=!1)),h.prototype.eventNames=function(){var a,d,e=[];if(0===this._eventsCount)return e;for(d in a=this._events)b.call(a,d)&&e.push(c?d.slice(1):d);return Object.getOwnPropertySymbols?e.concat(Object.getOwnPropertySymbols(a)):e},h.prototype.listeners=function(a){var b=c?c+a:a,d=this._events[b];if(!d)return[];if(d.fn)return[d.fn];for(var e=0,f=d.length,g=Array(f);e<f;e++)g[e]=d[e].fn;return g},h.prototype.listenerCount=function(a){var b=c?c+a:a,d=this._events[b];return d?d.fn?1:d.length:0},h.prototype.emit=function(a,b,d,e,f,g){var h=c?c+a:a;if(!this._events[h])return!1;var i,j,k=this._events[h],l=arguments.length;if(k.fn){switch(k.once&&this.removeListener(a,k.fn,void 0,!0),l){case 1:return k.fn.call(k.context),!0;case 2:return k.fn.call(k.context,b),!0;case 3:return k.fn.call(k.context,b,d),!0;case 4:return k.fn.call(k.context,b,d,e),!0;case 5:return k.fn.call(k.context,b,d,e,f),!0;case 6:return k.fn.call(k.context,b,d,e,f,g),!0}for(j=1,i=Array(l-1);j<l;j++)i[j-1]=arguments[j];k.fn.apply(k.context,i)}else{var m,n=k.length;for(j=0;j<n;j++)switch(k[j].once&&this.removeListener(a,k[j].fn,void 0,!0),l){case 1:k[j].fn.call(k[j].context);break;case 2:k[j].fn.call(k[j].context,b);break;case 3:k[j].fn.call(k[j].context,b,d);break;case 4:k[j].fn.call(k[j].context,b,d,e);break;default:if(!i)for(m=1,i=Array(l-1);m<l;m++)i[m-1]=arguments[m];k[j].fn.apply(k[j].context,i)}}return!0},h.prototype.on=function(a,b,c){return f(this,a,b,c,!1)},h.prototype.once=function(a,b,c){return f(this,a,b,c,!0)},h.prototype.removeListener=function(a,b,d,e){var f=c?c+a:a;if(!this._events[f])return this;if(!b)return g(this,f),this;var h=this._events[f];if(h.fn)h.fn!==b||e&&!h.once||d&&h.context!==d||g(this,f);else{for(var i=0,j=[],k=h.length;i<k;i++)(h[i].fn!==b||e&&!h[i].once||d&&h[i].context!==d)&&j.push(h[i]);j.length?this._events[f]=1===j.length?j[0]:j:g(this,f)}return this},h.prototype.removeAllListeners=function(a){var b;return a?(b=c?c+a:a,this._events[b]&&g(this,b)):(this._events=new d,this._eventsCount=0),this},h.prototype.off=h.prototype.removeListener,h.prototype.addListener=h.prototype.on,h.prefixed=c,h.EventEmitter=h,a.exports=h},1135:()=>{},1204:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/page.tsx","default")},1251:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isTypedArray=function(a){return ArrayBuffer.isView(a)&&!(a instanceof DataView)}},1337:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(6777),e=c(2750);b.range=function(a,b,c){c&&"number"!=typeof c&&d.isIterateeCall(a,b,c)&&(b=c=void 0),a=e.toFinite(a),void 0===b?(b=a,a=0):b=e.toFinite(b),c=void 0===c?a<b?1:-1:e.toFinite(c);let f=Math.max(Math.ceil((b-a)/(c||1)),0),g=Array(f);for(let b=0;b<f;b++)g[b]=a,a+=c;return g}},1424:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.toArray=function(a){return Array.isArray(a)?a:Array.from(a)}},1428:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.getTag=function(a){return null==a?void 0===a?"[object Undefined]":"[object Null]":Object.prototype.toString.call(a)}},1622:(a,b,c)=>{Promise.resolve().then(c.bind(c,7779))},1640:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isObject=function(a){return null!==a&&("object"==typeof a||"function"==typeof a)}},1653:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.getSymbols=function(a){return Object.getOwnPropertySymbols(a).filter(b=>Object.prototype.propertyIsEnumerable.call(a,b))}},1706:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.eq=function(a,b){return a===b||Number.isNaN(a)&&Number.isNaN(b)}},1870:(a,b,c)=>{Promise.resolve().then(c.bind(c,1204))},2066:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(4454);b.isMatch=function(a,b){return d.isMatchWith(a,b,()=>void 0)}},2371:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(2923);b.cloneDeep=function(a){return d.cloneDeepWithImpl(a,void 0,a,new Map,void 0)}},2457:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(2066),e=c(2371);b.matches=function(a){return a=e.cloneDeep(a),b=>d.isMatch(b,a)}},2681:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.identity=function(a){return a}},2728:(a,b,c)=>{a.exports=c(9911).isEqual},2750:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(5708);b.toFinite=function(a){return a?(a=d.toNumber(a))===1/0||a===-1/0?(a<0?-1:1)*Number.MAX_VALUE:a==a?a:0:0===a?a:0}},2867:(a,b,c)=>{a.exports=c(324).isPlainObject},2923:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(1653),e=c(1428),f=c(7469),g=c(3457),h=c(1251);function i(a,b,c,d=new Map,k){let l=k?.(a,b,c,d);if(null!=l)return l;if(g.isPrimitive(a))return a;if(d.has(a))return d.get(a);if(Array.isArray(a)){let b=Array(a.length);d.set(a,b);for(let e=0;e<a.length;e++)b[e]=i(a[e],e,c,d,k);return Object.hasOwn(a,"index")&&(b.index=a.index),Object.hasOwn(a,"input")&&(b.input=a.input),b}if(a instanceof Date)return new Date(a.getTime());if(a instanceof RegExp){let b=new RegExp(a.source,a.flags);return b.lastIndex=a.lastIndex,b}if(a instanceof Map){let b=new Map;for(let[e,f]of(d.set(a,b),a))b.set(e,i(f,e,c,d,k));return b}if(a instanceof Set){let b=new Set;for(let e of(d.set(a,b),a))b.add(i(e,void 0,c,d,k));return b}if("undefined"!=typeof Buffer&&Buffer.isBuffer(a))return a.subarray();if(h.isTypedArray(a)){let b=new(Object.getPrototypeOf(a)).constructor(a.length);d.set(a,b);for(let e=0;e<a.length;e++)b[e]=i(a[e],e,c,d,k);return b}if(a instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&a instanceof SharedArrayBuffer)return a.slice(0);if(a instanceof DataView){let b=new DataView(a.buffer.slice(0),a.byteOffset,a.byteLength);return d.set(a,b),j(b,a,c,d,k),b}if("undefined"!=typeof File&&a instanceof File){let b=new File([a],a.name,{type:a.type});return d.set(a,b),j(b,a,c,d,k),b}if(a instanceof Blob){let b=new Blob([a],{type:a.type});return d.set(a,b),j(b,a,c,d,k),b}if(a instanceof Error){let b=new a.constructor;return d.set(a,b),b.message=a.message,b.name=a.name,b.stack=a.stack,b.cause=a.cause,j(b,a,c,d,k),b}if("object"==typeof a&&function(a){switch(e.getTag(a)){case f.argumentsTag:case f.arrayTag:case f.arrayBufferTag:case f.dataViewTag:case f.booleanTag:case f.dateTag:case f.float32ArrayTag:case f.float64ArrayTag:case f.int8ArrayTag:case f.int16ArrayTag:case f.int32ArrayTag:case f.mapTag:case f.numberTag:case f.objectTag:case f.regexpTag:case f.setTag:case f.stringTag:case f.symbolTag:case f.uint8ArrayTag:case f.uint8ClampedArrayTag:case f.uint16ArrayTag:case f.uint32ArrayTag:return!0;default:return!1}}(a)){let b=Object.create(Object.getPrototypeOf(a));return d.set(a,b),j(b,a,c,d,k),b}return a}function j(a,b,c=a,e,f){let g=[...Object.keys(b),...d.getSymbols(b)];for(let d=0;d<g.length;d++){let h=g[d],j=Object.getOwnPropertyDescriptor(a,h);(null==j||j.writable)&&(a[h]=i(b[h],h,c,e,f))}}b.cloneDeepWith=function(a,b){return i(a,void 0,a,new Map,b)},b.cloneDeepWithImpl=i,b.copyProperties=j},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3038:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.toKey=function(a){return"string"==typeof a||"symbol"==typeof a?a:Object.is(a?.valueOf?.(),-0)?"-0":String(a)}},3068:(a,b,c)=>{a.exports=c(5446).sortBy},3084:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(911);b.throttle=function(a,b=0,c={}){let{leading:e=!0,trailing:f=!0}=c;return d.debounce(a,b,{leading:e,maxWait:b,trailing:f})}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3332:(a,b,c)=>{"use strict";var d=c(3210),e="function"==typeof Object.is?Object.is:function(a,b){return a===b&&(0!==a||1/a==1/b)||a!=a&&b!=b},f=d.useState,g=d.useEffect,h=d.useLayoutEffect,i=d.useDebugValue;function j(a){var b=a.getSnapshot;a=a.value;try{var c=b();return!e(a,c)}catch(a){return!0}}var k="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(a,b){return b()}:function(a,b){var c=b(),d=f({inst:{value:c,getSnapshot:b}}),e=d[0].inst,k=d[1];return h(function(){e.value=c,e.getSnapshot=b,j(e)&&k({inst:e})},[a,c,b]),g(function(){return j(e)&&k({inst:e}),a(function(){j(e)&&k({inst:e})})},[a]),i(c),c};b.useSyncExternalStore=void 0!==d.useSyncExternalStore?d.useSyncExternalStore:k},3457:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isPrimitive=function(a){return null==a||"object"!=typeof a&&"function"!=typeof a}},3567:(a,b,c)=>{"use strict";var d=c(3210);"function"==typeof Object.is&&Object.is,d.useSyncExternalStore,d.useRef,d.useEffect,d.useMemo,d.useDebugValue},3574:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.toPath=function(a){let b=[],c=a.length;if(0===c)return b;let d=0,e="",f="",g=!1;for(46===a.charCodeAt(0)&&(b.push(""),d++);d<c;){let h=a[d];f?"\\"===h&&d+1<c?e+=a[++d]:h===f?f="":e+=h:g?'"'===h||"'"===h?f=h:"]"===h?(g=!1,b.push(e),e=""):e+=h:"["===h?(g=!0,e&&(b.push(e),e="")):"."===h?e&&(b.push(e),e=""):e+=h,d++}return e&&b.push(e),b}},3731:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(5385),e=c(1424),f=c(8730);b.last=function(a){if(f.isArrayLike(a))return d.last(e.toArray(a))}},3854:(a,b,c)=>{a.exports=c(5263).uniqBy},3873:a=>{"use strict";a.exports=require("path")},4187:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,5227,23)),Promise.resolve().then(c.t.bind(c,6346,23)),Promise.resolve().then(c.t.bind(c,7924,23)),Promise.resolve().then(c.t.bind(c,99,23)),Promise.resolve().then(c.t.bind(c,8243,23)),Promise.resolve().then(c.t.bind(c,8827,23)),Promise.resolve().then(c.t.bind(c,2763,23)),Promise.resolve().then(c.t.bind(c,7173,23)),Promise.resolve().then(c.bind(c,5587))},4431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j,metadata:()=>i});var d=c(7413),e=c(2202),f=c.n(e),g=c(4988),h=c.n(g);c(1135);let i={title:"Create Next App",description:"Generated by create next app"};function j({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:a})})}},4454:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(2066),e=c(1640),f=c(3457),g=c(1706);function h(a,b,c,d){if(b===a)return!0;switch(typeof b){case"object":return function(a,b,c,d){if(null==b)return!0;if(Array.isArray(b))return i(a,b,c,d);if(b instanceof Map){var e=a,g=b,h=c,k=d;if(0===g.size)return!0;if(!(e instanceof Map))return!1;for(let[a,b]of g.entries())if(!1===h(e.get(a),b,a,e,g,k))return!1;return!0}if(b instanceof Set)return j(a,b,c,d);let l=Object.keys(b);if(null==a)return 0===l.length;if(0===l.length)return!0;if(d&&d.has(b))return d.get(b)===a;d&&d.set(b,a);try{for(let e=0;e<l.length;e++){let g=l[e];if(!f.isPrimitive(a)&&!(g in a)||void 0===b[g]&&void 0!==a[g]||null===b[g]&&null!==a[g]||!c(a[g],b[g],g,a,b,d))return!1}return!0}finally{d&&d.delete(b)}}(a,b,c,d);case"function":if(Object.keys(b).length>0)return h(a,{...b},c,d);return g.eq(a,b);default:if(!e.isObject(a))return g.eq(a,b);if("string"==typeof b)return""===b;return!0}}function i(a,b,c,d){if(0===b.length)return!0;if(!Array.isArray(a))return!1;let e=new Set;for(let f=0;f<b.length;f++){let g=b[f],h=!1;for(let i=0;i<a.length;i++){if(e.has(i))continue;let j=a[i],k=!1;if(c(j,g,f,a,b,d)&&(k=!0),k){e.add(i),h=!0;break}}if(!h)return!1}return!0}function j(a,b,c,d){return 0===b.size||a instanceof Set&&i([...a],[...b],c,d)}b.isMatchWith=function(a,b,c){return"function"!=typeof c?d.isMatch(a,b):h(a,b,function a(b,d,e,f,g,i){let j=c(b,d,e,f,g,i);return void 0!==j?!!j:h(b,d,a,i)},new Map)},b.isSetMatch=j},4909:()=>{},5263:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(9618),e=c(2681),f=c(830),g=c(7617);b.uniqBy=function(a,b=e.identity){return f.isArrayLikeObject(a)?d.uniqBy(Array.from(a),g.iteratee(b)):[]}},5314:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isUnsafeProperty=function(a){return"__proto__"===a}},5385:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.last=function(a){return a[a.length-1]}},5446:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(7586),e=c(8382),f=c(6777);b.sortBy=function(a,...b){let c=b.length;return c>1&&f.isIterateeCall(a,b[0],b[1])?b=[]:c>2&&f.isIterateeCall(b[0],b[1],b[2])&&(b=[b[0]]),d.orderBy(a,e.flatten(b),["asc"])}},5664:(a,b,c)=>{a.exports=c(7509).get},5708:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(5819);b.toNumber=function(a){return d.isSymbol(a)?NaN:Number(a)}},5819:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isSymbol=function(a){return"symbol"==typeof a||a instanceof Symbol}},6021:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(5819),e=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,f=/^\w*$/;b.isKey=function(a,b){return!Array.isArray(a)&&(!!("number"==typeof a||"boolean"==typeof a||null==a||d.isSymbol(a))||"string"==typeof a&&(f.test(a)||!e.test(a))||null!=b&&Object.hasOwn(b,a))}},6023:(a,b)=>{"use strict";function c(a){return"symbol"==typeof a?1:null===a?2:void 0===a?3:4*(a!=a)}Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.compareValues=(a,b,d)=>{if(a!==b){let e=c(a),f=c(b);if(e===f&&0===e){if(a<b)return"desc"===d?1:-1;if(a>b)return"desc"===d?-1:1}return"desc"===d?f-e:e-f}return 0}},6349:()=>{},6431:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isDeepKey=function(a){switch(typeof a){case"number":case"symbol":return!1;case"string":return a.includes(".")||a.includes("[")||a.includes("]")}}},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},6777:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(8150),e=c(8730),f=c(1640),g=c(1706);b.isIterateeCall=function(a,b,c){return!!f.isObject(c)&&(!!("number"==typeof b&&e.isArrayLike(c)&&d.isIndex(b))&&b<c.length||"string"==typeof b&&b in c)&&g.eq(c[b],a)}},6895:(a,b,c)=>{"use strict";c(3567)},7156:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(5239),e=c(8088),f=c(7220),g=c(1289),h=c(6191),i=c(4823),j=c(1998),k=c(2603),l=c(4649),m=c(2781),n=c(2602),o=c(1268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(6713),u=c(3365),v=c(1454),w=c(7778),x=c(6143),y=c(9105),z=c(8171),A=c(6439),B=c(6133),C=c.n(B),D=c(893),E=c(2836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,1204)),"/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/page.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,4431)),"/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,6133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,9868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,9615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Users/<USER>/Projects/OpenPub/journal-transparency/src/app/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},7469:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.argumentsTag="[object Arguments]",b.arrayBufferTag="[object ArrayBuffer]",b.arrayTag="[object Array]",b.bigInt64ArrayTag="[object BigInt64Array]",b.bigUint64ArrayTag="[object BigUint64Array]",b.booleanTag="[object Boolean]",b.dataViewTag="[object DataView]",b.dateTag="[object Date]",b.errorTag="[object Error]",b.float32ArrayTag="[object Float32Array]",b.float64ArrayTag="[object Float64Array]",b.functionTag="[object Function]",b.int16ArrayTag="[object Int16Array]",b.int32ArrayTag="[object Int32Array]",b.int8ArrayTag="[object Int8Array]",b.mapTag="[object Map]",b.numberTag="[object Number]",b.objectTag="[object Object]",b.regexpTag="[object RegExp]",b.setTag="[object Set]",b.stringTag="[object String]",b.symbolTag="[object Symbol]",b.uint16ArrayTag="[object Uint16Array]",b.uint32ArrayTag="[object Uint32Array]",b.uint8ArrayTag="[object Uint8Array]",b.uint8ClampedArrayTag="[object Uint8ClampedArray]"},7509:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(5314),e=c(6431),f=c(3038),g=c(3574);b.get=function a(b,c,h){if(null==b)return h;switch(typeof c){case"string":{if(d.isUnsafeProperty(c))return h;let f=b[c];if(void 0===f)if(e.isDeepKey(c))return a(b,g.toPath(c),h);else return h;return f}case"number":case"symbol":{"number"==typeof c&&(c=f.toKey(c));let a=b[c];if(void 0===a)return h;return a}default:{if(Array.isArray(c)){var i=b,j=c,k=h;if(0===j.length)return k;let a=i;for(let b=0;b<j.length;b++){if(null==a||d.isUnsafeProperty(j[b]))return k;a=a[j[b]]}return void 0===a?k:a}if(c=Object.is(c?.valueOf(),-0)?"-0":String(c),d.isUnsafeProperty(c))return h;let a=b[c];if(void 0===a)return h;return a}}}},7586:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(6023),e=c(6021),f=c(3574);b.orderBy=function(a,b,c,g){if(null==a)return[];c=g?void 0:c,Array.isArray(a)||(a=Object.values(a)),Array.isArray(b)||(b=null==b?[null]:[b]),0===b.length&&(b=[null]),Array.isArray(c)||(c=null==c?[]:[c]),c=c.map(a=>String(a));let h=(a,b)=>{let c=a;for(let a=0;a<b.length&&null!=c;++a)c=c[b[a]];return c},i=b.map(a=>(Array.isArray(a)&&1===a.length&&(a=a[0]),null==a||"function"==typeof a||Array.isArray(a)||e.isKey(a))?a:{key:a,path:f.toPath(a)});return a.map(a=>({original:a,criteria:i.map(b=>{var c,d;return c=b,null==(d=a)||null==c?d:"object"==typeof c&&"key"in c?Object.hasOwn(d,c.key)?d[c.key]:h(d,c.path):"function"==typeof c?c(d):Array.isArray(c)?h(d,c):"object"==typeof d?d[c]:d})})).slice().sort((a,b)=>{for(let e=0;e<i.length;e++){let f=d.compareValues(a.criteria[e],b.criteria[e],c[e]);if(0!==f)return f}return 0}).map(a=>a.original)}},7617:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(2681),e=c(144),f=c(2457),g=c(415);b.iteratee=function(a){if(null==a)return d.identity;switch(typeof a){case"function":return a;case"object":if(Array.isArray(a)&&2===a.length)return g.matchesProperty(a[0],a[1]);return f.matches(a);case"string":case"symbol":case"number":return e.property(a)}}},7668:(a,b)=>{"use strict";var c="function"==typeof Symbol&&Symbol.for,d=c?Symbol.for("react.element"):60103,e=c?Symbol.for("react.portal"):60106,f=c?Symbol.for("react.fragment"):60107,g=c?Symbol.for("react.strict_mode"):60108,h=c?Symbol.for("react.profiler"):60114,i=c?Symbol.for("react.provider"):60109,j=c?Symbol.for("react.context"):60110,k=c?Symbol.for("react.async_mode"):60111,l=c?Symbol.for("react.concurrent_mode"):60111,m=c?Symbol.for("react.forward_ref"):60112,n=c?Symbol.for("react.suspense"):60113,o=(c&&Symbol.for("react.suspense_list"),c?Symbol.for("react.memo"):60115),p=c?Symbol.for("react.lazy"):60116;function q(a){if("object"==typeof a&&null!==a){var b=a.$$typeof;switch(b){case d:switch(a=a.type){case k:case l:case f:case h:case g:case n:return a;default:switch(a=a&&a.$$typeof){case j:case m:case p:case o:case i:return a;default:return b}}case e:return b}}}c&&Symbol.for("react.block"),c&&Symbol.for("react.fundamental"),c&&Symbol.for("react.responder"),c&&Symbol.for("react.scope");b.isFragment=function(a){return q(a)===f}},7766:(a,b,c)=>{a.exports=c(3084).throttle},7779:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>r0});var d={};c.r(d),c.d(d,{scaleBand:()=>dm,scaleDiverging:()=>function a(){var b=eB(gQ()(ej));return b.copy=function(){return gN(b,a())},dg.apply(b,arguments)},scaleDivergingLog:()=>function a(){var b=eJ(gQ()).domain([.1,1,10]);return b.copy=function(){return gN(b,a()).base(b.base())},dg.apply(b,arguments)},scaleDivergingPow:()=>gR,scaleDivergingSqrt:()=>gS,scaleDivergingSymlog:()=>function a(){var b=eM(gQ());return b.copy=function(){return gN(b,a()).constant(b.constant())},dg.apply(b,arguments)},scaleIdentity:()=>function a(b){var c;function d(a){return null==a||isNaN(a*=1)?c:a}return d.invert=d,d.domain=d.range=function(a){return arguments.length?(b=Array.from(a,eh),d):b.slice()},d.unknown=function(a){return arguments.length?(c=a,d):c},d.copy=function(){return a(b).unknown(c)},b=arguments.length?Array.from(b,eh):[0,1],eB(d)},scaleImplicit:()=>dk,scaleLinear:()=>function a(){var b=ep();return b.copy=function(){return en(b,a())},df.apply(b,arguments),eB(b)},scaleLog:()=>function a(){let b=eJ(eo()).domain([1,10]);return b.copy=()=>en(b,a()).base(b.base()),df.apply(b,arguments),b},scaleOrdinal:()=>dl,scalePoint:()=>dn,scalePow:()=>eR,scaleQuantile:()=>function a(){var b,c=[],d=[],e=[];function f(){var a=0,b=Math.max(1,d.length);for(e=Array(b-1);++a<b;)e[a-1]=function(a,b,c=dA){if(!(!(d=a.length)||isNaN(b*=1))){if(b<=0||d<2)return+c(a[0],0,a);if(b>=1)return+c(a[d-1],d-1,a);var d,e=(d-1)*b,f=Math.floor(e),g=+c(a[f],f,a);return g+(c(a[f+1],f+1,a)-g)*(e-f)}}(c,a/b);return g}function g(a){return null==a||isNaN(a*=1)?b:d[dC(e,a)]}return g.invertExtent=function(a){var b=d.indexOf(a);return b<0?[NaN,NaN]:[b>0?e[b-1]:c[0],b<e.length?e[b]:c[c.length-1]]},g.domain=function(a){if(!arguments.length)return c.slice();for(let b of(c=[],a))null==b||isNaN(b*=1)||c.push(b);return c.sort(dw),f()},g.range=function(a){return arguments.length?(d=Array.from(a),f()):d.slice()},g.unknown=function(a){return arguments.length?(b=a,g):b},g.quantiles=function(){return e.slice()},g.copy=function(){return a().domain(c).range(d).unknown(b)},df.apply(g,arguments)},scaleQuantize:()=>function a(){var b,c=0,d=1,e=1,f=[.5],g=[0,1];function h(a){return null!=a&&a<=a?g[dC(f,a,0,e)]:b}function i(){var a=-1;for(f=Array(e);++a<e;)f[a]=((a+1)*d-(a-e)*c)/(e+1);return h}return h.domain=function(a){return arguments.length?([c,d]=a,c*=1,d*=1,i()):[c,d]},h.range=function(a){return arguments.length?(e=(g=Array.from(a)).length-1,i()):g.slice()},h.invertExtent=function(a){var b=g.indexOf(a);return b<0?[NaN,NaN]:b<1?[c,f[0]]:b>=e?[f[e-1],d]:[f[b-1],f[b]]},h.unknown=function(a){return arguments.length&&(b=a),h},h.thresholds=function(){return f.slice()},h.copy=function(){return a().domain([c,d]).range(g).unknown(b)},df.apply(eB(h),arguments)},scaleRadial:()=>function a(){var b,c=ep(),d=[0,1],e=!1;function f(a){var d,f=Math.sign(d=c(a))*Math.sqrt(Math.abs(d));return isNaN(f)?b:e?Math.round(f):f}return f.invert=function(a){return c.invert(eT(a))},f.domain=function(a){return arguments.length?(c.domain(a),f):c.domain()},f.range=function(a){return arguments.length?(c.range((d=Array.from(a,eh)).map(eT)),f):d.slice()},f.rangeRound=function(a){return f.range(a).round(!0)},f.round=function(a){return arguments.length?(e=!!a,f):e},f.clamp=function(a){return arguments.length?(c.clamp(a),f):c.clamp()},f.unknown=function(a){return arguments.length?(b=a,f):b},f.copy=function(){return a(c.domain(),d).round(e).clamp(c.clamp()).unknown(b)},df.apply(f,arguments),eB(f)},scaleSequential:()=>function a(){var b=eB(gM()(ej));return b.copy=function(){return gN(b,a())},dg.apply(b,arguments)},scaleSequentialLog:()=>function a(){var b=eJ(gM()).domain([1,10]);return b.copy=function(){return gN(b,a()).base(b.base())},dg.apply(b,arguments)},scaleSequentialPow:()=>gO,scaleSequentialQuantile:()=>function a(){var b=[],c=ej;function d(a){if(null!=a&&!isNaN(a*=1))return c((dC(b,a,1)-1)/(b.length-1))}return d.domain=function(a){if(!arguments.length)return b.slice();for(let c of(b=[],a))null==c||isNaN(c*=1)||b.push(c);return b.sort(dw),d},d.interpolator=function(a){return arguments.length?(c=a,d):c},d.range=function(){return b.map((a,d)=>c(d/(b.length-1)))},d.quantiles=function(a){return Array.from({length:a+1},(c,d)=>(function(a,b,c){if(!(!(d=(a=Float64Array.from(function*(a,b){if(void 0===b)for(let b of a)null!=b&&(b*=1)>=b&&(yield b);else{let c=-1;for(let d of a)null!=(d=b(d,++c,a))&&(d*=1)>=d&&(yield d)}}(a,void 0))).length)||isNaN(b*=1))){if(b<=0||d<2)return eV(a);if(b>=1)return eU(a);var d,e=(d-1)*b,f=Math.floor(e),g=eU((function a(b,c,d=0,e=1/0,f){if(c=Math.floor(c),d=Math.floor(Math.max(0,d)),e=Math.floor(Math.min(b.length-1,e)),!(d<=c&&c<=e))return b;for(f=void 0===f?eW:function(a=dw){if(a===dw)return eW;if("function"!=typeof a)throw TypeError("compare is not a function");return(b,c)=>{let d=a(b,c);return d||0===d?d:(0===a(c,c))-(0===a(b,b))}}(f);e>d;){if(e-d>600){let g=e-d+1,h=c-d+1,i=Math.log(g),j=.5*Math.exp(2*i/3),k=.5*Math.sqrt(i*j*(g-j)/g)*(h-g/2<0?-1:1),l=Math.max(d,Math.floor(c-h*j/g+k)),m=Math.min(e,Math.floor(c+(g-h)*j/g+k));a(b,c,l,m,f)}let g=b[c],h=d,i=e;for(eX(b,d,c),f(b[e],g)>0&&eX(b,d,e);h<i;){for(eX(b,h,i),++h,--i;0>f(b[h],g);)++h;for(;f(b[i],g)>0;)--i}0===f(b[d],g)?eX(b,d,i):eX(b,++i,e),i<=c&&(d=i+1),c<=i&&(e=i-1)}return b})(a,f).subarray(0,f+1));return g+(eV(a.subarray(f+1))-g)*(e-f)}})(b,d/a))},d.copy=function(){return a(c).domain(b)},dg.apply(d,arguments)},scaleSequentialSqrt:()=>gP,scaleSequentialSymlog:()=>function a(){var b=eM(gM());return b.copy=function(){return gN(b,a()).constant(b.constant())},dg.apply(b,arguments)},scaleSqrt:()=>eS,scaleSymlog:()=>function a(){var b=eM(eo());return b.copy=function(){return en(b,a()).constant(b.constant())},df.apply(b,arguments)},scaleThreshold:()=>function a(){var b,c=[.5],d=[0,1],e=1;function f(a){return null!=a&&a<=a?d[dC(c,a,0,e)]:b}return f.domain=function(a){return arguments.length?(e=Math.min((c=Array.from(a)).length,d.length-1),f):c.slice()},f.range=function(a){return arguments.length?(d=Array.from(a),e=Math.min(c.length,d.length-1),f):d.slice()},f.invertExtent=function(a){var b=d.indexOf(a);return[c[b-1],c[b]]},f.unknown=function(a){return arguments.length?(b=a,f):b},f.copy=function(){return a().domain(c).range(d).unknown(b)},df.apply(f,arguments)},scaleTime:()=>gK,scaleUtc:()=>gL,tickFormat:()=>eA});var e=c(687),f=c(3210),g=c.n(f);let h=a=>{let b,c=new Set,d=(a,d)=>{let e="function"==typeof a?a(b):a;if(!Object.is(e,b)){let a=b;b=(null!=d?d:"object"!=typeof e||null===e)?e:Object.assign({},b,e),c.forEach(c=>c(b,a))}},e=()=>b,f={setState:d,getState:e,getInitialState:()=>g,subscribe:a=>(c.add(a),()=>c.delete(a))},g=b=a(d,e,f);return f},i=a=>{let b=(a=>a?h(a):h)(a),c=a=>(function(a,b=a=>a){let c=f.useSyncExternalStore(a.subscribe,()=>b(a.getState()),()=>b(a.getInitialState()));return f.useDebugValue(c),c})(b,a);return Object.assign(c,b),c},j={oaTypes:new Set,apcRange:[0,15e3],impactRange:[0,150],searchTerm:"",includeNaApc:!1},k=(a=>a?i(a):i)((a,b)=>({journals:[],filteredJournals:[],selectedJournals:new Set,filters:j,isLoading:!1,error:null,setJournals:c=>{a({journals:c}),b().applyFilters()},setFilters:c=>{a({filters:{...b().filters,...c}}),setTimeout(()=>b().applyFilters(),100)},setSelectedJournals:b=>a({selectedJournals:b}),addSelectedJournal:c=>{let d=new Set(b().selectedJournals);d.add(c),a({selectedJournals:d})},removeSelectedJournal:c=>{let d=new Set(b().selectedJournals);d.delete(c),a({selectedJournals:d})},clearSelectedJournals:()=>a({selectedJournals:new Set}),setLoading:b=>a({isLoading:b}),setError:b=>a({error:b}),applyFilters:()=>{let{journals:c,filters:d}=b();a({filteredJournals:c.filter(a=>{if(d.oaTypes.size>0&&!d.oaTypes.has(a.oa_type))return!1;if(null!=a.apc_usd){if(a.apc_usd<d.apcRange[0]||a.apc_usd>d.apcRange[1])return!1}else if(!d.includeNaApc)return!1;return!(a.impact_proxy<d.impactRange[0])&&!(a.impact_proxy>d.impactRange[1])&&(!d.searchTerm||!!a.title.toLowerCase().includes(d.searchTerm.toLowerCase()))})})}})),l=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},m=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,f.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:d,className:e="",children:g,iconNode:h,...i},j)=>(0,f.createElement)("svg",{ref:j,...n,width:b,height:b,stroke:a,strokeWidth:d?24*Number(c)/Number(b):c,className:m("lucide",e),...!g&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(i)&&{"aria-hidden":"true"},...i},[...h.map(([a,b])=>(0,f.createElement)(a,b)),...Array.isArray(g)?g:[g]])),p=(a,b)=>{let c=(0,f.forwardRef)(({className:c,...d},e)=>(0,f.createElement)(o,{ref:e,iconNode:b,className:m(`lucide-${l(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,c),...d}));return c.displayName=l(a),c},q=p("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]),r=p("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),s=()=>{let{journals:a,filteredJournals:b,filters:c,setFilters:d,clearSelectedJournals:g}=k(),h=(0,f.useMemo)(()=>{let b=new Set(a.map(a=>a.oa_type).filter(Boolean)),c=Math.max(...a.map(a=>a.apc_usd)),d=Math.max(...a.map(a=>a.impact_proxy));return{oaTypes:Array.from(b).sort(),maxApc:1e3*Math.ceil(c/1e3),maxImpact:Math.ceil(d)}},[a]),i=a=>{d({searchTerm:a}),g()};return(0,e.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-4 max-h-[800px] lg:h-full overflow-y-auto",children:[(0,e.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,e.jsx)(q,{className:"w-5 h-5 text-gray-600"}),(0,e.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Filters"}),(0,e.jsxs)("span",{className:"ml-auto text-sm text-gray-500",children:[b.length," results"]})]}),(0,e.jsxs)("div",{className:"mb-6",children:[(0,e.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search Journals"}),(0,e.jsxs)("div",{className:"relative mb-2",children:[(0,e.jsx)(r,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,e.jsx)("input",{type:"text",placeholder:"Search by journal title...",value:c.searchTerm,onChange:a=>i(a.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,e.jsxs)("div",{className:"flex flex-wrap gap-1",children:[(0,e.jsx)("button",{onClick:()=>i("Cell"),className:"px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors",children:"Cell"}),(0,e.jsx)("button",{onClick:()=>i("Nature"),className:"px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors",children:"Nature"})]})]}),(0,e.jsxs)("div",{className:"mb-6",children:[(0,e.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Open Access Type"}),(0,e.jsx)("div",{className:"space-y-2",children:h.oaTypes.map(a=>(0,e.jsxs)("label",{className:"flex items-center",children:[(0,e.jsx)("input",{type:"checkbox",checked:c.oaTypes.has(a),onChange:b=>((a,b)=>{let e=new Set(c.oaTypes);b?e.add(a):e.delete(a),d({oaTypes:e}),g()})(a,b.target.checked),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,e.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:a})]},a))})]}),(0,e.jsxs)("div",{className:"mb-6",children:[(0,e.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["APC Range (USD): $",c.apcRange[0]," - $",c.apcRange[1]]}),(0,e.jsx)("input",{type:"range",min:"0",max:h.maxApc,step:"100",value:c.apcRange[1],onChange:a=>(a=>{d({apcRange:[a[0],a[1]]}),g()})([c.apcRange[0],parseInt(a.target.value)]),className:"w-full"}),(0,e.jsx)("div",{className:"mt-2",children:(0,e.jsxs)("label",{className:"flex items-center",children:[(0,e.jsx)("input",{type:"checkbox",checked:c.includeNaApc,onChange:a=>{d({includeNaApc:a.target.checked}),g()},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,e.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Include journals with N/A APC (Note: N/A ≠ Free)"})]})})]}),(0,e.jsxs)("div",{className:"mb-6",children:[(0,e.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Min Impact: ",c.impactRange[0].toFixed(1)]}),(0,e.jsx)("input",{type:"range",min:"0",max:h.maxImpact,step:"0.1",value:c.impactRange[0],onChange:a=>(a=>{d({impactRange:[a[0],a[1]]}),g()})([parseFloat(a.target.value),c.impactRange[1]]),className:"w-full"})]}),(0,e.jsx)("button",{onClick:()=>{d({oaTypes:new Set,apcRange:[0,h.maxApc],impactRange:[0,h.maxImpact],searchTerm:"",includeNaApc:!1}),g()},className:"w-full px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500",children:"Clear All Filters"})]})};function t(){for(var a,b,c=0,d="",e=arguments.length;c<e;c++)(a=arguments[c])&&(b=function a(b){var c,d,e="";if("string"==typeof b||"number"==typeof b)e+=b;else if("object"==typeof b)if(Array.isArray(b)){var f=b.length;for(c=0;c<f;c++)b[c]&&(d=a(b[c]))&&(e&&(e+=" "),e+=d)}else for(d in b)b[d]&&(e&&(e+=" "),e+=d);return e}(a))&&(d&&(d+=" "),d+=b);return d}var u=c(7766),v=c.n(u),w=c(5664),x=c.n(w),y=a=>0===a?0:a>0?1:-1,z=a=>"number"==typeof a&&a!=+a,A=a=>"string"==typeof a&&a.indexOf("%")===a.length-1,B=a=>("number"==typeof a||a instanceof Number)&&!z(a),C=a=>B(a)||"string"==typeof a,D=0,E=a=>{var b=++D;return"".concat(a||"").concat(b)},F=function(a,b){var c,d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,e=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!B(a)&&"string"!=typeof a)return d;if(A(a)){if(null==b)return d;var f=a.indexOf("%");c=b*parseFloat(a.slice(0,f))/100}else c=+a;return z(c)&&(c=d),e&&null!=b&&c>b&&(c=b),c},G=a=>{if(!Array.isArray(a))return!1;for(var b=a.length,c={},d=0;d<b;d++)if(c[a[d]])return!0;else c[a[d]]=!0;return!1},H=(a,b)=>B(a)&&B(b)?c=>a+c*(b-a):()=>b;function I(a,b,c){if(a&&a.length)return a.find(a=>a&&("function"==typeof b?b(a):x()(a,b))===c)}var J=a=>null==a?a:"".concat(a.charAt(0).toUpperCase()).concat(a.slice(1)),K=function(a,b){for(var c=arguments.length,d=Array(c>2?c-2:0),e=2;e<c;e++)d[e-2]=arguments[e]};function L(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function M(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?L(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):L(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var N=(0,f.forwardRef)((a,b)=>{var{aspect:c,initialDimension:d={width:-1,height:-1},width:e="100%",height:g="100%",minWidth:h=0,minHeight:i,maxHeight:j,children:k,debounce:l=0,id:m,className:n,onResize:o,style:p={}}=a,q=(0,f.useRef)(null),r=(0,f.useRef)();r.current=o,(0,f.useImperativeHandle)(b,()=>q.current);var[s,u]=(0,f.useState)({containerWidth:d.width,containerHeight:d.height}),w=(0,f.useCallback)((a,b)=>{u(c=>{var d=Math.round(a),e=Math.round(b);return c.containerWidth===d&&c.containerHeight===e?c:{containerWidth:d,containerHeight:e}})},[]);(0,f.useEffect)(()=>{var a=a=>{var b,{width:c,height:d}=a[0].contentRect;w(c,d),null==(b=r.current)||b.call(r,c,d)};l>0&&(a=v()(a,l,{trailing:!0,leading:!1}));var b=new ResizeObserver(a),{width:c,height:d}=q.current.getBoundingClientRect();return w(c,d),b.observe(q.current),()=>{b.disconnect()}},[w,l]);var x=(0,f.useMemo)(()=>{var{containerWidth:a,containerHeight:b}=s;if(a<0||b<0)return null;K(A(e)||A(g),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",e,g),K(!c||c>0,"The aspect(%s) must be greater than zero.",c);var d=A(e)?a:e,l=A(g)?b:g;return c&&c>0&&(d?l=d/c:l&&(d=l*c),j&&l>j&&(l=j)),K(d>0||l>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",d,l,e,g,h,i,c),f.Children.map(k,a=>(0,f.cloneElement)(a,{width:d,height:l,style:M({width:d,height:l},a.props.style)}))},[c,k,g,j,i,h,s,e]);return f.createElement("div",{id:m?"".concat(m):void 0,className:t("recharts-responsive-container",n),style:M(M({},p),{},{width:e,height:g,minWidth:h,minHeight:i,maxHeight:j}),ref:q},f.createElement("div",{style:{width:0,height:0,overflow:"visible"}},x))});function O(a){return`Minified Redux error #${a}; visit https://redux.js.org/Errors?code=${a} for the full message or use the non-minified dev environment for full errors. `}var P="function"==typeof Symbol&&Symbol.observable||"@@observable",Q=()=>Math.random().toString(36).substring(7).split("").join("."),R={INIT:`@@redux/INIT${Q()}`,REPLACE:`@@redux/REPLACE${Q()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${Q()}`};function S(a){if("object"!=typeof a||null===a)return!1;let b=a;for(;null!==Object.getPrototypeOf(b);)b=Object.getPrototypeOf(b);return Object.getPrototypeOf(a)===b||null===Object.getPrototypeOf(a)}function T(a){let b,c=Object.keys(a),d={};for(let b=0;b<c.length;b++){let e=c[b];"function"==typeof a[e]&&(d[e]=a[e])}let e=Object.keys(d);try{Object.keys(d).forEach(a=>{let b=d[a];if(void 0===b(void 0,{type:R.INIT}))throw Error(O(12));if(void 0===b(void 0,{type:R.PROBE_UNKNOWN_ACTION()}))throw Error(O(13))})}catch(a){b=a}return function(a={},c){if(b)throw b;let f=!1,g={};for(let b=0;b<e.length;b++){let h=e[b],i=d[h],j=a[h],k=i(j,c);if(void 0===k)throw c&&c.type,Error(O(14));g[h]=k,f=f||k!==j}return(f=f||e.length!==Object.keys(a).length)?g:a}}function U(...a){return 0===a.length?a=>a:1===a.length?a[0]:a.reduce((a,b)=>(...c)=>a(b(...c)))}function V(a){return S(a)&&"type"in a&&"string"==typeof a.type}function W(a){return({dispatch:b,getState:c})=>d=>e=>"function"==typeof e?e(b,c,a):d(e)}var X=W(),Y=Symbol.for("immer-nothing"),Z=Symbol.for("immer-draftable"),$=Symbol.for("immer-state");function _(a,...b){throw Error(`[Immer] minified error nr: ${a}. Full error at: https://bit.ly/3cXEKWf`)}var aa=Object.getPrototypeOf;function ab(a){return!!a&&!!a[$]}function ac(a){return!!a&&(ae(a)||Array.isArray(a)||!!a[Z]||!!a.constructor?.[Z]||aj(a)||ak(a))}var ad=Object.prototype.constructor.toString();function ae(a){if(!a||"object"!=typeof a)return!1;let b=aa(a);if(null===b)return!0;let c=Object.hasOwnProperty.call(b,"constructor")&&b.constructor;return c===Object||"function"==typeof c&&Function.toString.call(c)===ad}function af(a,b){0===ag(a)?Reflect.ownKeys(a).forEach(c=>{b(c,a[c],a)}):a.forEach((c,d)=>b(d,c,a))}function ag(a){let b=a[$];return b?b.type_:Array.isArray(a)?1:aj(a)?2:3*!!ak(a)}function ah(a,b){return 2===ag(a)?a.has(b):Object.prototype.hasOwnProperty.call(a,b)}function ai(a,b,c){let d=ag(a);2===d?a.set(b,c):3===d?a.add(c):a[b]=c}function aj(a){return a instanceof Map}function ak(a){return a instanceof Set}function al(a){return a.copy_||a.base_}function am(a,b){if(aj(a))return new Map(a);if(ak(a))return new Set(a);if(Array.isArray(a))return Array.prototype.slice.call(a);let c=ae(a);if(!0!==b&&("class_only"!==b||c)){let b=aa(a);return null!==b&&c?{...a}:Object.assign(Object.create(b),a)}{let b=Object.getOwnPropertyDescriptors(a);delete b[$];let c=Reflect.ownKeys(b);for(let d=0;d<c.length;d++){let e=c[d],f=b[e];!1===f.writable&&(f.writable=!0,f.configurable=!0),(f.get||f.set)&&(b[e]={configurable:!0,writable:!0,enumerable:f.enumerable,value:a[e]})}return Object.create(aa(a),b)}}function an(a,b=!1){return ap(a)||ab(a)||!ac(a)||(ag(a)>1&&(a.set=a.add=a.clear=a.delete=ao),Object.freeze(a),b&&Object.entries(a).forEach(([a,b])=>an(b,!0))),a}function ao(){_(2)}function ap(a){return Object.isFrozen(a)}var aq={};function ar(a){let b=aq[a];return b||_(0,a),b}function as(a,b){b&&(ar("Patches"),a.patches_=[],a.inversePatches_=[],a.patchListener_=b)}function at(a){au(a),a.drafts_.forEach(aw),a.drafts_=null}function au(a){a===g$&&(g$=a.parent_)}function av(a){return g$={drafts_:[],parent_:g$,immer_:a,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function aw(a){let b=a[$];0===b.type_||1===b.type_?b.revoke_():b.revoked_=!0}function ax(a,b){b.unfinalizedDrafts_=b.drafts_.length;let c=b.drafts_[0];return void 0!==a&&a!==c?(c[$].modified_&&(at(b),_(4)),ac(a)&&(a=ay(b,a),b.parent_||aA(b,a)),b.patches_&&ar("Patches").generateReplacementPatches_(c[$].base_,a,b.patches_,b.inversePatches_)):a=ay(b,c,[]),at(b),b.patches_&&b.patchListener_(b.patches_,b.inversePatches_),a!==Y?a:void 0}function ay(a,b,c){if(ap(b))return b;let d=b[$];if(!d)return af(b,(e,f)=>az(a,d,b,e,f,c)),b;if(d.scope_!==a)return b;if(!d.modified_)return aA(a,d.base_,!0),d.base_;if(!d.finalized_){d.finalized_=!0,d.scope_.unfinalizedDrafts_--;let b=d.copy_,e=b,f=!1;3===d.type_&&(e=new Set(b),b.clear(),f=!0),af(e,(e,g)=>az(a,d,b,e,g,c,f)),aA(a,b,!1),c&&a.patches_&&ar("Patches").generatePatches_(d,c,a.patches_,a.inversePatches_)}return d.copy_}function az(a,b,c,d,e,f,g){if(ab(e)){let g=ay(a,e,f&&b&&3!==b.type_&&!ah(b.assigned_,d)?f.concat(d):void 0);if(ai(c,d,g),!ab(g))return;a.canAutoFreeze_=!1}else g&&c.add(e);if(ac(e)&&!ap(e)){if(!a.immer_.autoFreeze_&&a.unfinalizedDrafts_<1)return;ay(a,e),(!b||!b.scope_.parent_)&&"symbol"!=typeof d&&Object.prototype.propertyIsEnumerable.call(c,d)&&aA(a,e)}}function aA(a,b,c=!1){!a.parent_&&a.immer_.autoFreeze_&&a.canAutoFreeze_&&an(b,c)}var aB={get(a,b){if(b===$)return a;let c=al(a);if(!ah(c,b)){var d=a,e=c,f=b;let g=aE(e,f);return g?"value"in g?g.value:g.get?.call(d.draft_):void 0}let g=c[b];return a.finalized_||!ac(g)?g:g===aD(a.base_,b)?(aG(a),a.copy_[b]=aH(g,a)):g},has:(a,b)=>b in al(a),ownKeys:a=>Reflect.ownKeys(al(a)),set(a,b,c){let d=aE(al(a),b);if(d?.set)return d.set.call(a.draft_,c),!0;if(!a.modified_){let d=aD(al(a),b),e=d?.[$];if(e&&e.base_===c)return a.copy_[b]=c,a.assigned_[b]=!1,!0;if((c===d?0!==c||1/c==1/d:c!=c&&d!=d)&&(void 0!==c||ah(a.base_,b)))return!0;aG(a),aF(a)}return!!(a.copy_[b]===c&&(void 0!==c||b in a.copy_)||Number.isNaN(c)&&Number.isNaN(a.copy_[b]))||(a.copy_[b]=c,a.assigned_[b]=!0,!0)},deleteProperty:(a,b)=>(void 0!==aD(a.base_,b)||b in a.base_?(a.assigned_[b]=!1,aG(a),aF(a)):delete a.assigned_[b],a.copy_&&delete a.copy_[b],!0),getOwnPropertyDescriptor(a,b){let c=al(a),d=Reflect.getOwnPropertyDescriptor(c,b);return d?{writable:!0,configurable:1!==a.type_||"length"!==b,enumerable:d.enumerable,value:c[b]}:d},defineProperty(){_(11)},getPrototypeOf:a=>aa(a.base_),setPrototypeOf(){_(12)}},aC={};function aD(a,b){let c=a[$];return(c?al(c):a)[b]}function aE(a,b){if(!(b in a))return;let c=aa(a);for(;c;){let a=Object.getOwnPropertyDescriptor(c,b);if(a)return a;c=aa(c)}}function aF(a){!a.modified_&&(a.modified_=!0,a.parent_&&aF(a.parent_))}function aG(a){a.copy_||(a.copy_=am(a.base_,a.scope_.immer_.useStrictShallowCopy_))}function aH(a,b){let c=aj(a)?ar("MapSet").proxyMap_(a,b):ak(a)?ar("MapSet").proxySet_(a,b):function(a,b){let c=Array.isArray(a),d={type_:+!!c,scope_:b?b.scope_:g$,modified_:!1,finalized_:!1,assigned_:{},parent_:b,base_:a,draft_:null,copy_:null,revoke_:null,isManual_:!1},e=d,f=aB;c&&(e=[d],f=aC);let{revoke:g,proxy:h}=Proxy.revocable(e,f);return d.draft_=h,d.revoke_=g,h}(a,b);return(b?b.scope_:g$).drafts_.push(c),c}function aI(a){return ab(a)||_(10,a),function a(b){let c;if(!ac(b)||ap(b))return b;let d=b[$];if(d){if(!d.modified_)return d.base_;d.finalized_=!0,c=am(b,d.scope_.immer_.useStrictShallowCopy_)}else c=am(b,!0);return af(c,(b,d)=>{ai(c,b,a(d))}),d&&(d.finalized_=!1),c}(a)}af(aB,(a,b)=>{aC[a]=function(){return arguments[0]=arguments[0][0],b.apply(this,arguments)}}),aC.deleteProperty=function(a,b){return aC.set.call(this,a,b,void 0)},aC.set=function(a,b,c){return aB.set.call(this,a[0],b,c,a[0])};var aJ=new class{constructor(a){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(a,b,c)=>{let d;if("function"==typeof a&&"function"!=typeof b){let c=b;b=a;let d=this;return function(a=c,...e){return d.produce(a,a=>b.call(this,a,...e))}}if("function"!=typeof b&&_(6),void 0!==c&&"function"!=typeof c&&_(7),ac(a)){let e=av(this),f=aH(a,void 0),g=!0;try{d=b(f),g=!1}finally{g?at(e):au(e)}return as(e,c),ax(d,e)}if(a&&"object"==typeof a)_(1,a);else{if(void 0===(d=b(a))&&(d=a),d===Y&&(d=void 0),this.autoFreeze_&&an(d,!0),c){let b=[],e=[];ar("Patches").generateReplacementPatches_(a,d,b,e),c(b,e)}return d}},this.produceWithPatches=(a,b)=>{let c,d;return"function"==typeof a?(b,...c)=>this.produceWithPatches(b,b=>a(b,...c)):[this.produce(a,b,(a,b)=>{c=a,d=b}),c,d]},"boolean"==typeof a?.autoFreeze&&this.setAutoFreeze(a.autoFreeze),"boolean"==typeof a?.useStrictShallowCopy&&this.setUseStrictShallowCopy(a.useStrictShallowCopy)}createDraft(a){ac(a)||_(8),ab(a)&&(a=aI(a));let b=av(this),c=aH(a,void 0);return c[$].isManual_=!0,au(b),c}finishDraft(a,b){let c=a&&a[$];c&&c.isManual_||_(9);let{scope_:d}=c;return as(d,b),ax(void 0,d)}setAutoFreeze(a){this.autoFreeze_=a}setUseStrictShallowCopy(a){this.useStrictShallowCopy_=a}applyPatches(a,b){let c;for(c=b.length-1;c>=0;c--){let d=b[c];if(0===d.path.length&&"replace"===d.op){a=d.value;break}}c>-1&&(b=b.slice(c+1));let d=ar("Patches").applyPatches_;return ab(a)?d(a,b):this.produce(a,a=>d(a,b))}},aK=aJ.produce;aJ.produceWithPatches.bind(aJ),aJ.setAutoFreeze.bind(aJ),aJ.setUseStrictShallowCopy.bind(aJ),aJ.applyPatches.bind(aJ),aJ.createDraft.bind(aJ),aJ.finishDraft.bind(aJ);var aL="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?U:U.apply(null,arguments)};function aM(a,b){function c(...d){if(b){let c=b(...d);if(!c)throw Error(bp(0));return{type:a,payload:c.payload,..."meta"in c&&{meta:c.meta},..."error"in c&&{error:c.error}}}return{type:a,payload:d[0]}}return c.toString=()=>`${a}`,c.type=a,c.match=b=>V(b)&&b.type===a,c}"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var aN=class a extends Array{constructor(...b){super(...b),Object.setPrototypeOf(this,a.prototype)}static get[Symbol.species](){return a}concat(...a){return super.concat.apply(this,a)}prepend(...b){return 1===b.length&&Array.isArray(b[0])?new a(...b[0].concat(this)):new a(...b.concat(this))}};function aO(a){return ac(a)?aK(a,()=>{}):a}function aP(a,b,c){return a.has(b)?a.get(b):a.set(b,c(b)).get(b)}var aQ=a=>b=>{setTimeout(b,a)};function aR(a){let b,c={},d=[],e={addCase(a,b){let d="string"==typeof a?a:a.type;if(!d)throw Error(bp(28));if(d in c)throw Error(bp(29));return c[d]=b,e},addMatcher:(a,b)=>(d.push({matcher:a,reducer:b}),e),addDefaultCase:a=>(b=a,e)};return a(e),[c,d,b]}var aS=Symbol.for("rtk-slice-createasyncthunk"),aT=(a=>(a.reducer="reducer",a.reducerWithPrepare="reducerWithPrepare",a.asyncThunk="asyncThunk",a))(aT||{}),aU=function({creators:a}={}){let b=a?.asyncThunk?.[aS];return function(a){let c,{name:d,reducerPath:e=d}=a;if(!d)throw Error(bp(11));let f=("function"==typeof a.reducers?a.reducers(function(){function a(a,b){return{_reducerDefinitionType:"asyncThunk",payloadCreator:a,...b}}return a.withTypes=()=>a,{reducer:a=>Object.assign({[a.name]:(...b)=>a(...b)}[a.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(a,b)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:a,reducer:b}),asyncThunk:a}}()):a.reducers)||{},g=Object.keys(f),h={},i={},j={},k=[],l={addCase(a,b){let c="string"==typeof a?a:a.type;if(!c)throw Error(bp(12));if(c in i)throw Error(bp(13));return i[c]=b,l},addMatcher:(a,b)=>(k.push({matcher:a,reducer:b}),l),exposeAction:(a,b)=>(j[a]=b,l),exposeCaseReducer:(a,b)=>(h[a]=b,l)};function m(){let[b={},c=[],d]="function"==typeof a.extraReducers?aR(a.extraReducers):[a.extraReducers],e={...b,...i};return function(a,b){let c,[d,e,f]=aR(b);if("function"==typeof a)c=()=>aO(a());else{let b=aO(a);c=()=>b}function g(a=c(),b){let h=[d[b.type],...e.filter(({matcher:a})=>a(b)).map(({reducer:a})=>a)];return 0===h.filter(a=>!!a).length&&(h=[f]),h.reduce((a,c)=>{if(c)if(ab(a)){let d=c(a,b);return void 0===d?a:d}else{if(ac(a))return aK(a,a=>c(a,b));let d=c(a,b);if(void 0===d){if(null===a)return a;throw Error("A case reducer on a non-draftable value must not return undefined")}return d}return a},a)}return g.getInitialState=c,g}(a.initialState,a=>{for(let b in e)a.addCase(b,e[b]);for(let b of k)a.addMatcher(b.matcher,b.reducer);for(let b of c)a.addMatcher(b.matcher,b.reducer);d&&a.addDefaultCase(d)})}g.forEach(c=>{let e=f[c],g={reducerName:c,type:`${d}/${c}`,createNotation:"function"==typeof a.reducers};"asyncThunk"===e._reducerDefinitionType?function({type:a,reducerName:b},c,d,e){if(!e)throw Error(bp(18));let{payloadCreator:f,fulfilled:g,pending:h,rejected:i,settled:j,options:k}=c,l=e(a,f,k);d.exposeAction(b,l),g&&d.addCase(l.fulfilled,g),h&&d.addCase(l.pending,h),i&&d.addCase(l.rejected,i),j&&d.addMatcher(l.settled,j),d.exposeCaseReducer(b,{fulfilled:g||aV,pending:h||aV,rejected:i||aV,settled:j||aV})}(g,e,l,b):function({type:a,reducerName:b,createNotation:c},d,e){let f,g;if("reducer"in d){if(c&&"reducerWithPrepare"!==d._reducerDefinitionType)throw Error(bp(17));f=d.reducer,g=d.prepare}else f=d;e.addCase(a,f).exposeCaseReducer(b,f).exposeAction(b,g?aM(a,g):aM(a))}(g,e,l)});let n=a=>a,o=new Map,p=new WeakMap;function q(a,b){return c||(c=m()),c(a,b)}function r(){return c||(c=m()),c.getInitialState()}function s(b,c=!1){function d(a){let e=a[b];return void 0===e&&c&&(e=aP(p,d,r)),e}function e(b=n){let d=aP(o,c,()=>new WeakMap);return aP(d,b,()=>{let d={};for(let[e,f]of Object.entries(a.selectors??{}))d[e]=function(a,b,c,d){function e(f,...g){let h=b(f);return void 0===h&&d&&(h=c()),a(h,...g)}return e.unwrapped=a,e}(f,b,()=>aP(p,b,r),c);return d})}return{reducerPath:b,getSelectors:e,get selectors(){return e(d)},selectSlice:d}}let t={name:d,reducer:q,actions:j,caseReducers:h,getInitialState:r,...s(e),injectInto(a,{reducerPath:b,...c}={}){let d=b??e;return a.inject({reducerPath:d,reducer:q},c),{...t,...s(d,!0)}}};return t}}();function aV(){}var aW="listener",aX="completed",aY="cancelled",aZ=`task-${aY}`,a$=`task-${aX}`,a_=`${aW}-${aY}`,a0=`${aW}-${aX}`,a1=class{constructor(a){this.code=a,this.message=`task ${aY} (reason: ${a})`}name="TaskAbortError";message},a2=(a,b)=>{if("function"!=typeof a)throw TypeError(bp(32))},a3=()=>{},a4=(a,b=a3)=>(a.catch(b),a),a5=(a,b)=>(a.addEventListener("abort",b,{once:!0}),()=>a.removeEventListener("abort",b)),a6=(a,b)=>{let c=a.signal;c.aborted||("reason"in c||Object.defineProperty(c,"reason",{enumerable:!0,value:b,configurable:!0,writable:!0}),a.abort(b))},a7=a=>{if(a.aborted){let{reason:b}=a;throw new a1(b)}};function a8(a,b){let c=a3;return new Promise((d,e)=>{let f=()=>e(new a1(a.reason));if(a.aborted)return void f();c=a5(a,f),b.finally(()=>c()).then(d,e)}).finally(()=>{c=a3})}var a9=async(a,b)=>{try{await Promise.resolve();let b=await a();return{status:"ok",value:b}}catch(a){return{status:a instanceof a1?"cancelled":"rejected",error:a}}finally{b?.()}},ba=a=>b=>a4(a8(a,b).then(b=>(a7(a),b))),bb=a=>{let b=ba(a);return a=>b(new Promise(b=>setTimeout(b,a)))},{assign:bc}=Object,bd={},be="listenerMiddleware",bf=a=>{let{type:b,actionCreator:c,matcher:d,predicate:e,effect:f}=a;if(b)e=aM(b).match;else if(c)b=c.type,e=c.match;else if(d)e=d;else if(e);else throw Error(bp(21));return a2(f,"options.listener"),{predicate:e,type:b,effect:f}},bg=bc(a=>{let{type:b,predicate:c,effect:d}=bf(a);return{id:((a=21)=>{let b="",c=a;for(;c--;)b+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return b})(),effect:d,type:b,predicate:c,pending:new Set,unsubscribe:()=>{throw Error(bp(22))}}},{withTypes:()=>bg}),bh=(a,b)=>{let{type:c,effect:d,predicate:e}=bf(b);return Array.from(a.values()).find(a=>("string"==typeof c?a.type===c:a.predicate===e)&&a.effect===d)},bi=a=>{a.pending.forEach(a=>{a6(a,a_)})},bj=(a,b,c)=>{try{a(b,c)}catch(a){setTimeout(()=>{throw a},0)}},bk=bc(aM(`${be}/add`),{withTypes:()=>bk}),bl=aM(`${be}/removeAll`),bm=bc(aM(`${be}/remove`),{withTypes:()=>bm}),bn=(...a)=>{console.error(`${be}/error`,...a)},bo=(a={})=>{let b=new Map,{extra:c,onError:d=bn}=a;a2(d,"onError");let e=a=>(a=>(a.unsubscribe=()=>b.delete(a.id),b.set(a.id,a),b=>{a.unsubscribe(),b?.cancelActive&&bi(a)}))(bh(b,a)??bg(a));bc(e,{withTypes:()=>e});let f=a=>{let c=bh(b,a);return c&&(c.unsubscribe(),a.cancelActive&&bi(c)),!!c};bc(f,{withTypes:()=>f});let g=async(a,f,g,h)=>{let i=new AbortController,j=((a,b)=>{let c=async(c,d)=>{a7(b);let e=()=>{},f=[new Promise((b,d)=>{let f=a({predicate:c,effect:(a,c)=>{c.unsubscribe(),b([a,c.getState(),c.getOriginalState()])}});e=()=>{f(),d()}})];null!=d&&f.push(new Promise(a=>setTimeout(a,d,null)));try{let a=await a8(b,Promise.race(f));return a7(b),a}finally{e()}};return(a,b)=>a4(c(a,b))})(e,i.signal),k=[];try{a.pending.add(i),await Promise.resolve(a.effect(f,bc({},g,{getOriginalState:h,condition:(a,b)=>j(a,b).then(Boolean),take:j,delay:bb(i.signal),pause:ba(i.signal),extra:c,signal:i.signal,fork:((a,b)=>(c,d)=>{a2(c,"taskExecutor");let e=new AbortController;a5(a,()=>a6(e,a.reason));let f=a9(async()=>{a7(a),a7(e.signal);let b=await c({pause:ba(e.signal),delay:bb(e.signal),signal:e.signal});return a7(e.signal),b},()=>a6(e,a$));return d?.autoJoin&&b.push(f.catch(a3)),{result:ba(a)(f),cancel(){a6(e,aZ)}}})(i.signal,k),unsubscribe:a.unsubscribe,subscribe:()=>{b.set(a.id,a)},cancelActiveListeners:()=>{a.pending.forEach((a,b,c)=>{a!==i&&(a6(a,a_),c.delete(a))})},cancel:()=>{a6(i,a_),a.pending.delete(i)},throwIfCancelled:()=>{a7(i.signal)}})))}catch(a){a instanceof a1||bj(d,a,{raisedBy:"effect"})}finally{await Promise.all(k),a6(i,a0),a.pending.delete(i)}},h=(a=>()=>{a.forEach(bi),a.clear()})(b);return{middleware:a=>c=>i=>{let j;if(!V(i))return c(i);if(bk.match(i))return e(i.payload);if(bl.match(i))return void h();if(bm.match(i))return f(i.payload);let k=a.getState(),l=()=>{if(k===bd)throw Error(bp(23));return k};try{if(j=c(i),b.size>0){let c=a.getState();for(let e of Array.from(b.values())){let b=!1;try{b=e.predicate(i,c,k)}catch(a){b=!1,bj(d,a,{raisedBy:"predicate"})}b&&g(e,i,a,l)}}}finally{k=bd}return j},startListening:e,stopListening:f,clearListeners:h}};function bp(a){return`Minified Redux Toolkit error #${a}; visit https://redux-toolkit.js.org/Errors?code=${a} for the full message or use the non-minified dev environment for full errors. `}function bq(a,b){if(b){var c=Number.parseInt(b,10);if(!z(c))return null==a?void 0:a[c]}}Symbol.for("rtk-state-proxy-original");var br=aU({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:a=>{null==a.eventEmitter&&(a.eventEmitter=Symbol("rechartsEventEmitter"))}}}),bs=br.reducer,{createEventEmitter:bt}=br.actions;c(6895);var bu={notify(){},get:()=>[]},bv="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,bw="undefined"!=typeof navigator&&"ReactNative"===navigator.product,bx=bv||bw?f.useLayoutEffect:f.useEffect;Object.getOwnPropertyNames,Object.getOwnPropertySymbols,Object.getOwnPropertyDescriptor,Object.getPrototypeOf,Object.prototype;var by=Symbol.for("react-redux-context"),bz="undefined"!=typeof globalThis?globalThis:{},bA=function(){if(!f.createContext)return{};let a=bz[by]??=new Map,b=a.get(f.createContext);return b||(b=f.createContext(null),a.set(f.createContext,b)),b}(),bB=function(a){let{children:b,context:c,serverState:d,store:e}=a,g=f.useMemo(()=>{let a=function(a,b){let c,d=bu,e=0,f=!1;function g(){j.onStateChange&&j.onStateChange()}function h(){if(e++,!c){let b,e;c=a.subscribe(g),b=null,e=null,d={clear(){b=null,e=null},notify(){let a=b;for(;a;)a.callback(),a=a.next},get(){let a=[],c=b;for(;c;)a.push(c),c=c.next;return a},subscribe(a){let c=!0,d=e={callback:a,next:null,prev:e};return d.prev?d.prev.next=d:b=d,function(){c&&null!==b&&(c=!1,d.next?d.next.prev=d.prev:e=d.prev,d.prev?d.prev.next=d.next:b=d.next)}}}}}function i(){e--,c&&0===e&&(c(),c=void 0,d.clear(),d=bu)}let j={addNestedSub:function(a){h();let b=d.subscribe(a),c=!1;return()=>{c||(c=!0,b(),i())}},notifyNestedSubs:function(){d.notify()},handleChangeWrapper:g,isSubscribed:function(){return f},trySubscribe:function(){f||(f=!0,h())},tryUnsubscribe:function(){f&&(f=!1,i())},getListeners:()=>d};return j}(e);return{store:e,subscription:a,getServerState:d?()=>d:void 0}},[e,d]),h=f.useMemo(()=>e.getState(),[e]);return bx(()=>{let{subscription:a}=g;return a.onStateChange=a.notifyNestedSubs,a.trySubscribe(),h!==e.getState()&&a.notifyNestedSubs(),()=>{a.tryUnsubscribe(),a.onStateChange=void 0}},[g,h]),f.createElement((c||bA).Provider,{value:g},b)},bC={active:!1,index:null,dataKey:void 0,coordinate:void 0},bD=aU({name:"tooltip",initialState:{itemInteraction:{click:bC,hover:bC},axisInteraction:{click:bC,hover:bC},keyboardInteraction:bC,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(a,b){a.tooltipItemPayloads.push(b.payload)},removeTooltipEntrySettings(a,b){var c=aI(a).tooltipItemPayloads.indexOf(b.payload);c>-1&&a.tooltipItemPayloads.splice(c,1)},setTooltipSettingsState(a,b){a.settings=b.payload},setActiveMouseOverItemIndex(a,b){a.syncInteraction.active=!1,a.keyboardInteraction.active=!1,a.itemInteraction.hover.active=!0,a.itemInteraction.hover.index=b.payload.activeIndex,a.itemInteraction.hover.dataKey=b.payload.activeDataKey,a.itemInteraction.hover.coordinate=b.payload.activeCoordinate},mouseLeaveChart(a){a.itemInteraction.hover.active=!1,a.axisInteraction.hover.active=!1},mouseLeaveItem(a){a.itemInteraction.hover.active=!1},setActiveClickItemIndex(a,b){a.syncInteraction.active=!1,a.itemInteraction.click.active=!0,a.keyboardInteraction.active=!1,a.itemInteraction.click.index=b.payload.activeIndex,a.itemInteraction.click.dataKey=b.payload.activeDataKey,a.itemInteraction.click.coordinate=b.payload.activeCoordinate},setMouseOverAxisIndex(a,b){a.syncInteraction.active=!1,a.axisInteraction.hover.active=!0,a.keyboardInteraction.active=!1,a.axisInteraction.hover.index=b.payload.activeIndex,a.axisInteraction.hover.dataKey=b.payload.activeDataKey,a.axisInteraction.hover.coordinate=b.payload.activeCoordinate},setMouseClickAxisIndex(a,b){a.syncInteraction.active=!1,a.keyboardInteraction.active=!1,a.axisInteraction.click.active=!0,a.axisInteraction.click.index=b.payload.activeIndex,a.axisInteraction.click.dataKey=b.payload.activeDataKey,a.axisInteraction.click.coordinate=b.payload.activeCoordinate},setSyncInteraction(a,b){a.syncInteraction=b.payload},setKeyboardInteraction(a,b){a.keyboardInteraction.active=b.payload.active,a.keyboardInteraction.index=b.payload.activeIndex,a.keyboardInteraction.coordinate=b.payload.activeCoordinate,a.keyboardInteraction.dataKey=b.payload.activeDataKey}}}),{addTooltipEntrySettings:bE,removeTooltipEntrySettings:bF,setTooltipSettingsState:bG,setActiveMouseOverItemIndex:bH,mouseLeaveItem:bI,mouseLeaveChart:bJ,setActiveClickItemIndex:bK,setMouseOverAxisIndex:bL,setMouseClickAxisIndex:bM,setSyncInteraction:bN,setKeyboardInteraction:bO}=bD.actions,bP=bD.reducer,bQ=aU({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(a,b){if(a.chartData=b.payload,null==b.payload){a.dataStartIndex=0,a.dataEndIndex=0;return}b.payload.length>0&&a.dataEndIndex!==b.payload.length-1&&(a.dataEndIndex=b.payload.length-1)},setComputedData(a,b){a.computedData=b.payload},setDataStartEndIndexes(a,b){var{startIndex:c,endIndex:d}=b.payload;null!=c&&(a.dataStartIndex=c),null!=d&&(a.dataEndIndex=d)}}}),{setChartData:bR,setDataStartEndIndexes:bS,setComputedData:bT}=bQ.actions,bU=bQ.reducer,bV=aU({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(a,b){a.layoutType=b.payload},setChartSize(a,b){a.width=b.payload.width,a.height=b.payload.height},setMargin(a,b){a.margin.top=b.payload.top,a.margin.right=b.payload.right,a.margin.bottom=b.payload.bottom,a.margin.left=b.payload.left},setScale(a,b){a.scale=b.payload}}}),{setMargin:bW,setLayout:bX,setChartSize:bY,setScale:bZ}=bV.actions,b$=bV.reducer,b_=a=>Array.isArray(a)?a:[a],b0=0,b1=class{revision=b0;_value;_lastValue;_isEqual=b2;constructor(a,b=b2){this._value=this._lastValue=a,this._isEqual=b}get value(){return this._value}set value(a){this.value!==a&&(this._value=a,this.revision=++b0)}};function b2(a,b){return a===b}function b3(a){return a instanceof b1||console.warn("Not a valid cell! ",a),a.value}var b4=(a,b)=>!1;function b5(){return function(a,b=b2){return new b1(null,b)}(0,b4)}var b6=a=>{let b=a.collectionTag;null===b&&(b=a.collectionTag=b5()),b3(b)};Symbol();var b7=0,b8=Object.getPrototypeOf({}),b9=class{constructor(a){this.value=a,this.value=a,this.tag.value=a}proxy=new Proxy(this,ca);tag=b5();tags={};children={};collectionTag=null;id=b7++},ca={get:(a,b)=>(function(){let{value:c}=a,d=Reflect.get(c,b);if("symbol"==typeof b||b in b8)return d;if("object"==typeof d&&null!==d){var e;let c=a.children[b];return void 0===c&&(c=a.children[b]=Array.isArray(e=d)?new cb(e):new b9(e)),c.tag&&b3(c.tag),c.proxy}{let c=a.tags[b];return void 0===c&&((c=a.tags[b]=b5()).value=d),b3(c),d}})(),ownKeys:a=>(b6(a),Reflect.ownKeys(a.value)),getOwnPropertyDescriptor:(a,b)=>Reflect.getOwnPropertyDescriptor(a.value,b),has:(a,b)=>Reflect.has(a.value,b)},cb=class{constructor(a){this.value=a,this.value=a,this.tag.value=a}proxy=new Proxy([this],cc);tag=b5();tags={};children={};collectionTag=null;id=b7++},cc={get:([a],b)=>("length"===b&&b6(a),ca.get(a,b)),ownKeys:([a])=>ca.ownKeys(a),getOwnPropertyDescriptor:([a],b)=>ca.getOwnPropertyDescriptor(a,b),has:([a],b)=>ca.has(a,b)},cd="undefined"!=typeof WeakRef?WeakRef:class{constructor(a){this.value=a}deref(){return this.value}};function ce(){return{s:0,v:void 0,o:null,p:null}}function cf(a,b={}){let c,d=ce(),{resultEqualityCheck:e}=b,f=0;function g(){let b,g=d,{length:h}=arguments;for(let a=0;a<h;a++){let b=arguments[a];if("function"==typeof b||"object"==typeof b&&null!==b){let a=g.o;null===a&&(g.o=a=new WeakMap);let c=a.get(b);void 0===c?(g=ce(),a.set(b,g)):g=c}else{let a=g.p;null===a&&(g.p=a=new Map);let c=a.get(b);void 0===c?(g=ce(),a.set(b,g)):g=c}}let i=g;if(1===g.s)b=g.v;else if(b=a.apply(null,arguments),f++,e){let a=c?.deref?.()??c;null!=a&&e(a,b)&&(b=a,0!==f&&f--),c="object"==typeof b&&null!==b||"function"==typeof b?new cd(b):b}return i.s=1,i.v=b,b}return g.clearCache=()=>{d=ce(),g.resetResultsCount()},g.resultsCount=()=>f,g.resetResultsCount=()=>{f=0},g}var cg=function(a,...b){let c="function"==typeof a?{memoize:a,memoizeOptions:b}:a,d=(...a)=>{let b,d=0,e=0,f={},g=a.pop();"object"==typeof g&&(f=g,g=a.pop()),function(a,b=`expected a function, instead received ${typeof a}`){if("function"!=typeof a)throw TypeError(b)}(g,`createSelector expects an output function after the inputs, but received: [${typeof g}]`);let{memoize:h,memoizeOptions:i=[],argsMemoize:j=cf,argsMemoizeOptions:k=[],devModeChecks:l={}}={...c,...f},m=b_(i),n=b_(k),o=function(a){let b=Array.isArray(a[0])?a[0]:a;return!function(a,b="expected all items to be functions, instead received the following types: "){if(!a.every(a=>"function"==typeof a)){let c=a.map(a=>"function"==typeof a?`function ${a.name||"unnamed"}()`:typeof a).join(", ");throw TypeError(`${b}[${c}]`)}}(b,"createSelector expects all input-selectors to be functions, but received the following types: "),b}(a),p=h(function(){return d++,g.apply(null,arguments)},...m);return Object.assign(j(function(){e++;let a=function(a,b){let c=[],{length:d}=a;for(let e=0;e<d;e++)c.push(a[e].apply(null,b));return c}(o,arguments);return b=p.apply(null,a)},...n),{resultFunc:g,memoizedResultFunc:p,dependencies:o,dependencyRecomputations:()=>e,resetDependencyRecomputations:()=>{e=0},lastResult:()=>b,recomputations:()=>d,resetRecomputations:()=>{d=0},memoize:h,argsMemoize:j})};return Object.assign(d,{withTypes:()=>d}),d}(cf),ch=Object.assign((a,b=cg)=>{!function(a,b=`expected an object, instead received ${typeof a}`){if("object"!=typeof a)throw TypeError(b)}(a,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof a}`);let c=Object.keys(a);return b(c.map(b=>a[b]),(...a)=>a.reduce((a,b,d)=>(a[c[d]]=b,a),{}))},{withTypes:()=>ch}),ci=c(9733),cj=(0,f.createContext)(null),ck=a=>a,cl=()=>{var a=(0,f.useContext)(cj);return a?a.store.dispatch:ck},cm=()=>{},cn=()=>cm,co=(a,b)=>a===b;function cp(a){var b=(0,f.useContext)(cj);return(0,ci.useSyncExternalStoreWithSelector)(b?b.subscription.addNestedSub:cn,b?b.store.getState:cm,b?b.store.getState:cm,b?a:cm,co)}var cq=c(3068),cr=c.n(cq),cs=a=>a.legend.settings,ct=cg([a=>a.legend.payload,cs],(a,b)=>{var{itemSorter:c}=b,d=a.flat(1);return c?cr()(d,c):d});function cu(a,b){if((e=a.length)>1)for(var c,d,e,f=1,g=a[b[0]],h=g.length;f<e;++f)for(d=g,g=a[b[f]],c=0;c<h;++c)g[c][1]+=g[c][0]=isNaN(d[c][1])?d[c][0]:d[c][1]}function cv(a){return"object"==typeof a&&"length"in a?a:Array.from(a)}function cw(a){return function(){return a}}function cx(a){for(var b=a.length,c=Array(b);--b>=0;)c[b]=b;return c}function cy(a,b){return a[b]}function cz(a){let b=[];return b.key=a,b}function cA(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function cB(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?cA(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):cA(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}Array.prototype.slice;var cC=Math.PI/180,cD=(a,b,c,d)=>({x:a+Math.cos(-cC*d)*c,y:b+Math.sin(-cC*d)*c});function cE(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function cF(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?cE(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):cE(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function cG(a,b,c){return null==a||null==b?c:C(b)?x()(a,b,c):"function"==typeof b?b(a):c}var cH=(a,b)=>"horizontal"===a&&"xAxis"===b||"vertical"===a&&"yAxis"===b||"centric"===a&&"angleAxis"===b||"radial"===a&&"radiusAxis"===b,cI=(a,b,c,d)=>{if(d)return a.map(a=>a.coordinate);var e,f,g=a.map(a=>(a.coordinate===b&&(e=!0),a.coordinate===c&&(f=!0),a.coordinate));return e||g.push(b),f||g.push(c),g},cJ=(a,b,c)=>{if(!a)return null;var{duplicateDomain:d,type:e,range:f,scale:g,realScaleType:h,isCategorical:i,categoricalDomain:j,tickCount:k,ticks:l,niceTicks:m,axisType:n}=a;if(!g)return null;var o="scaleBand"===h&&g.bandwidth?g.bandwidth()/2:2,p=(b||c)&&"category"===e&&g.bandwidth?g.bandwidth()/o:0;return(p="angleAxis"===n&&f&&f.length>=2?2*y(f[0]-f[1])*p:p,b&&(l||m))?(l||m||[]).map((a,b)=>({coordinate:g(d?d.indexOf(a):a)+p,value:a,offset:p,index:b})).filter(a=>!z(a.coordinate)):i&&j?j.map((a,b)=>({coordinate:g(a)+p,value:a,index:b,offset:p})):g.ticks&&!c&&null!=k?g.ticks(k).map((a,b)=>({coordinate:g(a)+p,value:a,offset:p,index:b})):g.domain().map((a,b)=>({coordinate:g(a)+p,value:d?d[a]:a,index:b,offset:p}))},cK={sign:a=>{var b=a.length;if(!(b<=0))for(var c=0,d=a[0].length;c<d;++c)for(var e=0,f=0,g=0;g<b;++g){var h=z(a[g][c][1])?a[g][c][0]:a[g][c][1];h>=0?(a[g][c][0]=e,a[g][c][1]=e+h,e=a[g][c][1]):(a[g][c][0]=f,a[g][c][1]=f+h,f=a[g][c][1])}},expand:function(a,b){if((d=a.length)>0){for(var c,d,e,f=0,g=a[0].length;f<g;++f){for(e=c=0;c<d;++c)e+=a[c][f][1]||0;if(e)for(c=0;c<d;++c)a[c][f][1]/=e}cu(a,b)}},none:cu,silhouette:function(a,b){if((c=a.length)>0){for(var c,d=0,e=a[b[0]],f=e.length;d<f;++d){for(var g=0,h=0;g<c;++g)h+=a[g][d][1]||0;e[d][1]+=e[d][0]=-h/2}cu(a,b)}},wiggle:function(a,b){if((e=a.length)>0&&(d=(c=a[b[0]]).length)>0){for(var c,d,e,f=0,g=1;g<d;++g){for(var h=0,i=0,j=0;h<e;++h){for(var k=a[b[h]],l=k[g][1]||0,m=(l-(k[g-1][1]||0))/2,n=0;n<h;++n){var o=a[b[n]];m+=(o[g][1]||0)-(o[g-1][1]||0)}i+=l,j+=m*l}c[g-1][1]+=c[g-1][0]=f,i&&(f-=j/i)}c[g-1][1]+=c[g-1][0]=f,cu(a,b)}},positive:a=>{var b=a.length;if(!(b<=0))for(var c=0,d=a[0].length;c<d;++c)for(var e=0,f=0;f<b;++f){var g=z(a[f][c][1])?a[f][c][0]:a[f][c][1];g>=0?(a[f][c][0]=e,a[f][c][1]=e+g,e=a[f][c][1]):(a[f][c][0]=0,a[f][c][1]=0)}}};function cL(a){var{axis:b,ticks:c,bandSize:d,entry:e,index:f,dataKey:g}=a;if("category"===b.type){if(!b.allowDuplicatedCategory&&b.dataKey&&null!=e[b.dataKey]){var h=I(c,"value",e[b.dataKey]);if(h)return h.coordinate+d/2}return c[f]?c[f].coordinate+d/2:null}var i=cG(e,null==g?b.dataKey:g);return null==i?null:b.scale(i)}var cM=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,cN=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/;function cO(a){var{tooltipEntrySettings:b,dataKey:c,payload:d,value:e,name:f}=a;return cF(cF({},b),{},{dataKey:c,payload:d,value:e,name:f})}function cP(a,b){return a?String(a):"string"==typeof b?b:void 0}var cQ=a=>a.layout.width,cR=a=>a.layout.height,cS=a=>a.layout.scale,cT=a=>a.layout.margin,cU=cg(a=>a.cartesianAxis.xAxis,a=>Object.values(a)),cV=cg(a=>a.cartesianAxis.yAxis,a=>Object.values(a)),cW="data-recharts-item-index",cX="data-recharts-item-data-key";function cY(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function cZ(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?cY(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):cY(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var c$=cg([cQ,cR,cT,a=>a.brush.height,cU,cV,cs,a=>a.legend.size],(a,b,c,d,e,f,g,h)=>{var i=f.reduce((a,b)=>{var{orientation:c}=b;if(!b.mirror&&!b.hide){var d="number"==typeof b.width?b.width:60;return cZ(cZ({},a),{},{[c]:a[c]+d})}return a},{left:c.left||0,right:c.right||0}),j=e.reduce((a,b)=>{var{orientation:c}=b;return b.mirror||b.hide?a:cZ(cZ({},a),{},{[c]:x()(a,"".concat(c))+b.height})},{top:c.top||0,bottom:c.bottom||0}),k=cZ(cZ({},j),i),l=k.bottom;k.bottom+=d;var m=a-(k=((a,b,c)=>{if(b&&c){var{width:d,height:e}=c,{align:f,verticalAlign:g,layout:h}=b;if(("vertical"===h||"horizontal"===h&&"middle"===g)&&"center"!==f&&B(a[f]))return cF(cF({},a),{},{[f]:a[f]+(d||0)});if(("horizontal"===h||"vertical"===h&&"center"===f)&&"middle"!==g&&B(a[g]))return cF(cF({},a),{},{[g]:a[g]+(e||0)})}return a})(k,g,h)).left-k.right,n=b-k.top-k.bottom;return cZ(cZ({brushBottom:l},k),{},{width:Math.max(m,0),height:Math.max(n,0)})}),c_=cg(c$,a=>({x:a.left,y:a.top,width:a.width,height:a.height})),c0=cg(cQ,cR,(a,b)=>({x:0,y:0,width:a,height:b})),c1=(0,f.createContext)(null),c2=()=>null!=(0,f.useContext)(c1),c3=a=>a.brush,c4=cg([c3,c$,cT],(a,b,c)=>({height:a.height,x:B(a.x)?a.x:b.left,y:B(a.y)?a.y:b.top+b.height+b.brushBottom-((null==c?void 0:c.bottom)||0),width:B(a.width)?a.width:b.width})),c5=()=>{var a,b=c2(),c=cp(c_),d=cp(c4),e=null==(a=cp(c3))?void 0:a.padding;return b&&d&&e?{width:d.width-e.left-e.right,height:d.height-e.top-e.bottom,x:e.left,y:e.top}:c},c6={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},c7=()=>{var a;return null!=(a=cp(c$))?a:c6},c8=()=>cp(cQ),c9=()=>cp(cR),da={top:0,right:0,bottom:0,left:0},db=a=>a.layout.layoutType,dc=()=>cp(db),dd=c(921),de=c.n(dd);function df(a,b){switch(arguments.length){case 0:break;case 1:this.range(a);break;default:this.range(b).domain(a)}return this}function dg(a,b){switch(arguments.length){case 0:break;case 1:"function"==typeof a?this.interpolator(a):this.range(a);break;default:this.domain(a),"function"==typeof b?this.interpolator(b):this.range(b)}return this}class dh extends Map{constructor(a,b=dj){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:b}}),null!=a)for(let[b,c]of a)this.set(b,c)}get(a){return super.get(di(this,a))}has(a){return super.has(di(this,a))}set(a,b){return super.set(function({_intern:a,_key:b},c){let d=b(c);return a.has(d)?a.get(d):(a.set(d,c),c)}(this,a),b)}delete(a){return super.delete(function({_intern:a,_key:b},c){let d=b(c);return a.has(d)&&(c=a.get(d),a.delete(d)),c}(this,a))}}function di({_intern:a,_key:b},c){let d=b(c);return a.has(d)?a.get(d):c}function dj(a){return null!==a&&"object"==typeof a?a.valueOf():a}let dk=Symbol("implicit");function dl(){var a=new dh,b=[],c=[],d=dk;function e(e){let f=a.get(e);if(void 0===f){if(d!==dk)return d;a.set(e,f=b.push(e)-1)}return c[f%c.length]}return e.domain=function(c){if(!arguments.length)return b.slice();for(let d of(b=[],a=new dh,c))a.has(d)||a.set(d,b.push(d)-1);return e},e.range=function(a){return arguments.length?(c=Array.from(a),e):c.slice()},e.unknown=function(a){return arguments.length?(d=a,e):d},e.copy=function(){return dl(b,c).unknown(d)},df.apply(e,arguments),e}function dm(){var a,b,c=dl().unknown(void 0),d=c.domain,e=c.range,f=0,g=1,h=!1,i=0,j=0,k=.5;function l(){var c=d().length,l=g<f,m=l?g:f,n=l?f:g;a=(n-m)/Math.max(1,c-i+2*j),h&&(a=Math.floor(a)),m+=(n-m-a*(c-i))*k,b=a*(1-i),h&&(m=Math.round(m),b=Math.round(b));var o=(function(a,b,c){a*=1,b*=1,c=(e=arguments.length)<2?(b=a,a=0,1):e<3?1:+c;for(var d=-1,e=0|Math.max(0,Math.ceil((b-a)/c)),f=Array(e);++d<e;)f[d]=a+d*c;return f})(c).map(function(b){return m+a*b});return e(l?o.reverse():o)}return delete c.unknown,c.domain=function(a){return arguments.length?(d(a),l()):d()},c.range=function(a){return arguments.length?([f,g]=a,f*=1,g*=1,l()):[f,g]},c.rangeRound=function(a){return[f,g]=a,f*=1,g*=1,h=!0,l()},c.bandwidth=function(){return b},c.step=function(){return a},c.round=function(a){return arguments.length?(h=!!a,l()):h},c.padding=function(a){return arguments.length?(i=Math.min(1,j=+a),l()):i},c.paddingInner=function(a){return arguments.length?(i=Math.min(1,a),l()):i},c.paddingOuter=function(a){return arguments.length?(j=+a,l()):j},c.align=function(a){return arguments.length?(k=Math.max(0,Math.min(1,a)),l()):k},c.copy=function(){return dm(d(),[f,g]).round(h).paddingInner(i).paddingOuter(j).align(k)},df.apply(l(),arguments)}function dn(){return function a(b){var c=b.copy;return b.padding=b.paddingOuter,delete b.paddingInner,delete b.paddingOuter,b.copy=function(){return a(c())},b}(dm.apply(null,arguments).paddingInner(1))}let dp=Math.sqrt(50),dq=Math.sqrt(10),dr=Math.sqrt(2);function ds(a,b,c){let d,e,f,g=(b-a)/Math.max(0,c),h=Math.floor(Math.log10(g)),i=g/Math.pow(10,h),j=i>=dp?10:i>=dq?5:i>=dr?2:1;return(h<0?(d=Math.round(a*(f=Math.pow(10,-h)/j)),e=Math.round(b*f),d/f<a&&++d,e/f>b&&--e,f=-f):(d=Math.round(a/(f=Math.pow(10,h)*j)),e=Math.round(b/f),d*f<a&&++d,e*f>b&&--e),e<d&&.5<=c&&c<2)?ds(a,b,2*c):[d,e,f]}function dt(a,b,c){if(b*=1,a*=1,!((c*=1)>0))return[];if(a===b)return[a];let d=b<a,[e,f,g]=d?ds(b,a,c):ds(a,b,c);if(!(f>=e))return[];let h=f-e+1,i=Array(h);if(d)if(g<0)for(let a=0;a<h;++a)i[a]=-((f-a)/g);else for(let a=0;a<h;++a)i[a]=(f-a)*g;else if(g<0)for(let a=0;a<h;++a)i[a]=-((e+a)/g);else for(let a=0;a<h;++a)i[a]=(e+a)*g;return i}function du(a,b,c){return ds(a*=1,b*=1,c*=1)[2]}function dv(a,b,c){b*=1,a*=1,c*=1;let d=b<a,e=d?du(b,a,c):du(a,b,c);return(d?-1:1)*(e<0?-(1/e):e)}function dw(a,b){return null==a||null==b?NaN:a<b?-1:a>b?1:a>=b?0:NaN}function dx(a,b){return null==a||null==b?NaN:b<a?-1:b>a?1:b>=a?0:NaN}function dy(a){let b,c,d;function e(a,d,f=0,g=a.length){if(f<g){if(0!==b(d,d))return g;do{let b=f+g>>>1;0>c(a[b],d)?f=b+1:g=b}while(f<g)}return f}return 2!==a.length?(b=dw,c=(b,c)=>dw(a(b),c),d=(b,c)=>a(b)-c):(b=a===dw||a===dx?a:dz,c=a,d=a),{left:e,center:function(a,b,c=0,f=a.length){let g=e(a,b,c,f-1);return g>c&&d(a[g-1],b)>-d(a[g],b)?g-1:g},right:function(a,d,e=0,f=a.length){if(e<f){if(0!==b(d,d))return f;do{let b=e+f>>>1;0>=c(a[b],d)?e=b+1:f=b}while(e<f)}return e}}}function dz(){return 0}function dA(a){return null===a?NaN:+a}let dB=dy(dw),dC=dB.right;function dD(a,b,c){a.prototype=b.prototype=c,c.constructor=a}function dE(a,b){var c=Object.create(a.prototype);for(var d in b)c[d]=b[d];return c}function dF(){}dB.left,dy(dA).center;var dG="\\s*([+-]?\\d+)\\s*",dH="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",dI="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",dJ=/^#([0-9a-f]{3,8})$/,dK=RegExp(`^rgb\\(${dG},${dG},${dG}\\)$`),dL=RegExp(`^rgb\\(${dI},${dI},${dI}\\)$`),dM=RegExp(`^rgba\\(${dG},${dG},${dG},${dH}\\)$`),dN=RegExp(`^rgba\\(${dI},${dI},${dI},${dH}\\)$`),dO=RegExp(`^hsl\\(${dH},${dI},${dI}\\)$`),dP=RegExp(`^hsla\\(${dH},${dI},${dI},${dH}\\)$`),dQ={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function dR(){return this.rgb().formatHex()}function dS(){return this.rgb().formatRgb()}function dT(a){var b,c;return a=(a+"").trim().toLowerCase(),(b=dJ.exec(a))?(c=b[1].length,b=parseInt(b[1],16),6===c?dU(b):3===c?new dX(b>>8&15|b>>4&240,b>>4&15|240&b,(15&b)<<4|15&b,1):8===c?dV(b>>24&255,b>>16&255,b>>8&255,(255&b)/255):4===c?dV(b>>12&15|b>>8&240,b>>8&15|b>>4&240,b>>4&15|240&b,((15&b)<<4|15&b)/255):null):(b=dK.exec(a))?new dX(b[1],b[2],b[3],1):(b=dL.exec(a))?new dX(255*b[1]/100,255*b[2]/100,255*b[3]/100,1):(b=dM.exec(a))?dV(b[1],b[2],b[3],b[4]):(b=dN.exec(a))?dV(255*b[1]/100,255*b[2]/100,255*b[3]/100,b[4]):(b=dO.exec(a))?d1(b[1],b[2]/100,b[3]/100,1):(b=dP.exec(a))?d1(b[1],b[2]/100,b[3]/100,b[4]):dQ.hasOwnProperty(a)?dU(dQ[a]):"transparent"===a?new dX(NaN,NaN,NaN,0):null}function dU(a){return new dX(a>>16&255,a>>8&255,255&a,1)}function dV(a,b,c,d){return d<=0&&(a=b=c=NaN),new dX(a,b,c,d)}function dW(a,b,c,d){var e;return 1==arguments.length?((e=a)instanceof dF||(e=dT(e)),e)?new dX((e=e.rgb()).r,e.g,e.b,e.opacity):new dX:new dX(a,b,c,null==d?1:d)}function dX(a,b,c,d){this.r=+a,this.g=+b,this.b=+c,this.opacity=+d}function dY(){return`#${d0(this.r)}${d0(this.g)}${d0(this.b)}`}function dZ(){let a=d$(this.opacity);return`${1===a?"rgb(":"rgba("}${d_(this.r)}, ${d_(this.g)}, ${d_(this.b)}${1===a?")":`, ${a})`}`}function d$(a){return isNaN(a)?1:Math.max(0,Math.min(1,a))}function d_(a){return Math.max(0,Math.min(255,Math.round(a)||0))}function d0(a){return((a=d_(a))<16?"0":"")+a.toString(16)}function d1(a,b,c,d){return d<=0?a=b=c=NaN:c<=0||c>=1?a=b=NaN:b<=0&&(a=NaN),new d3(a,b,c,d)}function d2(a){if(a instanceof d3)return new d3(a.h,a.s,a.l,a.opacity);if(a instanceof dF||(a=dT(a)),!a)return new d3;if(a instanceof d3)return a;var b=(a=a.rgb()).r/255,c=a.g/255,d=a.b/255,e=Math.min(b,c,d),f=Math.max(b,c,d),g=NaN,h=f-e,i=(f+e)/2;return h?(g=b===f?(c-d)/h+(c<d)*6:c===f?(d-b)/h+2:(b-c)/h+4,h/=i<.5?f+e:2-f-e,g*=60):h=i>0&&i<1?0:g,new d3(g,h,i,a.opacity)}function d3(a,b,c,d){this.h=+a,this.s=+b,this.l=+c,this.opacity=+d}function d4(a){return(a=(a||0)%360)<0?a+360:a}function d5(a){return Math.max(0,Math.min(1,a||0))}function d6(a,b,c){return(a<60?b+(c-b)*a/60:a<180?c:a<240?b+(c-b)*(240-a)/60:b)*255}function d7(a,b,c,d,e){var f=a*a,g=f*a;return((1-3*a+3*f-g)*b+(4-6*f+3*g)*c+(1+3*a+3*f-3*g)*d+g*e)/6}dD(dF,dT,{copy(a){return Object.assign(new this.constructor,this,a)},displayable(){return this.rgb().displayable()},hex:dR,formatHex:dR,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return d2(this).formatHsl()},formatRgb:dS,toString:dS}),dD(dX,dW,dE(dF,{brighter(a){return a=null==a?1.4285714285714286:Math.pow(1.4285714285714286,a),new dX(this.r*a,this.g*a,this.b*a,this.opacity)},darker(a){return a=null==a?.7:Math.pow(.7,a),new dX(this.r*a,this.g*a,this.b*a,this.opacity)},rgb(){return this},clamp(){return new dX(d_(this.r),d_(this.g),d_(this.b),d$(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:dY,formatHex:dY,formatHex8:function(){return`#${d0(this.r)}${d0(this.g)}${d0(this.b)}${d0((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:dZ,toString:dZ})),dD(d3,function(a,b,c,d){return 1==arguments.length?d2(a):new d3(a,b,c,null==d?1:d)},dE(dF,{brighter(a){return a=null==a?1.4285714285714286:Math.pow(1.4285714285714286,a),new d3(this.h,this.s,this.l*a,this.opacity)},darker(a){return a=null==a?.7:Math.pow(.7,a),new d3(this.h,this.s,this.l*a,this.opacity)},rgb(){var a=this.h%360+(this.h<0)*360,b=isNaN(a)||isNaN(this.s)?0:this.s,c=this.l,d=c+(c<.5?c:1-c)*b,e=2*c-d;return new dX(d6(a>=240?a-240:a+120,e,d),d6(a,e,d),d6(a<120?a+240:a-120,e,d),this.opacity)},clamp(){return new d3(d4(this.h),d5(this.s),d5(this.l),d$(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let a=d$(this.opacity);return`${1===a?"hsl(":"hsla("}${d4(this.h)}, ${100*d5(this.s)}%, ${100*d5(this.l)}%${1===a?")":`, ${a})`}`}}));let d8=a=>()=>a;function d9(a,b){var c=b-a;return c?function(b){return a+b*c}:d8(isNaN(a)?b:a)}let ea=function a(b){var c,d=1==(c=+b)?d9:function(a,b){var d,e,f;return b-a?(d=a,e=b,d=Math.pow(d,f=c),e=Math.pow(e,f)-d,f=1/f,function(a){return Math.pow(d+a*e,f)}):d8(isNaN(a)?b:a)};function e(a,b){var c=d((a=dW(a)).r,(b=dW(b)).r),e=d(a.g,b.g),f=d(a.b,b.b),g=d9(a.opacity,b.opacity);return function(b){return a.r=c(b),a.g=e(b),a.b=f(b),a.opacity=g(b),a+""}}return e.gamma=a,e}(1);function eb(a){return function(b){var c,d,e=b.length,f=Array(e),g=Array(e),h=Array(e);for(c=0;c<e;++c)d=dW(b[c]),f[c]=d.r||0,g[c]=d.g||0,h[c]=d.b||0;return f=a(f),g=a(g),h=a(h),d.opacity=1,function(a){return d.r=f(a),d.g=g(a),d.b=h(a),d+""}}}function ec(a,b){return a*=1,b*=1,function(c){return a*(1-c)+b*c}}eb(function(a){var b=a.length-1;return function(c){var d=c<=0?c=0:c>=1?(c=1,b-1):Math.floor(c*b),e=a[d],f=a[d+1],g=d>0?a[d-1]:2*e-f,h=d<b-1?a[d+2]:2*f-e;return d7((c-d/b)*b,g,e,f,h)}}),eb(function(a){var b=a.length;return function(c){var d=Math.floor(((c%=1)<0?++c:c)*b),e=a[(d+b-1)%b],f=a[d%b],g=a[(d+1)%b],h=a[(d+2)%b];return d7((c-d/b)*b,e,f,g,h)}});var ed=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ee=RegExp(ed.source,"g");function ef(a,b){var c,d,e=typeof b;return null==b||"boolean"===e?d8(b):("number"===e?ec:"string"===e?(d=dT(b))?(b=d,ea):function(a,b){var c,d,e,f,g,h=ed.lastIndex=ee.lastIndex=0,i=-1,j=[],k=[];for(a+="",b+="";(e=ed.exec(a))&&(f=ee.exec(b));)(g=f.index)>h&&(g=b.slice(h,g),j[i]?j[i]+=g:j[++i]=g),(e=e[0])===(f=f[0])?j[i]?j[i]+=f:j[++i]=f:(j[++i]=null,k.push({i:i,x:ec(e,f)})),h=ee.lastIndex;return h<b.length&&(g=b.slice(h),j[i]?j[i]+=g:j[++i]=g),j.length<2?k[0]?(c=k[0].x,function(a){return c(a)+""}):(d=b,function(){return d}):(b=k.length,function(a){for(var c,d=0;d<b;++d)j[(c=k[d]).i]=c.x(a);return j.join("")})}:b instanceof dT?ea:b instanceof Date?function(a,b){var c=new Date;return a*=1,b*=1,function(d){return c.setTime(a*(1-d)+b*d),c}}:!ArrayBuffer.isView(c=b)||c instanceof DataView?Array.isArray(b)?function(a,b){var c,d=b?b.length:0,e=a?Math.min(d,a.length):0,f=Array(e),g=Array(d);for(c=0;c<e;++c)f[c]=ef(a[c],b[c]);for(;c<d;++c)g[c]=b[c];return function(a){for(c=0;c<e;++c)g[c]=f[c](a);return g}}:"function"!=typeof b.valueOf&&"function"!=typeof b.toString||isNaN(b)?function(a,b){var c,d={},e={};for(c in(null===a||"object"!=typeof a)&&(a={}),(null===b||"object"!=typeof b)&&(b={}),b)c in a?d[c]=ef(a[c],b[c]):e[c]=b[c];return function(a){for(c in d)e[c]=d[c](a);return e}}:ec:function(a,b){b||(b=[]);var c,d=a?Math.min(b.length,a.length):0,e=b.slice();return function(f){for(c=0;c<d;++c)e[c]=a[c]*(1-f)+b[c]*f;return e}})(a,b)}function eg(a,b){return a*=1,b*=1,function(c){return Math.round(a*(1-c)+b*c)}}function eh(a){return+a}var ei=[0,1];function ej(a){return a}function ek(a,b){var c;return(b-=a*=1)?function(c){return(c-a)/b}:(c=isNaN(b)?NaN:.5,function(){return c})}function el(a,b,c){var d=a[0],e=a[1],f=b[0],g=b[1];return e<d?(d=ek(e,d),f=c(g,f)):(d=ek(d,e),f=c(f,g)),function(a){return f(d(a))}}function em(a,b,c){var d=Math.min(a.length,b.length)-1,e=Array(d),f=Array(d),g=-1;for(a[d]<a[0]&&(a=a.slice().reverse(),b=b.slice().reverse());++g<d;)e[g]=ek(a[g],a[g+1]),f[g]=c(b[g],b[g+1]);return function(b){var c=dC(a,b,1,d)-1;return f[c](e[c](b))}}function en(a,b){return b.domain(a.domain()).range(a.range()).interpolate(a.interpolate()).clamp(a.clamp()).unknown(a.unknown())}function eo(){var a,b,c,d,e,f,g=ei,h=ei,i=ef,j=ej;function k(){var a,b,c,i=Math.min(g.length,h.length);return j!==ej&&(a=g[0],b=g[i-1],a>b&&(c=a,a=b,b=c),j=function(c){return Math.max(a,Math.min(b,c))}),d=i>2?em:el,e=f=null,l}function l(b){return null==b||isNaN(b*=1)?c:(e||(e=d(g.map(a),h,i)))(a(j(b)))}return l.invert=function(c){return j(b((f||(f=d(h,g.map(a),ec)))(c)))},l.domain=function(a){return arguments.length?(g=Array.from(a,eh),k()):g.slice()},l.range=function(a){return arguments.length?(h=Array.from(a),k()):h.slice()},l.rangeRound=function(a){return h=Array.from(a),i=eg,k()},l.clamp=function(a){return arguments.length?(j=!!a||ej,k()):j!==ej},l.interpolate=function(a){return arguments.length?(i=a,k()):i},l.unknown=function(a){return arguments.length?(c=a,l):c},function(c,d){return a=c,b=d,k()}}function ep(){return eo()(ej,ej)}var eq=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function er(a){var b;if(!(b=eq.exec(a)))throw Error("invalid format: "+a);return new es({fill:b[1],align:b[2],sign:b[3],symbol:b[4],zero:b[5],width:b[6],comma:b[7],precision:b[8]&&b[8].slice(1),trim:b[9],type:b[10]})}function es(a){this.fill=void 0===a.fill?" ":a.fill+"",this.align=void 0===a.align?">":a.align+"",this.sign=void 0===a.sign?"-":a.sign+"",this.symbol=void 0===a.symbol?"":a.symbol+"",this.zero=!!a.zero,this.width=void 0===a.width?void 0:+a.width,this.comma=!!a.comma,this.precision=void 0===a.precision?void 0:+a.precision,this.trim=!!a.trim,this.type=void 0===a.type?"":a.type+""}function et(a,b){if((c=(a=b?a.toExponential(b-1):a.toExponential()).indexOf("e"))<0)return null;var c,d=a.slice(0,c);return[d.length>1?d[0]+d.slice(2):d,+a.slice(c+1)]}function eu(a){return(a=et(Math.abs(a)))?a[1]:NaN}function ev(a,b){var c=et(a,b);if(!c)return a+"";var d=c[0],e=c[1];return e<0?"0."+Array(-e).join("0")+d:d.length>e+1?d.slice(0,e+1)+"."+d.slice(e+1):d+Array(e-d.length+2).join("0")}er.prototype=es.prototype,es.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let ew={"%":(a,b)=>(100*a).toFixed(b),b:a=>Math.round(a).toString(2),c:a=>a+"",d:function(a){return Math.abs(a=Math.round(a))>=1e21?a.toLocaleString("en").replace(/,/g,""):a.toString(10)},e:(a,b)=>a.toExponential(b),f:(a,b)=>a.toFixed(b),g:(a,b)=>a.toPrecision(b),o:a=>Math.round(a).toString(8),p:(a,b)=>ev(100*a,b),r:ev,s:function(a,b){var c=et(a,b);if(!c)return a+"";var d=c[0],e=c[1],f=e-(g_=3*Math.max(-8,Math.min(8,Math.floor(e/3))))+1,g=d.length;return f===g?d:f>g?d+Array(f-g+1).join("0"):f>0?d.slice(0,f)+"."+d.slice(f):"0."+Array(1-f).join("0")+et(a,Math.max(0,b+f-1))[0]},X:a=>Math.round(a).toString(16).toUpperCase(),x:a=>Math.round(a).toString(16)};function ex(a){return a}var ey=Array.prototype.map,ez=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function eA(a,b,c,d){var e,f,g=dv(a,b,c);switch((d=er(null==d?",f":d)).type){case"s":var h=Math.max(Math.abs(a),Math.abs(b));return null!=d.precision||isNaN(f=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(eu(h)/3)))-eu(Math.abs(g))))||(d.precision=f),g2(d,h);case"":case"e":case"g":case"p":case"r":null!=d.precision||isNaN(f=Math.max(0,eu(Math.abs(Math.max(Math.abs(a),Math.abs(b)))-(e=Math.abs(e=g)))-eu(e))+1)||(d.precision=f-("e"===d.type));break;case"f":case"%":null!=d.precision||isNaN(f=Math.max(0,-eu(Math.abs(g))))||(d.precision=f-("%"===d.type)*2)}return g1(d)}function eB(a){var b=a.domain;return a.ticks=function(a){var c=b();return dt(c[0],c[c.length-1],null==a?10:a)},a.tickFormat=function(a,c){var d=b();return eA(d[0],d[d.length-1],null==a?10:a,c)},a.nice=function(c){null==c&&(c=10);var d,e,f=b(),g=0,h=f.length-1,i=f[g],j=f[h],k=10;for(j<i&&(e=i,i=j,j=e,e=g,g=h,h=e);k-- >0;){if((e=du(i,j,c))===d)return f[g]=i,f[h]=j,b(f);if(e>0)i=Math.floor(i/e)*e,j=Math.ceil(j/e)*e;else if(e<0)i=Math.ceil(i*e)/e,j=Math.floor(j*e)/e;else break;d=e}return a},a}function eC(a,b){a=a.slice();var c,d=0,e=a.length-1,f=a[d],g=a[e];return g<f&&(c=d,d=e,e=c,c=f,f=g,g=c),a[d]=b.floor(f),a[e]=b.ceil(g),a}function eD(a){return Math.log(a)}function eE(a){return Math.exp(a)}function eF(a){return-Math.log(-a)}function eG(a){return-Math.exp(-a)}function eH(a){return isFinite(a)?+("1e"+a):a<0?0:a}function eI(a){return(b,c)=>-a(-b,c)}function eJ(a){let b,c,d=a(eD,eE),e=d.domain,f=10;function g(){var g,h;return b=(g=f)===Math.E?Math.log:10===g&&Math.log10||2===g&&Math.log2||(g=Math.log(g),a=>Math.log(a)/g),c=10===(h=f)?eH:h===Math.E?Math.exp:a=>Math.pow(h,a),e()[0]<0?(b=eI(b),c=eI(c),a(eF,eG)):a(eD,eE),d}return d.base=function(a){return arguments.length?(f=+a,g()):f},d.domain=function(a){return arguments.length?(e(a),g()):e()},d.ticks=a=>{let d,g,h=e(),i=h[0],j=h[h.length-1],k=j<i;k&&([i,j]=[j,i]);let l=b(i),m=b(j),n=null==a?10:+a,o=[];if(!(f%1)&&m-l<n){if(l=Math.floor(l),m=Math.ceil(m),i>0){for(;l<=m;++l)for(d=1;d<f;++d)if(!((g=l<0?d/c(-l):d*c(l))<i)){if(g>j)break;o.push(g)}}else for(;l<=m;++l)for(d=f-1;d>=1;--d)if(!((g=l>0?d/c(-l):d*c(l))<i)){if(g>j)break;o.push(g)}2*o.length<n&&(o=dt(i,j,n))}else o=dt(l,m,Math.min(m-l,n)).map(c);return k?o.reverse():o},d.tickFormat=(a,e)=>{if(null==a&&(a=10),null==e&&(e=10===f?"s":","),"function"!=typeof e&&(f%1||null!=(e=er(e)).precision||(e.trim=!0),e=g1(e)),a===1/0)return e;let g=Math.max(1,f*a/d.ticks().length);return a=>{let d=a/c(Math.round(b(a)));return d*f<f-.5&&(d*=f),d<=g?e(a):""}},d.nice=()=>e(eC(e(),{floor:a=>c(Math.floor(b(a))),ceil:a=>c(Math.ceil(b(a)))})),d}function eK(a){return function(b){return Math.sign(b)*Math.log1p(Math.abs(b/a))}}function eL(a){return function(b){return Math.sign(b)*Math.expm1(Math.abs(b))*a}}function eM(a){var b=1,c=a(eK(1),eL(b));return c.constant=function(c){return arguments.length?a(eK(b=+c),eL(b)):b},eB(c)}function eN(a){return function(b){return b<0?-Math.pow(-b,a):Math.pow(b,a)}}function eO(a){return a<0?-Math.sqrt(-a):Math.sqrt(a)}function eP(a){return a<0?-a*a:a*a}function eQ(a){var b=a(ej,ej),c=1;return b.exponent=function(b){return arguments.length?1==(c=+b)?a(ej,ej):.5===c?a(eO,eP):a(eN(c),eN(1/c)):c},eB(b)}function eR(){var a=eQ(eo());return a.copy=function(){return en(a,eR()).exponent(a.exponent())},df.apply(a,arguments),a}function eS(){return eR.apply(null,arguments).exponent(.5)}function eT(a){return Math.sign(a)*a*a}function eU(a,b){let c;if(void 0===b)for(let b of a)null!=b&&(c<b||void 0===c&&b>=b)&&(c=b);else{let d=-1;for(let e of a)null!=(e=b(e,++d,a))&&(c<e||void 0===c&&e>=e)&&(c=e)}return c}function eV(a,b){let c;if(void 0===b)for(let b of a)null!=b&&(c>b||void 0===c&&b>=b)&&(c=b);else{let d=-1;for(let e of a)null!=(e=b(e,++d,a))&&(c>e||void 0===c&&e>=e)&&(c=e)}return c}function eW(a,b){return(null==a||!(a>=a))-(null==b||!(b>=b))||(a<b?-1:+(a>b))}function eX(a,b,c){let d=a[b];a[b]=a[c],a[c]=d}g1=(g0=function(a){var b,c,d,e=void 0===a.grouping||void 0===a.thousands?ex:(b=ey.call(a.grouping,Number),c=a.thousands+"",function(a,d){for(var e=a.length,f=[],g=0,h=b[0],i=0;e>0&&h>0&&(i+h+1>d&&(h=Math.max(1,d-i)),f.push(a.substring(e-=h,e+h)),!((i+=h+1)>d));)h=b[g=(g+1)%b.length];return f.reverse().join(c)}),f=void 0===a.currency?"":a.currency[0]+"",g=void 0===a.currency?"":a.currency[1]+"",h=void 0===a.decimal?".":a.decimal+"",i=void 0===a.numerals?ex:(d=ey.call(a.numerals,String),function(a){return a.replace(/[0-9]/g,function(a){return d[+a]})}),j=void 0===a.percent?"%":a.percent+"",k=void 0===a.minus?"−":a.minus+"",l=void 0===a.nan?"NaN":a.nan+"";function m(a){var b=(a=er(a)).fill,c=a.align,d=a.sign,m=a.symbol,n=a.zero,o=a.width,p=a.comma,q=a.precision,r=a.trim,s=a.type;"n"===s?(p=!0,s="g"):ew[s]||(void 0===q&&(q=12),r=!0,s="g"),(n||"0"===b&&"="===c)&&(n=!0,b="0",c="=");var t="$"===m?f:"#"===m&&/[boxX]/.test(s)?"0"+s.toLowerCase():"",u="$"===m?g:/[%p]/.test(s)?j:"",v=ew[s],w=/[defgprs%]/.test(s);function x(a){var f,g,j,m=t,x=u;if("c"===s)x=v(a)+x,a="";else{var y=(a*=1)<0||1/a<0;if(a=isNaN(a)?l:v(Math.abs(a),q),r&&(a=function(a){a:for(var b,c=a.length,d=1,e=-1;d<c;++d)switch(a[d]){case".":e=b=d;break;case"0":0===e&&(e=d),b=d;break;default:if(!+a[d])break a;e>0&&(e=0)}return e>0?a.slice(0,e)+a.slice(b+1):a}(a)),y&&0==+a&&"+"!==d&&(y=!1),m=(y?"("===d?d:k:"-"===d||"("===d?"":d)+m,x=("s"===s?ez[8+g_/3]:"")+x+(y&&"("===d?")":""),w){for(f=-1,g=a.length;++f<g;)if(48>(j=a.charCodeAt(f))||j>57){x=(46===j?h+a.slice(f+1):a.slice(f))+x,a=a.slice(0,f);break}}}p&&!n&&(a=e(a,1/0));var z=m.length+a.length+x.length,A=z<o?Array(o-z+1).join(b):"";switch(p&&n&&(a=e(A+a,A.length?o-x.length:1/0),A=""),c){case"<":a=m+a+x+A;break;case"=":a=m+A+a+x;break;case"^":a=A.slice(0,z=A.length>>1)+m+a+x+A.slice(z);break;default:a=A+m+a+x}return i(a)}return q=void 0===q?6:/[gprs]/.test(s)?Math.max(1,Math.min(21,q)):Math.max(0,Math.min(20,q)),x.toString=function(){return a+""},x}return{format:m,formatPrefix:function(a,b){var c=m(((a=er(a)).type="f",a)),d=3*Math.max(-8,Math.min(8,Math.floor(eu(b)/3))),e=Math.pow(10,-d),f=ez[8+d/3];return function(a){return c(e*a)+f}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,g2=g0.formatPrefix;let eY=new Date,eZ=new Date;function e$(a,b,c,d){function e(b){return a(b=0==arguments.length?new Date:new Date(+b)),b}return e.floor=b=>(a(b=new Date(+b)),b),e.ceil=c=>(a(c=new Date(c-1)),b(c,1),a(c),c),e.round=a=>{let b=e(a),c=e.ceil(a);return a-b<c-a?b:c},e.offset=(a,c)=>(b(a=new Date(+a),null==c?1:Math.floor(c)),a),e.range=(c,d,f)=>{let g,h=[];if(c=e.ceil(c),f=null==f?1:Math.floor(f),!(c<d)||!(f>0))return h;do h.push(g=new Date(+c)),b(c,f),a(c);while(g<c&&c<d);return h},e.filter=c=>e$(b=>{if(b>=b)for(;a(b),!c(b);)b.setTime(b-1)},(a,d)=>{if(a>=a)if(d<0)for(;++d<=0;)for(;b(a,-1),!c(a););else for(;--d>=0;)for(;b(a,1),!c(a););}),c&&(e.count=(b,d)=>(eY.setTime(+b),eZ.setTime(+d),a(eY),a(eZ),Math.floor(c(eY,eZ))),e.every=a=>isFinite(a=Math.floor(a))&&a>0?a>1?e.filter(d?b=>d(b)%a==0:b=>e.count(0,b)%a==0):e:null),e}let e_=e$(()=>{},(a,b)=>{a.setTime(+a+b)},(a,b)=>b-a);e_.every=a=>isFinite(a=Math.floor(a))&&a>0?a>1?e$(b=>{b.setTime(Math.floor(b/a)*a)},(b,c)=>{b.setTime(+b+c*a)},(b,c)=>(c-b)/a):e_:null,e_.range;let e0=e$(a=>{a.setTime(a-a.getMilliseconds())},(a,b)=>{a.setTime(+a+1e3*b)},(a,b)=>(b-a)/1e3,a=>a.getUTCSeconds());e0.range;let e1=e$(a=>{a.setTime(a-a.getMilliseconds()-1e3*a.getSeconds())},(a,b)=>{a.setTime(+a+6e4*b)},(a,b)=>(b-a)/6e4,a=>a.getMinutes());e1.range;let e2=e$(a=>{a.setUTCSeconds(0,0)},(a,b)=>{a.setTime(+a+6e4*b)},(a,b)=>(b-a)/6e4,a=>a.getUTCMinutes());e2.range;let e3=e$(a=>{a.setTime(a-a.getMilliseconds()-1e3*a.getSeconds()-6e4*a.getMinutes())},(a,b)=>{a.setTime(+a+36e5*b)},(a,b)=>(b-a)/36e5,a=>a.getHours());e3.range;let e4=e$(a=>{a.setUTCMinutes(0,0,0)},(a,b)=>{a.setTime(+a+36e5*b)},(a,b)=>(b-a)/36e5,a=>a.getUTCHours());e4.range;let e5=e$(a=>a.setHours(0,0,0,0),(a,b)=>a.setDate(a.getDate()+b),(a,b)=>(b-a-(b.getTimezoneOffset()-a.getTimezoneOffset())*6e4)/864e5,a=>a.getDate()-1);e5.range;let e6=e$(a=>{a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+b)},(a,b)=>(b-a)/864e5,a=>a.getUTCDate()-1);e6.range;let e7=e$(a=>{a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+b)},(a,b)=>(b-a)/864e5,a=>Math.floor(a/864e5));function e8(a){return e$(b=>{b.setDate(b.getDate()-(b.getDay()+7-a)%7),b.setHours(0,0,0,0)},(a,b)=>{a.setDate(a.getDate()+7*b)},(a,b)=>(b-a-(b.getTimezoneOffset()-a.getTimezoneOffset())*6e4)/6048e5)}e7.range;let e9=e8(0),fa=e8(1),fb=e8(2),fc=e8(3),fd=e8(4),fe=e8(5),ff=e8(6);function fg(a){return e$(b=>{b.setUTCDate(b.getUTCDate()-(b.getUTCDay()+7-a)%7),b.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+7*b)},(a,b)=>(b-a)/6048e5)}e9.range,fa.range,fb.range,fc.range,fd.range,fe.range,ff.range;let fh=fg(0),fi=fg(1),fj=fg(2),fk=fg(3),fl=fg(4),fm=fg(5),fn=fg(6);fh.range,fi.range,fj.range,fk.range,fl.range,fm.range,fn.range;let fo=e$(a=>{a.setDate(1),a.setHours(0,0,0,0)},(a,b)=>{a.setMonth(a.getMonth()+b)},(a,b)=>b.getMonth()-a.getMonth()+(b.getFullYear()-a.getFullYear())*12,a=>a.getMonth());fo.range;let fp=e$(a=>{a.setUTCDate(1),a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCMonth(a.getUTCMonth()+b)},(a,b)=>b.getUTCMonth()-a.getUTCMonth()+(b.getUTCFullYear()-a.getUTCFullYear())*12,a=>a.getUTCMonth());fp.range;let fq=e$(a=>{a.setMonth(0,1),a.setHours(0,0,0,0)},(a,b)=>{a.setFullYear(a.getFullYear()+b)},(a,b)=>b.getFullYear()-a.getFullYear(),a=>a.getFullYear());fq.every=a=>isFinite(a=Math.floor(a))&&a>0?e$(b=>{b.setFullYear(Math.floor(b.getFullYear()/a)*a),b.setMonth(0,1),b.setHours(0,0,0,0)},(b,c)=>{b.setFullYear(b.getFullYear()+c*a)}):null,fq.range;let fr=e$(a=>{a.setUTCMonth(0,1),a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCFullYear(a.getUTCFullYear()+b)},(a,b)=>b.getUTCFullYear()-a.getUTCFullYear(),a=>a.getUTCFullYear());function fs(a,b,c,d,e,f){let g=[[e0,1,1e3],[e0,5,5e3],[e0,15,15e3],[e0,30,3e4],[f,1,6e4],[f,5,3e5],[f,15,9e5],[f,30,18e5],[e,1,36e5],[e,3,108e5],[e,6,216e5],[e,12,432e5],[d,1,864e5],[d,2,1728e5],[c,1,6048e5],[b,1,2592e6],[b,3,7776e6],[a,1,31536e6]];function h(b,c,d){let e=Math.abs(c-b)/d,f=dy(([,,a])=>a).right(g,e);if(f===g.length)return a.every(dv(b/31536e6,c/31536e6,d));if(0===f)return e_.every(Math.max(dv(b,c,d),1));let[h,i]=g[e/g[f-1][2]<g[f][2]/e?f-1:f];return h.every(i)}return[function(a,b,c){let d=b<a;d&&([a,b]=[b,a]);let e=c&&"function"==typeof c.range?c:h(a,b,c),f=e?e.range(a,+b+1):[];return d?f.reverse():f},h]}fr.every=a=>isFinite(a=Math.floor(a))&&a>0?e$(b=>{b.setUTCFullYear(Math.floor(b.getUTCFullYear()/a)*a),b.setUTCMonth(0,1),b.setUTCHours(0,0,0,0)},(b,c)=>{b.setUTCFullYear(b.getUTCFullYear()+c*a)}):null,fr.range;let[ft,fu]=fs(fr,fp,fh,e7,e4,e2),[fv,fw]=fs(fq,fo,e9,e5,e3,e1);function fx(a){if(0<=a.y&&a.y<100){var b=new Date(-1,a.m,a.d,a.H,a.M,a.S,a.L);return b.setFullYear(a.y),b}return new Date(a.y,a.m,a.d,a.H,a.M,a.S,a.L)}function fy(a){if(0<=a.y&&a.y<100){var b=new Date(Date.UTC(-1,a.m,a.d,a.H,a.M,a.S,a.L));return b.setUTCFullYear(a.y),b}return new Date(Date.UTC(a.y,a.m,a.d,a.H,a.M,a.S,a.L))}function fz(a,b,c){return{y:a,m:b,d:c,H:0,M:0,S:0,L:0}}var fA={"-":"",_:" ",0:"0"},fB=/^\s*\d+/,fC=/^%/,fD=/[\\^$*+?|[\]().{}]/g;function fE(a,b,c){var d=a<0?"-":"",e=(d?-a:a)+"",f=e.length;return d+(f<c?Array(c-f+1).join(b)+e:e)}function fF(a){return a.replace(fD,"\\$&")}function fG(a){return RegExp("^(?:"+a.map(fF).join("|")+")","i")}function fH(a){return new Map(a.map((a,b)=>[a.toLowerCase(),b]))}function fI(a,b,c){var d=fB.exec(b.slice(c,c+1));return d?(a.w=+d[0],c+d[0].length):-1}function fJ(a,b,c){var d=fB.exec(b.slice(c,c+1));return d?(a.u=+d[0],c+d[0].length):-1}function fK(a,b,c){var d=fB.exec(b.slice(c,c+2));return d?(a.U=+d[0],c+d[0].length):-1}function fL(a,b,c){var d=fB.exec(b.slice(c,c+2));return d?(a.V=+d[0],c+d[0].length):-1}function fM(a,b,c){var d=fB.exec(b.slice(c,c+2));return d?(a.W=+d[0],c+d[0].length):-1}function fN(a,b,c){var d=fB.exec(b.slice(c,c+4));return d?(a.y=+d[0],c+d[0].length):-1}function fO(a,b,c){var d=fB.exec(b.slice(c,c+2));return d?(a.y=+d[0]+(+d[0]>68?1900:2e3),c+d[0].length):-1}function fP(a,b,c){var d=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(b.slice(c,c+6));return d?(a.Z=d[1]?0:-(d[2]+(d[3]||"00")),c+d[0].length):-1}function fQ(a,b,c){var d=fB.exec(b.slice(c,c+1));return d?(a.q=3*d[0]-3,c+d[0].length):-1}function fR(a,b,c){var d=fB.exec(b.slice(c,c+2));return d?(a.m=d[0]-1,c+d[0].length):-1}function fS(a,b,c){var d=fB.exec(b.slice(c,c+2));return d?(a.d=+d[0],c+d[0].length):-1}function fT(a,b,c){var d=fB.exec(b.slice(c,c+3));return d?(a.m=0,a.d=+d[0],c+d[0].length):-1}function fU(a,b,c){var d=fB.exec(b.slice(c,c+2));return d?(a.H=+d[0],c+d[0].length):-1}function fV(a,b,c){var d=fB.exec(b.slice(c,c+2));return d?(a.M=+d[0],c+d[0].length):-1}function fW(a,b,c){var d=fB.exec(b.slice(c,c+2));return d?(a.S=+d[0],c+d[0].length):-1}function fX(a,b,c){var d=fB.exec(b.slice(c,c+3));return d?(a.L=+d[0],c+d[0].length):-1}function fY(a,b,c){var d=fB.exec(b.slice(c,c+6));return d?(a.L=Math.floor(d[0]/1e3),c+d[0].length):-1}function fZ(a,b,c){var d=fC.exec(b.slice(c,c+1));return d?c+d[0].length:-1}function f$(a,b,c){var d=fB.exec(b.slice(c));return d?(a.Q=+d[0],c+d[0].length):-1}function f_(a,b,c){var d=fB.exec(b.slice(c));return d?(a.s=+d[0],c+d[0].length):-1}function f0(a,b){return fE(a.getDate(),b,2)}function f1(a,b){return fE(a.getHours(),b,2)}function f2(a,b){return fE(a.getHours()%12||12,b,2)}function f3(a,b){return fE(1+e5.count(fq(a),a),b,3)}function f4(a,b){return fE(a.getMilliseconds(),b,3)}function f5(a,b){return f4(a,b)+"000"}function f6(a,b){return fE(a.getMonth()+1,b,2)}function f7(a,b){return fE(a.getMinutes(),b,2)}function f8(a,b){return fE(a.getSeconds(),b,2)}function f9(a){var b=a.getDay();return 0===b?7:b}function ga(a,b){return fE(e9.count(fq(a)-1,a),b,2)}function gb(a){var b=a.getDay();return b>=4||0===b?fd(a):fd.ceil(a)}function gc(a,b){return a=gb(a),fE(fd.count(fq(a),a)+(4===fq(a).getDay()),b,2)}function gd(a){return a.getDay()}function ge(a,b){return fE(fa.count(fq(a)-1,a),b,2)}function gf(a,b){return fE(a.getFullYear()%100,b,2)}function gg(a,b){return fE((a=gb(a)).getFullYear()%100,b,2)}function gh(a,b){return fE(a.getFullYear()%1e4,b,4)}function gi(a,b){var c=a.getDay();return fE((a=c>=4||0===c?fd(a):fd.ceil(a)).getFullYear()%1e4,b,4)}function gj(a){var b=a.getTimezoneOffset();return(b>0?"-":(b*=-1,"+"))+fE(b/60|0,"0",2)+fE(b%60,"0",2)}function gk(a,b){return fE(a.getUTCDate(),b,2)}function gl(a,b){return fE(a.getUTCHours(),b,2)}function gm(a,b){return fE(a.getUTCHours()%12||12,b,2)}function gn(a,b){return fE(1+e6.count(fr(a),a),b,3)}function go(a,b){return fE(a.getUTCMilliseconds(),b,3)}function gp(a,b){return go(a,b)+"000"}function gq(a,b){return fE(a.getUTCMonth()+1,b,2)}function gr(a,b){return fE(a.getUTCMinutes(),b,2)}function gs(a,b){return fE(a.getUTCSeconds(),b,2)}function gt(a){var b=a.getUTCDay();return 0===b?7:b}function gu(a,b){return fE(fh.count(fr(a)-1,a),b,2)}function gv(a){var b=a.getUTCDay();return b>=4||0===b?fl(a):fl.ceil(a)}function gw(a,b){return a=gv(a),fE(fl.count(fr(a),a)+(4===fr(a).getUTCDay()),b,2)}function gx(a){return a.getUTCDay()}function gy(a,b){return fE(fi.count(fr(a)-1,a),b,2)}function gz(a,b){return fE(a.getUTCFullYear()%100,b,2)}function gA(a,b){return fE((a=gv(a)).getUTCFullYear()%100,b,2)}function gB(a,b){return fE(a.getUTCFullYear()%1e4,b,4)}function gC(a,b){var c=a.getUTCDay();return fE((a=c>=4||0===c?fl(a):fl.ceil(a)).getUTCFullYear()%1e4,b,4)}function gD(){return"+0000"}function gE(){return"%"}function gF(a){return+a}function gG(a){return Math.floor(a/1e3)}function gH(a){return new Date(a)}function gI(a){return a instanceof Date?+a:+new Date(+a)}function gJ(a,b,c,d,e,f,g,h,i,j){var k=ep(),l=k.invert,m=k.domain,n=j(".%L"),o=j(":%S"),p=j("%I:%M"),q=j("%I %p"),r=j("%a %d"),s=j("%b %d"),t=j("%B"),u=j("%Y");function v(a){return(i(a)<a?n:h(a)<a?o:g(a)<a?p:f(a)<a?q:d(a)<a?e(a)<a?r:s:c(a)<a?t:u)(a)}return k.invert=function(a){return new Date(l(a))},k.domain=function(a){return arguments.length?m(Array.from(a,gI)):m().map(gH)},k.ticks=function(b){var c=m();return a(c[0],c[c.length-1],null==b?10:b)},k.tickFormat=function(a,b){return null==b?v:j(b)},k.nice=function(a){var c=m();return a&&"function"==typeof a.range||(a=b(c[0],c[c.length-1],null==a?10:a)),a?m(eC(c,a)):k},k.copy=function(){return en(k,gJ(a,b,c,d,e,f,g,h,i,j))},k}function gK(){return df.apply(gJ(fv,fw,fq,fo,e9,e5,e3,e1,e0,g4).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function gL(){return df.apply(gJ(ft,fu,fr,fp,fh,e6,e4,e2,e0,g5).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function gM(){var a,b,c,d,e,f=0,g=1,h=ej,i=!1;function j(b){return null==b||isNaN(b*=1)?e:h(0===c?.5:(b=(d(b)-a)*c,i?Math.max(0,Math.min(1,b)):b))}function k(a){return function(b){var c,d;return arguments.length?([c,d]=b,h=a(c,d),j):[h(0),h(1)]}}return j.domain=function(e){return arguments.length?([f,g]=e,a=d(f*=1),b=d(g*=1),c=a===b?0:1/(b-a),j):[f,g]},j.clamp=function(a){return arguments.length?(i=!!a,j):i},j.interpolator=function(a){return arguments.length?(h=a,j):h},j.range=k(ef),j.rangeRound=k(eg),j.unknown=function(a){return arguments.length?(e=a,j):e},function(e){return d=e,a=e(f),b=e(g),c=a===b?0:1/(b-a),j}}function gN(a,b){return b.domain(a.domain()).interpolator(a.interpolator()).clamp(a.clamp()).unknown(a.unknown())}function gO(){var a=eQ(gM());return a.copy=function(){return gN(a,gO()).exponent(a.exponent())},dg.apply(a,arguments)}function gP(){return gO.apply(null,arguments).exponent(.5)}function gQ(){var a,b,c,d,e,f,g,h=0,i=.5,j=1,k=1,l=ej,m=!1;function n(a){return isNaN(a*=1)?g:(a=.5+((a=+f(a))-b)*(k*a<k*b?d:e),l(m?Math.max(0,Math.min(1,a)):a))}function o(a){return function(b){var c,d,e;return arguments.length?([c,d,e]=b,l=function(a,b){void 0===b&&(b=a,a=ef);for(var c=0,d=b.length-1,e=b[0],f=Array(d<0?0:d);c<d;)f[c]=a(e,e=b[++c]);return function(a){var b=Math.max(0,Math.min(d-1,Math.floor(a*=d)));return f[b](a-b)}}(a,[c,d,e]),n):[l(0),l(.5),l(1)]}}return n.domain=function(g){return arguments.length?([h,i,j]=g,a=f(h*=1),b=f(i*=1),c=f(j*=1),d=a===b?0:.5/(b-a),e=b===c?0:.5/(c-b),k=b<a?-1:1,n):[h,i,j]},n.clamp=function(a){return arguments.length?(m=!!a,n):m},n.interpolator=function(a){return arguments.length?(l=a,n):l},n.range=o(ef),n.rangeRound=o(eg),n.unknown=function(a){return arguments.length?(g=a,n):g},function(g){return f=g,a=g(h),b=g(i),c=g(j),d=a===b?0:.5/(b-a),e=b===c?0:.5/(c-b),k=b<a?-1:1,n}}function gR(){var a=eQ(gQ());return a.copy=function(){return gN(a,gR()).exponent(a.exponent())},dg.apply(a,arguments)}function gS(){return gR.apply(null,arguments).exponent(.5)}g4=(g3=function(a){var b=a.dateTime,c=a.date,d=a.time,e=a.periods,f=a.days,g=a.shortDays,h=a.months,i=a.shortMonths,j=fG(e),k=fH(e),l=fG(f),m=fH(f),n=fG(g),o=fH(g),p=fG(h),q=fH(h),r=fG(i),s=fH(i),t={a:function(a){return g[a.getDay()]},A:function(a){return f[a.getDay()]},b:function(a){return i[a.getMonth()]},B:function(a){return h[a.getMonth()]},c:null,d:f0,e:f0,f:f5,g:gg,G:gi,H:f1,I:f2,j:f3,L:f4,m:f6,M:f7,p:function(a){return e[+(a.getHours()>=12)]},q:function(a){return 1+~~(a.getMonth()/3)},Q:gF,s:gG,S:f8,u:f9,U:ga,V:gc,w:gd,W:ge,x:null,X:null,y:gf,Y:gh,Z:gj,"%":gE},u={a:function(a){return g[a.getUTCDay()]},A:function(a){return f[a.getUTCDay()]},b:function(a){return i[a.getUTCMonth()]},B:function(a){return h[a.getUTCMonth()]},c:null,d:gk,e:gk,f:gp,g:gA,G:gC,H:gl,I:gm,j:gn,L:go,m:gq,M:gr,p:function(a){return e[+(a.getUTCHours()>=12)]},q:function(a){return 1+~~(a.getUTCMonth()/3)},Q:gF,s:gG,S:gs,u:gt,U:gu,V:gw,w:gx,W:gy,x:null,X:null,y:gz,Y:gB,Z:gD,"%":gE},v={a:function(a,b,c){var d=n.exec(b.slice(c));return d?(a.w=o.get(d[0].toLowerCase()),c+d[0].length):-1},A:function(a,b,c){var d=l.exec(b.slice(c));return d?(a.w=m.get(d[0].toLowerCase()),c+d[0].length):-1},b:function(a,b,c){var d=r.exec(b.slice(c));return d?(a.m=s.get(d[0].toLowerCase()),c+d[0].length):-1},B:function(a,b,c){var d=p.exec(b.slice(c));return d?(a.m=q.get(d[0].toLowerCase()),c+d[0].length):-1},c:function(a,c,d){return y(a,b,c,d)},d:fS,e:fS,f:fY,g:fO,G:fN,H:fU,I:fU,j:fT,L:fX,m:fR,M:fV,p:function(a,b,c){var d=j.exec(b.slice(c));return d?(a.p=k.get(d[0].toLowerCase()),c+d[0].length):-1},q:fQ,Q:f$,s:f_,S:fW,u:fJ,U:fK,V:fL,w:fI,W:fM,x:function(a,b,d){return y(a,c,b,d)},X:function(a,b,c){return y(a,d,b,c)},y:fO,Y:fN,Z:fP,"%":fZ};function w(a,b){return function(c){var d,e,f,g=[],h=-1,i=0,j=a.length;for(c instanceof Date||(c=new Date(+c));++h<j;)37===a.charCodeAt(h)&&(g.push(a.slice(i,h)),null!=(e=fA[d=a.charAt(++h)])?d=a.charAt(++h):e="e"===d?" ":"0",(f=b[d])&&(d=f(c,e)),g.push(d),i=h+1);return g.push(a.slice(i,h)),g.join("")}}function x(a,b){return function(c){var d,e,f=fz(1900,void 0,1);if(y(f,a,c+="",0)!=c.length)return null;if("Q"in f)return new Date(f.Q);if("s"in f)return new Date(1e3*f.s+("L"in f?f.L:0));if(!b||"Z"in f||(f.Z=0),"p"in f&&(f.H=f.H%12+12*f.p),void 0===f.m&&(f.m="q"in f?f.q:0),"V"in f){if(f.V<1||f.V>53)return null;"w"in f||(f.w=1),"Z"in f?(d=(e=(d=fy(fz(f.y,0,1))).getUTCDay())>4||0===e?fi.ceil(d):fi(d),d=e6.offset(d,(f.V-1)*7),f.y=d.getUTCFullYear(),f.m=d.getUTCMonth(),f.d=d.getUTCDate()+(f.w+6)%7):(d=(e=(d=fx(fz(f.y,0,1))).getDay())>4||0===e?fa.ceil(d):fa(d),d=e5.offset(d,(f.V-1)*7),f.y=d.getFullYear(),f.m=d.getMonth(),f.d=d.getDate()+(f.w+6)%7)}else("W"in f||"U"in f)&&("w"in f||(f.w="u"in f?f.u%7:+("W"in f)),e="Z"in f?fy(fz(f.y,0,1)).getUTCDay():fx(fz(f.y,0,1)).getDay(),f.m=0,f.d="W"in f?(f.w+6)%7+7*f.W-(e+5)%7:f.w+7*f.U-(e+6)%7);return"Z"in f?(f.H+=f.Z/100|0,f.M+=f.Z%100,fy(f)):fx(f)}}function y(a,b,c,d){for(var e,f,g=0,h=b.length,i=c.length;g<h;){if(d>=i)return -1;if(37===(e=b.charCodeAt(g++))){if(!(f=v[(e=b.charAt(g++))in fA?b.charAt(g++):e])||(d=f(a,c,d))<0)return -1}else if(e!=c.charCodeAt(d++))return -1}return d}return t.x=w(c,t),t.X=w(d,t),t.c=w(b,t),u.x=w(c,u),u.X=w(d,u),u.c=w(b,u),{format:function(a){var b=w(a+="",t);return b.toString=function(){return a},b},parse:function(a){var b=x(a+="",!1);return b.toString=function(){return a},b},utcFormat:function(a){var b=w(a+="",u);return b.toString=function(){return a},b},utcParse:function(a){var b=x(a+="",!0);return b.toString=function(){return a},b}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,g3.parse,g5=g3.utcFormat,g3.utcParse;var gT=a=>a.chartData,gU=cg([gT],a=>{var b=null!=a.chartData?a.chartData.length-1:0;return{chartData:a.chartData,computedData:a.computedData,dataEndIndex:b,dataStartIndex:0}}),gV=(a,b,c,d)=>d?gU(a):gT(a);function gW(a){return Number.isFinite(a)}function gX(a){return"number"==typeof a&&a>0&&Number.isFinite(a)}function gY(a){if(Array.isArray(a)&&2===a.length){var[b,c]=a;if(gW(b)&&gW(c))return!0}return!1}function gZ(a,b,c){return c?a:[Math.min(a[0],b[0]),Math.max(a[1],b[1])]}var g$,g_,g0,g1,g2,g3,g4,g5,g6,g7,g8=!0,g9="[DecimalError] ",ha=g9+"Invalid argument: ",hb=g9+"Exponent out of range: ",hc=Math.floor,hd=Math.pow,he=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,hf=hc(1286742750677284.5),hg={};function hh(a,b){var c,d,e,f,g,h,i,j,k=a.constructor,l=k.precision;if(!a.s||!b.s)return b.s||(b=new k(a)),g8?hr(b,l):b;if(i=a.d,j=b.d,g=a.e,e=b.e,i=i.slice(),f=g-e){for(f<0?(d=i,f=-f,h=j.length):(d=j,e=g,h=i.length),f>(h=(g=Math.ceil(l/7))>h?g+1:h+1)&&(f=h,d.length=1),d.reverse();f--;)d.push(0);d.reverse()}for((h=i.length)-(f=j.length)<0&&(f=h,d=j,j=i,i=d),c=0;f;)c=(i[--f]=i[f]+j[f]+c)/1e7|0,i[f]%=1e7;for(c&&(i.unshift(c),++e),h=i.length;0==i[--h];)i.pop();return b.d=i,b.e=e,g8?hr(b,l):b}function hi(a,b,c){if(a!==~~a||a<b||a>c)throw Error(ha+a)}function hj(a){var b,c,d,e=a.length-1,f="",g=a[0];if(e>0){for(f+=g,b=1;b<e;b++)(c=7-(d=a[b]+"").length)&&(f+=ho(c)),f+=d;(c=7-(d=(g=a[b])+"").length)&&(f+=ho(c))}else if(0===g)return"0";for(;g%10==0;)g/=10;return f+g}hg.absoluteValue=hg.abs=function(){var a=new this.constructor(this);return a.s&&(a.s=1),a},hg.comparedTo=hg.cmp=function(a){var b,c,d,e;if(a=new this.constructor(a),this.s!==a.s)return this.s||-a.s;if(this.e!==a.e)return this.e>a.e^this.s<0?1:-1;for(b=0,c=(d=this.d.length)<(e=a.d.length)?d:e;b<c;++b)if(this.d[b]!==a.d[b])return this.d[b]>a.d[b]^this.s<0?1:-1;return d===e?0:d>e^this.s<0?1:-1},hg.decimalPlaces=hg.dp=function(){var a=this.d.length-1,b=(a-this.e)*7;if(a=this.d[a])for(;a%10==0;a/=10)b--;return b<0?0:b},hg.dividedBy=hg.div=function(a){return hk(this,new this.constructor(a))},hg.dividedToIntegerBy=hg.idiv=function(a){var b=this.constructor;return hr(hk(this,new b(a),0,1),b.precision)},hg.equals=hg.eq=function(a){return!this.cmp(a)},hg.exponent=function(){return hm(this)},hg.greaterThan=hg.gt=function(a){return this.cmp(a)>0},hg.greaterThanOrEqualTo=hg.gte=function(a){return this.cmp(a)>=0},hg.isInteger=hg.isint=function(){return this.e>this.d.length-2},hg.isNegative=hg.isneg=function(){return this.s<0},hg.isPositive=hg.ispos=function(){return this.s>0},hg.isZero=function(){return 0===this.s},hg.lessThan=hg.lt=function(a){return 0>this.cmp(a)},hg.lessThanOrEqualTo=hg.lte=function(a){return 1>this.cmp(a)},hg.logarithm=hg.log=function(a){var b,c=this.constructor,d=c.precision,e=d+5;if(void 0===a)a=new c(10);else if((a=new c(a)).s<1||a.eq(g7))throw Error(g9+"NaN");if(this.s<1)throw Error(g9+(this.s?"NaN":"-Infinity"));return this.eq(g7)?new c(0):(g8=!1,b=hk(hp(this,e),hp(a,e),e),g8=!0,hr(b,d))},hg.minus=hg.sub=function(a){return a=new this.constructor(a),this.s==a.s?hs(this,a):hh(this,(a.s=-a.s,a))},hg.modulo=hg.mod=function(a){var b,c=this.constructor,d=c.precision;if(!(a=new c(a)).s)throw Error(g9+"NaN");return this.s?(g8=!1,b=hk(this,a,0,1).times(a),g8=!0,this.minus(b)):hr(new c(this),d)},hg.naturalExponential=hg.exp=function(){return hl(this)},hg.naturalLogarithm=hg.ln=function(){return hp(this)},hg.negated=hg.neg=function(){var a=new this.constructor(this);return a.s=-a.s||0,a},hg.plus=hg.add=function(a){return a=new this.constructor(a),this.s==a.s?hh(this,a):hs(this,(a.s=-a.s,a))},hg.precision=hg.sd=function(a){var b,c,d;if(void 0!==a&&!!a!==a&&1!==a&&0!==a)throw Error(ha+a);if(b=hm(this)+1,c=7*(d=this.d.length-1)+1,d=this.d[d]){for(;d%10==0;d/=10)c--;for(d=this.d[0];d>=10;d/=10)c++}return a&&b>c?b:c},hg.squareRoot=hg.sqrt=function(){var a,b,c,d,e,f,g,h=this.constructor;if(this.s<1){if(!this.s)return new h(0);throw Error(g9+"NaN")}for(a=hm(this),g8=!1,0==(e=Math.sqrt(+this))||e==1/0?(((b=hj(this.d)).length+a)%2==0&&(b+="0"),e=Math.sqrt(b),a=hc((a+1)/2)-(a<0||a%2),d=new h(b=e==1/0?"5e"+a:(b=e.toExponential()).slice(0,b.indexOf("e")+1)+a)):d=new h(e.toString()),e=g=(c=h.precision)+3;;)if(d=(f=d).plus(hk(this,f,g+2)).times(.5),hj(f.d).slice(0,g)===(b=hj(d.d)).slice(0,g)){if(b=b.slice(g-3,g+1),e==g&&"4999"==b){if(hr(f,c+1,0),f.times(f).eq(this)){d=f;break}}else if("9999"!=b)break;g+=4}return g8=!0,hr(d,c)},hg.times=hg.mul=function(a){var b,c,d,e,f,g,h,i,j,k=this.constructor,l=this.d,m=(a=new k(a)).d;if(!this.s||!a.s)return new k(0);for(a.s*=this.s,c=this.e+a.e,(i=l.length)<(j=m.length)&&(f=l,l=m,m=f,g=i,i=j,j=g),f=[],d=g=i+j;d--;)f.push(0);for(d=j;--d>=0;){for(b=0,e=i+d;e>d;)h=f[e]+m[d]*l[e-d-1]+b,f[e--]=h%1e7|0,b=h/1e7|0;f[e]=(f[e]+b)%1e7|0}for(;!f[--g];)f.pop();return b?++c:f.shift(),a.d=f,a.e=c,g8?hr(a,k.precision):a},hg.toDecimalPlaces=hg.todp=function(a,b){var c=this,d=c.constructor;return(c=new d(c),void 0===a)?c:(hi(a,0,1e9),void 0===b?b=d.rounding:hi(b,0,8),hr(c,a+hm(c)+1,b))},hg.toExponential=function(a,b){var c,d=this,e=d.constructor;return void 0===a?c=ht(d,!0):(hi(a,0,1e9),void 0===b?b=e.rounding:hi(b,0,8),c=ht(d=hr(new e(d),a+1,b),!0,a+1)),c},hg.toFixed=function(a,b){var c,d,e=this.constructor;return void 0===a?ht(this):(hi(a,0,1e9),void 0===b?b=e.rounding:hi(b,0,8),c=ht((d=hr(new e(this),a+hm(this)+1,b)).abs(),!1,a+hm(d)+1),this.isneg()&&!this.isZero()?"-"+c:c)},hg.toInteger=hg.toint=function(){var a=this.constructor;return hr(new a(this),hm(this)+1,a.rounding)},hg.toNumber=function(){return+this},hg.toPower=hg.pow=function(a){var b,c,d,e,f,g,h=this,i=h.constructor,j=+(a=new i(a));if(!a.s)return new i(g7);if(!(h=new i(h)).s){if(a.s<1)throw Error(g9+"Infinity");return h}if(h.eq(g7))return h;if(d=i.precision,a.eq(g7))return hr(h,d);if(g=(b=a.e)>=(c=a.d.length-1),f=h.s,g){if((c=j<0?-j:j)<=0x1fffffffffffff){for(e=new i(g7),b=Math.ceil(d/7+4),g8=!1;c%2&&hu((e=e.times(h)).d,b),0!==(c=hc(c/2));)hu((h=h.times(h)).d,b);return g8=!0,a.s<0?new i(g7).div(e):hr(e,d)}}else if(f<0)throw Error(g9+"NaN");return f=f<0&&1&a.d[Math.max(b,c)]?-1:1,h.s=1,g8=!1,e=a.times(hp(h,d+12)),g8=!0,(e=hl(e)).s=f,e},hg.toPrecision=function(a,b){var c,d,e=this,f=e.constructor;return void 0===a?(c=hm(e),d=ht(e,c<=f.toExpNeg||c>=f.toExpPos)):(hi(a,1,1e9),void 0===b?b=f.rounding:hi(b,0,8),c=hm(e=hr(new f(e),a,b)),d=ht(e,a<=c||c<=f.toExpNeg,a)),d},hg.toSignificantDigits=hg.tosd=function(a,b){var c=this.constructor;return void 0===a?(a=c.precision,b=c.rounding):(hi(a,1,1e9),void 0===b?b=c.rounding:hi(b,0,8)),hr(new c(this),a,b)},hg.toString=hg.valueOf=hg.val=hg.toJSON=hg[Symbol.for("nodejs.util.inspect.custom")]=function(){var a=hm(this),b=this.constructor;return ht(this,a<=b.toExpNeg||a>=b.toExpPos)};var hk=function(){function a(a,b){var c,d=0,e=a.length;for(a=a.slice();e--;)c=a[e]*b+d,a[e]=c%1e7|0,d=c/1e7|0;return d&&a.unshift(d),a}function b(a,b,c,d){var e,f;if(c!=d)f=c>d?1:-1;else for(e=f=0;e<c;e++)if(a[e]!=b[e]){f=a[e]>b[e]?1:-1;break}return f}function c(a,b,c){for(var d=0;c--;)a[c]-=d,d=+(a[c]<b[c]),a[c]=1e7*d+a[c]-b[c];for(;!a[0]&&a.length>1;)a.shift()}return function(d,e,f,g){var h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z=d.constructor,A=d.s==e.s?1:-1,B=d.d,C=e.d;if(!d.s)return new z(d);if(!e.s)throw Error(g9+"Division by zero");for(j=0,i=d.e-e.e,x=C.length,v=B.length,o=(n=new z(A)).d=[];C[j]==(B[j]||0);)++j;if(C[j]>(B[j]||0)&&--i,(s=null==f?f=z.precision:g?f+(hm(d)-hm(e))+1:f)<0)return new z(0);if(s=s/7+2|0,j=0,1==x)for(k=0,C=C[0],s++;(j<v||k)&&s--;j++)t=1e7*k+(B[j]||0),o[j]=t/C|0,k=t%C|0;else{for((k=1e7/(C[0]+1)|0)>1&&(C=a(C,k),B=a(B,k),x=C.length,v=B.length),u=x,q=(p=B.slice(0,x)).length;q<x;)p[q++]=0;(y=C.slice()).unshift(0),w=C[0],C[1]>=1e7/2&&++w;do k=0,(h=b(C,p,x,q))<0?(r=p[0],x!=q&&(r=1e7*r+(p[1]||0)),(k=r/w|0)>1?(k>=1e7&&(k=1e7-1),m=(l=a(C,k)).length,q=p.length,1==(h=b(l,p,m,q))&&(k--,c(l,x<m?y:C,m))):(0==k&&(h=k=1),l=C.slice()),(m=l.length)<q&&l.unshift(0),c(p,l,q),-1==h&&(q=p.length,(h=b(C,p,x,q))<1&&(k++,c(p,x<q?y:C,q))),q=p.length):0===h&&(k++,p=[0]),o[j++]=k,h&&p[0]?p[q++]=B[u]||0:(p=[B[u]],q=1);while((u++<v||void 0!==p[0])&&s--)}return o[0]||o.shift(),n.e=i,hr(n,g?f+hm(n)+1:f)}}();function hl(a,b){var c,d,e,f,g,h=0,i=0,j=a.constructor,k=j.precision;if(hm(a)>16)throw Error(hb+hm(a));if(!a.s)return new j(g7);for(null==b?(g8=!1,g=k):g=b,f=new j(.03125);a.abs().gte(.1);)a=a.times(f),i+=5;for(g+=Math.log(hd(2,i))/Math.LN10*2+5|0,c=d=e=new j(g7),j.precision=g;;){if(d=hr(d.times(a),g),c=c.times(++h),hj((f=e.plus(hk(d,c,g))).d).slice(0,g)===hj(e.d).slice(0,g)){for(;i--;)e=hr(e.times(e),g);return j.precision=k,null==b?(g8=!0,hr(e,k)):e}e=f}}function hm(a){for(var b=7*a.e,c=a.d[0];c>=10;c/=10)b++;return b}function hn(a,b,c){if(b>a.LN10.sd())throw g8=!0,c&&(a.precision=c),Error(g9+"LN10 precision limit exceeded");return hr(new a(a.LN10),b)}function ho(a){for(var b="";a--;)b+="0";return b}function hp(a,b){var c,d,e,f,g,h,i,j,k,l=1,m=a,n=m.d,o=m.constructor,p=o.precision;if(m.s<1)throw Error(g9+(m.s?"NaN":"-Infinity"));if(m.eq(g7))return new o(0);if(null==b?(g8=!1,j=p):j=b,m.eq(10))return null==b&&(g8=!0),hn(o,j);if(o.precision=j+=10,d=(c=hj(n)).charAt(0),!(15e14>Math.abs(f=hm(m))))return i=hn(o,j+2,p).times(f+""),m=hp(new o(d+"."+c.slice(1)),j-10).plus(i),o.precision=p,null==b?(g8=!0,hr(m,p)):m;for(;d<7&&1!=d||1==d&&c.charAt(1)>3;)d=(c=hj((m=m.times(a)).d)).charAt(0),l++;for(f=hm(m),d>1?(m=new o("0."+c),f++):m=new o(d+"."+c.slice(1)),h=g=m=hk(m.minus(g7),m.plus(g7),j),k=hr(m.times(m),j),e=3;;){if(g=hr(g.times(k),j),hj((i=h.plus(hk(g,new o(e),j))).d).slice(0,j)===hj(h.d).slice(0,j))return h=h.times(2),0!==f&&(h=h.plus(hn(o,j+2,p).times(f+""))),h=hk(h,new o(l),j),o.precision=p,null==b?(g8=!0,hr(h,p)):h;h=i,e+=2}}function hq(a,b){var c,d,e;for((c=b.indexOf("."))>-1&&(b=b.replace(".","")),(d=b.search(/e/i))>0?(c<0&&(c=d),c+=+b.slice(d+1),b=b.substring(0,d)):c<0&&(c=b.length),d=0;48===b.charCodeAt(d);)++d;for(e=b.length;48===b.charCodeAt(e-1);)--e;if(b=b.slice(d,e)){if(e-=d,a.e=hc((c=c-d-1)/7),a.d=[],d=(c+1)%7,c<0&&(d+=7),d<e){for(d&&a.d.push(+b.slice(0,d)),e-=7;d<e;)a.d.push(+b.slice(d,d+=7));d=7-(b=b.slice(d)).length}else d-=e;for(;d--;)b+="0";if(a.d.push(+b),g8&&(a.e>hf||a.e<-hf))throw Error(hb+c)}else a.s=0,a.e=0,a.d=[0];return a}function hr(a,b,c){var d,e,f,g,h,i,j,k,l=a.d;for(g=1,f=l[0];f>=10;f/=10)g++;if((d=b-g)<0)d+=7,e=b,j=l[k=0];else{if((k=Math.ceil((d+1)/7))>=(f=l.length))return a;for(g=1,j=f=l[k];f>=10;f/=10)g++;d%=7,e=d-7+g}if(void 0!==c&&(h=j/(f=hd(10,g-e-1))%10|0,i=b<0||void 0!==l[k+1]||j%f,i=c<4?(h||i)&&(0==c||c==(a.s<0?3:2)):h>5||5==h&&(4==c||i||6==c&&(d>0?e>0?j/hd(10,g-e):0:l[k-1])%10&1||c==(a.s<0?8:7))),b<1||!l[0])return i?(f=hm(a),l.length=1,b=b-f-1,l[0]=hd(10,(7-b%7)%7),a.e=hc(-b/7)||0):(l.length=1,l[0]=a.e=a.s=0),a;if(0==d?(l.length=k,f=1,k--):(l.length=k+1,f=hd(10,7-d),l[k]=e>0?(j/hd(10,g-e)%hd(10,e)|0)*f:0),i)for(;;)if(0==k){1e7==(l[0]+=f)&&(l[0]=1,++a.e);break}else{if(l[k]+=f,1e7!=l[k])break;l[k--]=0,f=1}for(d=l.length;0===l[--d];)l.pop();if(g8&&(a.e>hf||a.e<-hf))throw Error(hb+hm(a));return a}function hs(a,b){var c,d,e,f,g,h,i,j,k,l,m=a.constructor,n=m.precision;if(!a.s||!b.s)return b.s?b.s=-b.s:b=new m(a),g8?hr(b,n):b;if(i=a.d,l=b.d,d=b.e,j=a.e,i=i.slice(),g=j-d){for((k=g<0)?(c=i,g=-g,h=l.length):(c=l,d=j,h=i.length),g>(e=Math.max(Math.ceil(n/7),h)+2)&&(g=e,c.length=1),c.reverse(),e=g;e--;)c.push(0);c.reverse()}else{for((k=(e=i.length)<(h=l.length))&&(h=e),e=0;e<h;e++)if(i[e]!=l[e]){k=i[e]<l[e];break}g=0}for(k&&(c=i,i=l,l=c,b.s=-b.s),h=i.length,e=l.length-h;e>0;--e)i[h++]=0;for(e=l.length;e>g;){if(i[--e]<l[e]){for(f=e;f&&0===i[--f];)i[f]=1e7-1;--i[f],i[e]+=1e7}i[e]-=l[e]}for(;0===i[--h];)i.pop();for(;0===i[0];i.shift())--d;return i[0]?(b.d=i,b.e=d,g8?hr(b,n):b):new m(0)}function ht(a,b,c){var d,e=hm(a),f=hj(a.d),g=f.length;return b?(c&&(d=c-g)>0?f=f.charAt(0)+"."+f.slice(1)+ho(d):g>1&&(f=f.charAt(0)+"."+f.slice(1)),f=f+(e<0?"e":"e+")+e):e<0?(f="0."+ho(-e-1)+f,c&&(d=c-g)>0&&(f+=ho(d))):e>=g?(f+=ho(e+1-g),c&&(d=c-e-1)>0&&(f=f+"."+ho(d))):((d=e+1)<g&&(f=f.slice(0,d)+"."+f.slice(d)),c&&(d=c-g)>0&&(e+1===g&&(f+="."),f+=ho(d))),a.s<0?"-"+f:f}function hu(a,b){if(a.length>b)return a.length=b,!0}function hv(a){if(!a||"object"!=typeof a)throw Error(g9+"Object expected");var b,c,d,e=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(b=0;b<e.length;b+=3)if(void 0!==(d=a[c=e[b]]))if(hc(d)===d&&d>=e[b+1]&&d<=e[b+2])this[c]=d;else throw Error(ha+c+": "+d);if(void 0!==(d=a[c="LN10"]))if(d==Math.LN10)this[c]=new this(d);else throw Error(ha+c+": "+d);return this}var g6=function a(b){var c,d,e;function f(a){if(!(this instanceof f))return new f(a);if(this.constructor=f,a instanceof f){this.s=a.s,this.e=a.e,this.d=(a=a.d)?a.slice():a;return}if("number"==typeof a){if(0*a!=0)throw Error(ha+a);if(a>0)this.s=1;else if(a<0)a=-a,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(a===~~a&&a<1e7){this.e=0,this.d=[a];return}return hq(this,a.toString())}if("string"!=typeof a)throw Error(ha+a);if(45===a.charCodeAt(0)?(a=a.slice(1),this.s=-1):this.s=1,he.test(a))hq(this,a);else throw Error(ha+a)}if(f.prototype=hg,f.ROUND_UP=0,f.ROUND_DOWN=1,f.ROUND_CEIL=2,f.ROUND_FLOOR=3,f.ROUND_HALF_UP=4,f.ROUND_HALF_DOWN=5,f.ROUND_HALF_EVEN=6,f.ROUND_HALF_CEIL=7,f.ROUND_HALF_FLOOR=8,f.clone=a,f.config=f.set=hv,void 0===b&&(b={}),b)for(c=0,e=["precision","rounding","toExpNeg","toExpPos","LN10"];c<e.length;)b.hasOwnProperty(d=e[c++])||(b[d]=this[d]);return f.config(b),f}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});g7=new g6(1);let hw=g6;var hx=a=>a,hy={},hz=a=>function b(){let c;return 0==arguments.length||1==arguments.length&&(c=arguments.length<=0?void 0:arguments[0],c===hy)?b:a(...arguments)},hA=(a,b)=>1===a?b:hz(function(){for(var c=arguments.length,d=Array(c),e=0;e<c;e++)d[e]=arguments[e];var f=d.filter(a=>a!==hy).length;return f>=a?b(...d):hA(a-f,hz(function(){for(var a=arguments.length,c=Array(a),e=0;e<a;e++)c[e]=arguments[e];return b(...d.map(a=>a===hy?c.shift():a),...c)}))}),hB=a=>hA(a.length,a),hC=(a,b)=>{for(var c=[],d=a;d<b;++d)c[d-a]=d;return c},hD=hB((a,b)=>Array.isArray(b)?b.map(a):Object.keys(b).map(a=>b[a]).map(a)),hE=function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];if(!b.length)return hx;var d=b.reverse(),e=d[0],f=d.slice(1);return function(){return f.reduce((a,b)=>b(a),e(...arguments))}},hF=a=>Array.isArray(a)?a.reverse():a.split("").reverse().join(""),hG=a=>{var b=null,c=null;return function(){for(var d=arguments.length,e=Array(d),f=0;f<d;f++)e[f]=arguments[f];return b&&e.every((a,c)=>{var d;return a===(null==(d=b)?void 0:d[c])})?c:(b=e,c=a(...e))}};function hH(a){return 0===a?1:Math.floor(new hw(a).abs().log(10).toNumber())+1}function hI(a,b,c){for(var d=new hw(a),e=0,f=[];d.lt(b)&&e<1e5;)f.push(d.toNumber()),d=d.add(c),e++;return f}hB((a,b,c)=>{var d=+a;return d+c*(b-d)}),hB((a,b,c)=>{var d=b-a;return(c-a)/(d=d||1/0)}),hB((a,b,c)=>{var d=b-a;return Math.max(0,Math.min(1,(c-a)/(d=d||1/0)))});var hJ=a=>{var[b,c]=a,[d,e]=[b,c];return b>c&&([d,e]=[c,b]),[d,e]},hK=(a,b,c)=>{if(a.lte(0))return new hw(0);var d=hH(a.toNumber()),e=new hw(10).pow(d),f=a.div(e),g=1!==d?.05:.1,h=new hw(Math.ceil(f.div(g).toNumber())).add(c).mul(g).mul(e);return new hw(b?h.toNumber():Math.ceil(h.toNumber()))},hL=function(a,b,c,d){var e,f=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((b-a)/(c-1)))return{step:new hw(0),tickMin:new hw(0),tickMax:new hw(0)};var g=hK(new hw(b).sub(a).div(c-1),d,f),h=Math.ceil((e=a<=0&&b>=0?new hw(0):(e=new hw(a).add(b).div(2)).sub(new hw(e).mod(g))).sub(a).div(g).toNumber()),i=Math.ceil(new hw(b).sub(e).div(g).toNumber()),j=h+i+1;return j>c?hL(a,b,c,d,f+1):(j<c&&(i=b>0?i+(c-j):i,h=b>0?h:h+(c-j)),{step:g,tickMin:e.sub(new hw(h).mul(g)),tickMax:e.add(new hw(i).mul(g))})},hM=hG(function(a){var[b,c]=a,d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,e=!(arguments.length>2)||void 0===arguments[2]||arguments[2],f=Math.max(d,2),[g,h]=hJ([b,c]);if(g===-1/0||h===1/0){var i=h===1/0?[g,...hC(0,d-1).map(()=>1/0)]:[...hC(0,d-1).map(()=>-1/0),h];return b>c?hF(i):i}if(g===h){var j=new hw(1),k=new hw(g);if(!k.isint()&&e){var l=Math.abs(g);l<1?(j=new hw(10).pow(hH(g)-1),k=new hw(Math.floor(k.div(j).toNumber())).mul(j)):l>1&&(k=new hw(Math.floor(g)))}else 0===g?k=new hw(Math.floor((d-1)/2)):e||(k=new hw(Math.floor(g)));var m=Math.floor((d-1)/2);return hE(hD(a=>k.add(new hw(a-m).mul(j)).toNumber()),hC)(0,d)}var{step:n,tickMin:o,tickMax:p}=hL(g,h,f,e,0),q=hI(o,p.add(new hw(.1).mul(n)),n);return b>c?hF(q):q}),hN=hG(function(a,b){var[c,d]=a,e=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[f,g]=hJ([c,d]);if(f===-1/0||g===1/0)return[c,d];if(f===g)return[f];var h=Math.max(b,2),i=hK(new hw(g).sub(f).div(h-1),e,0),j=[...hI(new hw(f),new hw(g),i),g];return!1===e&&(j=j.map(a=>Math.round(a))),c>d?hF(j):j}),hO=a=>a.rootProps.stackOffset,hP=a=>a.options.chartName,hQ=a=>a.rootProps.syncId,hR=a=>a.rootProps.syncMethod,hS=a=>a.options.eventEmitter,hT={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},hU={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},hV=(a,b)=>{if(a&&b)return null!=a&&a.reversed?[b[1],b[0]]:b},hW={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:hT.angleAxisId,includeHidden:!1,name:void 0,reversed:hT.reversed,scale:hT.scale,tick:hT.tick,tickCount:void 0,ticks:void 0,type:hT.type,unit:void 0},hX={allowDataOverflow:hU.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:hU.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:hU.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:hU.scale,tick:hU.tick,tickCount:hU.tickCount,ticks:void 0,type:hU.type,unit:void 0},hY={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:hT.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:hT.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:hT.scale,tick:hT.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},hZ={allowDataOverflow:hU.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:hU.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:hU.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:hU.scale,tick:hU.tick,tickCount:hU.tickCount,ticks:void 0,type:"category",unit:void 0},h$=(a,b)=>null!=a.polarAxis.angleAxis[b]?a.polarAxis.angleAxis[b]:"radial"===a.layout.layoutType?hY:hW,h_=(a,b)=>null!=a.polarAxis.radiusAxis[b]?a.polarAxis.radiusAxis[b]:"radial"===a.layout.layoutType?hZ:hX,h0=a=>a.polarOptions,h1=cg([cQ,cR,c$],function(a,b){var c=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(a-(c.left||0)-(c.right||0)),Math.abs(b-(c.top||0)-(c.bottom||0)))/2}),h2=cg([h0,h1],(a,b)=>{if(null!=a)return F(a.innerRadius,b,0)}),h3=cg([h0,h1],(a,b)=>{if(null!=a)return F(a.outerRadius,b,.8*b)}),h4=cg([h0],a=>{if(null==a)return[0,0];var{startAngle:b,endAngle:c}=a;return[b,c]});cg([h$,h4],hV);var h5=cg([h1,h2,h3],(a,b,c)=>{if(null!=a&&null!=b&&null!=c)return[b,c]});cg([h_,h5],hV);var h6=cg([db,h0,h2,h3,cQ,cR],(a,b,c,d,e,f)=>{if(("centric"===a||"radial"===a)&&null!=b&&null!=c&&null!=d){var{cx:g,cy:h,startAngle:i,endAngle:j}=b;return{cx:F(g,e,e/2),cy:F(h,f,f/2),innerRadius:c,outerRadius:d,startAngle:i,endAngle:j,clockWise:!1}}}),h7=(a,b)=>b,h8=(a,b,c)=>c;function h9(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function ia(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?h9(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):h9(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var ib=[0,"auto"],ic={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},id=(a,b)=>{var c=a.cartesianAxis.xAxis[b];return null==c?ic:c},ie={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:ib,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:60},ig=(a,b)=>{var c=a.cartesianAxis.yAxis[b];return null==c?ie:c},ih={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},ii=(a,b)=>{var c=a.cartesianAxis.zAxis[b];return null==c?ih:c},ij=(a,b,c)=>{switch(b){case"xAxis":return id(a,c);case"yAxis":return ig(a,c);case"zAxis":return ii(a,c);case"angleAxis":return h$(a,c);case"radiusAxis":return h_(a,c);default:throw Error("Unexpected axis type: ".concat(b))}},ik=(a,b,c)=>{switch(b){case"xAxis":return id(a,c);case"yAxis":return ig(a,c);case"angleAxis":return h$(a,c);case"radiusAxis":return h_(a,c);default:throw Error("Unexpected axis type: ".concat(b))}},il=a=>a.graphicalItems.countOfBars>0;function im(a,b){return c=>{switch(a){case"xAxis":return"xAxisId"in c&&c.xAxisId===b;case"yAxis":return"yAxisId"in c&&c.yAxisId===b;case"zAxis":return"zAxisId"in c&&c.zAxisId===b;case"angleAxis":return"angleAxisId"in c&&c.angleAxisId===b;case"radiusAxis":return"radiusAxisId"in c&&c.radiusAxisId===b;default:return!1}}}var io=a=>a.graphicalItems.cartesianItems,ip=cg([h7,h8],im),iq=(a,b,c)=>a.filter(c).filter(a=>(null==b?void 0:b.includeHidden)===!0||!a.hide),ir=cg([io,ij,ip],iq),is=a=>a.filter(a=>void 0===a.stackId),it=cg([ir],is),iu=a=>a.map(a=>a.data).filter(Boolean).flat(1),iv=cg([ir],iu),iw=(a,b)=>{var{chartData:c=[],dataStartIndex:d,dataEndIndex:e}=b;return a.length>0?a:c.slice(d,e+1)},ix=cg([iv,gV],iw),iy=(a,b,c)=>(null==b?void 0:b.dataKey)!=null?a.map(a=>({value:cG(a,b.dataKey)})):c.length>0?c.map(a=>a.dataKey).flatMap(b=>a.map(a=>({value:cG(a,b)}))):a.map(a=>({value:a})),iz=cg([ix,ij,ir],iy);function iA(a,b){switch(a){case"xAxis":return"x"===b.direction;case"yAxis":return"y"===b.direction;default:return!1}}function iB(a){return a.filter(a=>C(a)||a instanceof Date).map(Number).filter(a=>!1===z(a))}var iC=(a,b,c)=>Object.fromEntries(Object.entries(b.reduce((a,b)=>(null==b.stackId||(null==a[b.stackId]&&(a[b.stackId]=[]),a[b.stackId].push(b)),a),{})).map(b=>{var[d,e]=b;return[d,{stackedData:((a,b,c)=>{var d=cK[c];return(function(){var a=cw([]),b=cx,c=cu,d=cy;function e(e){var f,g,h=Array.from(a.apply(this,arguments),cz),i=h.length,j=-1;for(let a of e)for(f=0,++j;f<i;++f)(h[f][j]=[0,+d(a,h[f].key,j,e)]).data=a;for(f=0,g=cv(b(h));f<i;++f)h[g[f]].index=f;return c(h,g),h}return e.keys=function(b){return arguments.length?(a="function"==typeof b?b:cw(Array.from(b)),e):a},e.value=function(a){return arguments.length?(d="function"==typeof a?a:cw(+a),e):d},e.order=function(a){return arguments.length?(b=null==a?cx:"function"==typeof a?a:cw(Array.from(a)),e):b},e.offset=function(a){return arguments.length?(c=null==a?cu:a,e):c},e})().keys(b).value((a,b)=>+cG(a,b,0)).order(cx).offset(d)(a)})(a,e.map(a=>a.dataKey),c),graphicalItems:e}]})),iD=cg([ix,ir,hO],iC),iE=(a,b,c)=>{var{dataStartIndex:d,dataEndIndex:e}=b;if("zAxis"!==c){var f=((a,b,c)=>{if(null!=a)return(a=>[a[0]===1/0?0:a[0],a[1]===-1/0?0:a[1]])(Object.keys(a).reduce((d,e)=>{var{stackedData:f}=a[e],g=f.reduce((a,d)=>{var e=(a=>{var b=a.flat(2).filter(B);return[Math.min(...b),Math.max(...b)]})(d.slice(b,c+1));return[Math.min(a[0],e[0]),Math.max(a[1],e[1])]},[1/0,-1/0]);return[Math.min(g[0],d[0]),Math.max(g[1],d[1])]},[1/0,-1/0]))})(a,d,e);if(null==f||0!==f[0]||0!==f[1])return f}},iF=cg([iD,gT,h7],iE),iG=(a,b,c,d)=>c.length>0?a.flatMap(a=>c.flatMap(c=>{var e,f,g=null==(e=c.errorBars)?void 0:e.filter(a=>iA(d,a)),h=cG(a,null!=(f=b.dataKey)?f:c.dataKey);return{value:h,errorDomain:function(a,b,c){return!c||"number"!=typeof b||z(b)||!c.length?[]:iB(c.flatMap(c=>{var d,e,f=cG(a,c.dataKey);if(Array.isArray(f)?[d,e]=f:d=e=f,gW(d)&&gW(e))return[b-d,b+e]}))}(a,h,g)}})).filter(Boolean):(null==b?void 0:b.dataKey)!=null?a.map(a=>({value:cG(a,b.dataKey),errorDomain:[]})):a.map(a=>({value:a,errorDomain:[]})),iH=cg(ix,ij,it,h7,iG);function iI(a){var{value:b}=a;if(C(b)||b instanceof Date)return b}var iJ=a=>{var b;if(null==a||!("domain"in a))return ib;if(null!=a.domain)return a.domain;if(null!=a.ticks){if("number"===a.type){var c=iB(a.ticks);return[Math.min(...c),Math.max(...c)]}if("category"===a.type)return a.ticks.map(String)}return null!=(b=null==a?void 0:a.domain)?b:ib},iK=function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];var d=b.filter(Boolean);if(0!==d.length){var e=d.flat();return[Math.min(...e),Math.max(...e)]}},iL=a=>a.referenceElements.dots,iM=(a,b,c)=>a.filter(a=>"extendDomain"===a.ifOverflow).filter(a=>"xAxis"===b?a.xAxisId===c:a.yAxisId===c),iN=cg([iL,h7,h8],iM),iO=a=>a.referenceElements.areas,iP=cg([iO,h7,h8],iM),iQ=a=>a.referenceElements.lines,iR=cg([iQ,h7,h8],iM),iS=(a,b)=>{var c=iB(a.map(a=>"xAxis"===b?a.x:a.y));if(0!==c.length)return[Math.min(...c),Math.max(...c)]},iT=cg(iN,h7,iS),iU=(a,b)=>{var c=iB(a.flatMap(a=>["xAxis"===b?a.x1:a.y1,"xAxis"===b?a.x2:a.y2]));if(0!==c.length)return[Math.min(...c),Math.max(...c)]},iV=cg([iP,h7],iU),iW=(a,b)=>{var c=iB(a.map(a=>"xAxis"===b?a.x:a.y));if(0!==c.length)return[Math.min(...c),Math.max(...c)]},iX=cg(iR,h7,iW),iY=cg(iT,iX,iV,(a,b,c)=>iK(a,c,b)),iZ=cg([ij],iJ),i$=(a,b,c,d,e)=>{var f=function(a,b){if(b&&"function"!=typeof a&&Array.isArray(a)&&2===a.length){var c,d,[e,f]=a;if(gW(e))c=e;else if("function"==typeof e)return;if(gW(f))d=f;else if("function"==typeof f)return;var g=[c,d];if(gY(g))return g}}(b,a.allowDataOverflow);return null!=f?f:function(a,b,c){if(c||null!=b){if("function"==typeof a&&null!=b)try{var d=a(b,c);if(gY(d))return gZ(d,b,c)}catch(a){}if(Array.isArray(a)&&2===a.length){var e,f,[g,h]=a;if("auto"===g)null!=b&&(e=Math.min(...b));else if(B(g))e=g;else if("function"==typeof g)try{null!=b&&(e=g(null==b?void 0:b[0]))}catch(a){}else if("string"==typeof g&&cM.test(g)){var i=cM.exec(g);if(null==i||null==b)e=void 0;else{var j=+i[1];e=b[0]-j}}else e=null==b?void 0:b[0];if("auto"===h)null!=b&&(f=Math.max(...b));else if(B(h))f=h;else if("function"==typeof h)try{null!=b&&(f=h(null==b?void 0:b[1]))}catch(a){}else if("string"==typeof h&&cN.test(h)){var k=cN.exec(h);if(null==k||null==b)f=void 0;else{var l=+k[1];f=b[1]+l}}else f=null==b?void 0:b[1];var m=[e,f];if(gY(m))return null==b?m:gZ(m,b,c)}}}(b,iK(c,e,(a=>{var b=iB(a.flatMap(a=>[a.value,a.errorDomain]).flat(1));if(0!==b.length)return[Math.min(...b),Math.max(...b)]})(d)),a.allowDataOverflow)},i_=cg([ij,iZ,iF,iH,iY],i$),i0=[0,1],i1=(a,b,c,d,e,f,g)=>{if(null!=a&&null!=c&&0!==c.length){var{dataKey:h,type:i}=a,j=cH(b,f);return j&&null==h?de()(0,c.length):"category"===i?((a,b,c)=>{var d=a.map(iI).filter(a=>null!=a);return c&&(null==b.dataKey||b.allowDuplicatedCategory&&G(d))?de()(0,a.length):b.allowDuplicatedCategory?d:Array.from(new Set(d))})(d,a,j):"expand"===e?i0:g}},i2=cg([ij,db,ix,iz,hO,h7,i_],i1),i3=(a,b,c,e,f)=>{if(null!=a){var{scale:g,type:h}=a;if("auto"===g)return"radial"===b&&"radiusAxis"===f?"band":"radial"===b&&"angleAxis"===f?"linear":"category"===h&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!c)?"point":"category"===h?"band":"linear";if("string"==typeof g){var i="scale".concat(J(g));return i in d?i:"point"}}},i4=cg([ij,db,il,hP,h7],i3);function i5(a,b,c,e){if(null!=c&&null!=e){if("function"==typeof a.scale)return a.scale.copy().domain(c).range(e);var f=function(a){if(null!=a){if(a in d)return d[a]();var b="scale".concat(J(a));if(b in d)return d[b]()}}(b);if(null!=f){var g=f.domain(c).range(e);return(a=>{var b=a.domain();if(b&&!(b.length<=2)){var c=b.length,d=a.range(),e=Math.min(d[0],d[1])-1e-4,f=Math.max(d[0],d[1])+1e-4,g=a(b[0]),h=a(b[c-1]);(g<e||g>f||h<e||h>f)&&a.domain([b[0],b[c-1]])}})(g),g}}}var i6=(a,b,c)=>{var d=iJ(b);if("auto"===c||"linear"===c){if(null!=b&&b.tickCount&&Array.isArray(d)&&("auto"===d[0]||"auto"===d[1])&&gY(a))return hM(a,b.tickCount,b.allowDecimals);if(null!=b&&b.tickCount&&"number"===b.type&&gY(a))return hN(a,b.tickCount,b.allowDecimals)}},i7=cg([i2,ik,i4],i6),i8=(a,b,c,d)=>"angleAxis"!==d&&(null==a?void 0:a.type)==="number"&&gY(b)&&Array.isArray(c)&&c.length>0?[Math.min(b[0],c[0]),Math.max(b[1],c[c.length-1])]:b,i9=cg([ij,i2,i7,h7],i8),ja=cg(iz,ij,(a,b)=>{if(b&&"number"===b.type){var c=1/0,d=Array.from(iB(a.map(a=>a.value))).sort((a,b)=>a-b);if(d.length<2)return 1/0;var e=d[d.length-1]-d[0];if(0===e)return 1/0;for(var f=0;f<d.length-1;f++)c=Math.min(c,d[f+1]-d[f]);return c/e}}),jb=cg(ja,db,a=>a.rootProps.barCategoryGap,c$,(a,b,c,d)=>d,(a,b,c,d,e)=>{if(!gW(a))return 0;var f="vertical"===b?d.height:d.width;if("gap"===e)return a*f/2;if("no-gap"===e){var g=F(c,a*f),h=a*f/2;return h-g-(h-g)/f*g}return 0}),jc=cg(id,(a,b)=>{var c=id(a,b);return null==c||"string"!=typeof c.padding?0:jb(a,"xAxis",b,c.padding)},(a,b)=>{if(null==a)return{left:0,right:0};var c,d,{padding:e}=a;return"string"==typeof e?{left:b,right:b}:{left:(null!=(c=e.left)?c:0)+b,right:(null!=(d=e.right)?d:0)+b}}),jd=cg(ig,(a,b)=>{var c=ig(a,b);return null==c||"string"!=typeof c.padding?0:jb(a,"yAxis",b,c.padding)},(a,b)=>{if(null==a)return{top:0,bottom:0};var c,d,{padding:e}=a;return"string"==typeof e?{top:b,bottom:b}:{top:(null!=(c=e.top)?c:0)+b,bottom:(null!=(d=e.bottom)?d:0)+b}}),je=cg([c$,jc,c4,c3,(a,b,c)=>c],(a,b,c,d,e)=>{var{padding:f}=d;return e?[f.left,c.width-f.right]:[a.left+b.left,a.left+a.width-b.right]}),jf=cg([c$,db,jd,c4,c3,(a,b,c)=>c],(a,b,c,d,e,f)=>{var{padding:g}=e;return f?[d.height-g.bottom,g.top]:"horizontal"===b?[a.top+a.height-c.bottom,a.top+c.top]:[a.top+c.top,a.top+a.height-c.bottom]}),jg=(a,b,c,d)=>{var e;switch(b){case"xAxis":return je(a,c,d);case"yAxis":return jf(a,c,d);case"zAxis":return null==(e=ii(a,c))?void 0:e.range;case"angleAxis":return h4(a);case"radiusAxis":return h5(a,c);default:return}},jh=cg([ij,jg],hV),ji=cg([ij,i4,i9,jh],i5);function jj(a,b){return a.id<b.id?-1:+(a.id>b.id)}cg(ir,h7,(a,b)=>a.flatMap(a=>{var b;return null!=(b=a.errorBars)?b:[]}).filter(a=>iA(b,a)));var jk=(a,b)=>b,jl=(a,b,c)=>c,jm=cg(cU,jk,jl,(a,b,c)=>a.filter(a=>a.orientation===b).filter(a=>a.mirror===c).sort(jj)),jn=cg(cV,jk,jl,(a,b,c)=>a.filter(a=>a.orientation===b).filter(a=>a.mirror===c).sort(jj)),jo=(a,b)=>({width:a.width,height:b.height}),jp=cg(c$,id,jo),jq=cg(cR,c$,jm,jk,jl,(a,b,c,d,e)=>{var f,g={};return c.forEach(c=>{var h=jo(b,c);null==f&&(f=((a,b,c)=>{switch(b){case"top":return a.top;case"bottom":return c-a.bottom;default:return 0}})(b,d,a));var i="top"===d&&!e||"bottom"===d&&e;g[c.id]=f-Number(i)*h.height,f+=(i?-1:1)*h.height}),g}),jr=cg(cQ,c$,jn,jk,jl,(a,b,c,d,e)=>{var f,g={};return c.forEach(c=>{var h=((a,b)=>({width:"number"==typeof b.width?b.width:60,height:a.height}))(b,c);null==f&&(f=((a,b,c)=>{switch(b){case"left":return a.left;case"right":return c-a.right;default:return 0}})(b,d,a));var i="left"===d&&!e||"right"===d&&e;g[c.id]=f-Number(i)*h.width,f+=(i?-1:1)*h.width}),g}),js=cg(c$,ig,(a,b)=>({width:"number"==typeof b.width?b.width:60,height:a.height})),jt=(a,b,c,d)=>{if(null!=c){var{allowDuplicatedCategory:e,type:f,dataKey:g}=c,h=cH(a,d),i=b.map(a=>a.value);if(g&&h&&"category"===f&&e&&G(i))return i}},ju=cg([db,iz,ij,h7],jt),jv=(a,b,c,d)=>{if(null!=c&&null!=c.dataKey){var{type:e,scale:f}=c;if(cH(a,d)&&("number"===e||"auto"!==f))return b.map(a=>a.value)}},jw=cg([db,iz,ik,h7],jv),jx=cg([db,(a,b,c)=>{switch(b){case"xAxis":return id(a,c);case"yAxis":return ig(a,c);default:throw Error("Unexpected axis type: ".concat(b))}},i4,ji,ju,jw,jg,i7,h7],(a,b,c,d,e,f,g,h,i)=>{if(null==b)return null;var j=cH(a,i);return{angle:b.angle,interval:b.interval,minTickGap:b.minTickGap,orientation:b.orientation,tick:b.tick,tickCount:b.tickCount,tickFormatter:b.tickFormatter,ticks:b.ticks,type:b.type,unit:b.unit,axisType:i,categoricalDomain:f,duplicateDomain:e,isCategorical:j,niceTicks:h,range:g,realScaleType:c,scale:d}}),jy=cg([db,ik,i4,ji,i7,jg,ju,jw,h7],(a,b,c,d,e,f,g,h,i)=>{if(null!=b&&null!=d){var j=cH(a,i),{type:k,ticks:l,tickCount:m}=b,n="scaleBand"===c&&"function"==typeof d.bandwidth?d.bandwidth()/2:2,o="category"===k&&d.bandwidth?d.bandwidth()/n:0;o="angleAxis"===i&&null!=f&&f.length>=2?2*y(f[0]-f[1])*o:o;var p=l||e;return p?p.map((a,b)=>({index:b,coordinate:d(g?g.indexOf(a):a)+o,value:a,offset:o})).filter(a=>!z(a.coordinate)):j&&h?h.map((a,b)=>({coordinate:d(a)+o,value:a,index:b,offset:o})):d.ticks?d.ticks(m).map(a=>({coordinate:d(a)+o,value:a,offset:o})):d.domain().map((a,b)=>({coordinate:d(a)+o,value:g?g[a]:a,index:b,offset:o}))}}),jz=cg([db,ik,ji,jg,ju,jw,h7],(a,b,c,d,e,f,g)=>{if(null!=b&&null!=c&&null!=d&&d[0]!==d[1]){var h=cH(a,g),{tickCount:i}=b,j=0;return(j="angleAxis"===g&&(null==d?void 0:d.length)>=2?2*y(d[0]-d[1])*j:j,h&&f)?f.map((a,b)=>({coordinate:c(a)+j,value:a,index:b,offset:j})):c.ticks?c.ticks(i).map(a=>({coordinate:c(a)+j,value:a,offset:j})):c.domain().map((a,b)=>({coordinate:c(a)+j,value:e?e[a]:a,index:b,offset:j}))}}),jA=cg(ij,ji,(a,b)=>{if(null!=a&&null!=b)return ia(ia({},a),{},{scale:b})}),jB=cg([ij,i4,i2,jh],i5),jC=cg((a,b,c)=>ii(a,c),jB,(a,b)=>{if(null!=a&&null!=b)return ia(ia({},a),{},{scale:b})}),jD=cg([db,cU,cV],(a,b,c)=>{switch(a){case"horizontal":return b.some(a=>a.reversed)?"right-to-left":"left-to-right";case"vertical":return c.some(a=>a.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}}),jE=a=>a.options.defaultTooltipEventType,jF=a=>a.options.validateTooltipEventTypes;function jG(a,b,c){if(null==a)return b;var d=a?"axis":"item";return null==c?b:c.includes(d)?d:b}function jH(a,b){return jG(b,jE(a),jF(a))}var jI=(a,b)=>{var c,d=Number(b);if(!z(d)&&null!=b)return d>=0?null==a||null==(c=a[d])?void 0:c.value:void 0};function jJ(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function jK(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?jJ(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):jJ(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var jL=(a,b,c,d)=>{if(null==b)return bC;var e=function(a,b,c){return"axis"===b?"click"===c?a.axisInteraction.click:a.axisInteraction.hover:"click"===c?a.itemInteraction.click:a.itemInteraction.hover}(a,b,c);if(null==e)return bC;if(e.active)return e;if(a.keyboardInteraction.active)return a.keyboardInteraction;if(a.syncInteraction.active&&null!=a.syncInteraction.index)return a.syncInteraction;var f=!0===a.settings.active;if(null!=e.index){if(f)return jK(jK({},e),{},{active:!0})}else if(null!=d)return{active:!0,coordinate:void 0,dataKey:void 0,index:d};return jK(jK({},bC),{},{coordinate:e.coordinate})},jM=(a,b)=>{var c=null==a?void 0:a.index;if(null==c)return null;var d=Number(c);if(!gW(d))return c;var e=Infinity;return b.length>0&&(e=b.length-1),String(Math.max(0,Math.min(d,e)))},jN=(a,b,c,d,e,f,g,h)=>{if(null!=f&&null!=h){var i=g[0],j=null==i?void 0:h(i.positions,f);if(null!=j)return j;var k=null==e?void 0:e[Number(f)];if(k)if("horizontal"===c)return{x:k.coordinate,y:(d.top+b)/2};else return{x:(d.left+a)/2,y:k.coordinate}}},jO=(a,b,c,d)=>{var e;return"axis"===b?a.tooltipItemPayloads:0===a.tooltipItemPayloads.length?[]:null==(e="hover"===c?a.itemInteraction.hover.dataKey:a.itemInteraction.click.dataKey)&&null!=d?[a.tooltipItemPayloads[0]]:a.tooltipItemPayloads.filter(a=>{var b;return(null==(b=a.settings)?void 0:b.dataKey)===e})},jP=a=>a.options.tooltipPayloadSearcher,jQ=a=>a.tooltip;function jR(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function jS(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?jR(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):jR(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var jT=(a,b,c,d,e,f,g)=>{if(null!=b&&null!=f){var{chartData:h,computedData:i,dataStartIndex:j,dataEndIndex:k}=c;return a.reduce((a,c)=>{var l,m,n,o,p,{dataDefinedOnItem:q,settings:r}=c,s=function(a,b,c){return Array.isArray(a)&&a&&b+c!==0?a.slice(b,c+1):a}((l=q,m=h,null!=l?l:m),j,k),t=null!=(n=null==r?void 0:r.dataKey)?n:null==d?void 0:d.dataKey,u=null==r?void 0:r.nameKey;return Array.isArray(o=null!=d&&d.dataKey&&Array.isArray(s)&&!Array.isArray(s[0])&&"axis"===g?I(s,d.dataKey,e):f(s,b,i,u))?o.forEach(b=>{var c=jS(jS({},r),{},{name:b.name,unit:b.unit,color:void 0,fill:void 0});a.push(cO({tooltipEntrySettings:c,dataKey:b.dataKey,payload:b.payload,value:cG(b.payload,b.dataKey),name:b.name}))}):a.push(cO({tooltipEntrySettings:r,dataKey:t,payload:o,value:cG(o,t),name:null!=(p=cG(o,u))?p:null==r?void 0:r.name})),a},[])}},jU=a=>{var b=db(a);return"horizontal"===b?"xAxis":"vertical"===b?"yAxis":"centric"===b?"angleAxis":"radiusAxis"},jV=a=>a.tooltip.settings.axisId,jW=a=>{var b=jU(a),c=jV(a);return ik(a,b,c)},jX=cg([jW,db,il,hP,jU],i3),jY=cg([a=>a.graphicalItems.cartesianItems,a=>a.graphicalItems.polarItems],(a,b)=>[...a,...b]),jZ=cg([jU,jV],im),j$=cg([jY,jW,jZ],iq),j_=cg([j$],iu),j0=cg([j_,gT],iw),j1=cg([j0,jW,j$],iy),j2=cg([jW],iJ),j3=cg([j0,j$,hO],iC),j4=cg([j3,gT,jU],iE),j5=cg([j$],is),j6=cg([j0,jW,j5,jU],iG),j7=cg([iL,jU,jV],iM),j8=cg([j7,jU],iS),j9=cg([iO,jU,jV],iM),ka=cg([j9,jU],iU),kb=cg([iQ,jU,jV],iM),kc=cg([kb,jU],iW),kd=cg([j8,kc,ka],iK),ke=cg([jW,j2,j4,j6,kd],i$),kf=cg([jW,db,j0,j1,hO,jU,ke],i1),kg=cg([kf,jW,jX],i6),kh=cg([jW,kf,kg,jU],i8),ki=a=>{var b=jU(a),c=jV(a);return jg(a,b,c,!1)},kj=cg([jW,ki],hV),kk=cg([jW,jX,kh,kj],i5),kl=cg([db,j1,jW,jU],jt),km=cg([db,j1,jW,jU],jv),kn=cg([db,jW,jX,kk,ki,kl,km,jU],(a,b,c,d,e,f,g,h)=>{if(b){var{type:i}=b,j=cH(a,h);if(d){var k="scaleBand"===c&&d.bandwidth?d.bandwidth()/2:2,l="category"===i&&d.bandwidth?d.bandwidth()/k:0;return(l="angleAxis"===h&&null!=e&&(null==e?void 0:e.length)>=2?2*y(e[0]-e[1])*l:l,j&&g)?g.map((a,b)=>({coordinate:d(a)+l,value:a,index:b,offset:l})):d.domain().map((a,b)=>({coordinate:d(a)+l,value:f?f[a]:a,index:b,offset:l}))}}}),ko=cg([jE,jF,a=>a.tooltip.settings],(a,b,c)=>jG(c.shared,a,b)),kp=a=>a.tooltip.settings.trigger,kq=a=>a.tooltip.settings.defaultIndex,kr=cg([jQ,ko,kp,kq],jL),ks=cg([kr,j0],jM),kt=cg([kn,ks],jI),ku=cg([kr],a=>{if(a)return a.dataKey}),kv=cg([jQ,ko,kp,kq],jO),kw=cg([cQ,cR,db,c$,kn,kq,kv,jP],jN),kx=cg([kr,kw],(a,b)=>null!=a&&a.coordinate?a.coordinate:b),ky=cg([kr],a=>a.active),kz=cg([kv,ks,gT,jW,kt,jP,ko],jT);cg([kz],a=>{if(null!=a)return Array.from(new Set(a.map(a=>a.payload).filter(a=>null!=a)))});var kA=(a,b)=>b,kB=(a,b,c)=>c,kC=(a,b,c,d)=>d,kD=cg(kn,a=>cr()(a,a=>a.coordinate)),kE=cg([jQ,kA,kB,kC],jL),kF=cg([kE,j0],jM),kG=cg([jQ,kA,kB,kC],jO),kH=cg([cQ,cR,db,c$,kn,kC,kG,jP],jN),kI=cg([kE,kH],(a,b)=>{var c;return null!=(c=a.coordinate)?c:b}),kJ=cg(kn,kF,jI),kK=cg([kG,kF,gT,jW,kJ,jP,kA],jT),kL=cg([kE],a=>({isActive:a.active,activeIndex:a.index})),kM=cg([(a,b)=>b,db,h6,jU,kj,kn,kD,c$],(a,b,c,d,e,f,g,h)=>{if(a&&b&&d&&e&&f){var i=function(a,b,c,d,e){return"horizontal"===c||"vertical"===c?a>=e.left&&a<=e.left+e.width&&b>=e.top&&b<=e.top+e.height?{x:a,y:b}:null:d?((a,b)=>{var c,{x:d,y:e}=a,{radius:f,angle:g}=((a,b)=>{var{x:c,y:d}=a,{cx:e,cy:f}=b,g=((a,b)=>{var{x:c,y:d}=a,{x:e,y:f}=b;return Math.sqrt((c-e)**2+(d-f)**2)})({x:c,y:d},{x:e,y:f});if(g<=0)return{radius:g,angle:0};var h=Math.acos((c-e)/g);return d>f&&(h=2*Math.PI-h),{radius:g,angle:180*h/Math.PI,angleInRadian:h}})({x:d,y:e},b),{innerRadius:h,outerRadius:i}=b;if(f<h||f>i||0===f)return null;var{startAngle:j,endAngle:k}=(a=>{var{startAngle:b,endAngle:c}=a,d=Math.min(Math.floor(b/360),Math.floor(c/360));return{startAngle:b-360*d,endAngle:c-360*d}})(b),l=g;if(j<=k){for(;l>k;)l-=360;for(;l<j;)l+=360;c=l>=j&&l<=k}else{for(;l>j;)l-=360;for(;l<k;)l+=360;c=l>=k&&l<=j}return c?cB(cB({},b),{},{radius:f,angle:((a,b)=>{var{startAngle:c,endAngle:d}=b;return a+360*Math.min(Math.floor(c/360),Math.floor(d/360))})(l,b)}):null})({x:a,y:b},d):null}(a.chartX,a.chartY,b,c,h);if(i){var j=((a,b,c,d,e)=>{var f,g=-1,h=null!=(f=null==b?void 0:b.length)?f:0;if(h<=1||null==a)return 0;if("angleAxis"===d&&null!=e&&1e-6>=Math.abs(Math.abs(e[1]-e[0])-360))for(var i=0;i<h;i++){var j=i>0?c[i-1].coordinate:c[h-1].coordinate,k=c[i].coordinate,l=i>=h-1?c[0].coordinate:c[i+1].coordinate,m=void 0;if(y(k-j)!==y(l-k)){var n=[];if(y(l-k)===y(e[1]-e[0])){m=l;var o=k+e[1]-e[0];n[0]=Math.min(o,(o+j)/2),n[1]=Math.max(o,(o+j)/2)}else{m=j;var p=l+e[1]-e[0];n[0]=Math.min(k,(p+k)/2),n[1]=Math.max(k,(p+k)/2)}var q=[Math.min(k,(m+k)/2),Math.max(k,(m+k)/2)];if(a>q[0]&&a<=q[1]||a>=n[0]&&a<=n[1]){({index:g}=c[i]);break}}else{var r=Math.min(j,l),s=Math.max(j,l);if(a>(r+k)/2&&a<=(s+k)/2){({index:g}=c[i]);break}}}else if(b){for(var t=0;t<h;t++)if(0===t&&a<=(b[t].coordinate+b[t+1].coordinate)/2||t>0&&t<h-1&&a>(b[t].coordinate+b[t-1].coordinate)/2&&a<=(b[t].coordinate+b[t+1].coordinate)/2||t===h-1&&a>(b[t].coordinate+b[t-1].coordinate)/2){({index:g}=b[t]);break}}return g})(((a,b)=>"horizontal"===b?a.x:"vertical"===b?a.y:"centric"===b?a.angle:a.radius)(i,b),g,f,d,e),k=((a,b,c,d)=>{var e=b.find(a=>a&&a.index===c);if(e){if("horizontal"===a)return{x:e.coordinate,y:d.y};if("vertical"===a)return{x:d.x,y:e.coordinate};if("centric"===a){var f=e.coordinate,{radius:g}=d;return cF(cF(cF({},d),cD(d.cx,d.cy,g,f)),{},{angle:f,radius:g})}var h=e.coordinate,{angle:i}=d;return cF(cF(cF({},d),cD(d.cx,d.cy,h,i)),{},{angle:i,radius:h})}return{x:0,y:0}})(b,f,j,i);return{activeIndex:String(j),activeCoordinate:k}}}}),kN=a=>{var b=a.currentTarget.getBoundingClientRect(),c=b.width/a.currentTarget.offsetWidth,d=b.height/a.currentTarget.offsetHeight;return{chartX:Math.round((a.clientX-b.left)/c),chartY:Math.round((a.clientY-b.top)/d)}},kO=aM("mouseClick"),kP=bo();kP.startListening({actionCreator:kO,effect:(a,b)=>{var c=a.payload,d=kM(b.getState(),kN(c));(null==d?void 0:d.activeIndex)!=null&&b.dispatch(bM({activeIndex:d.activeIndex,activeDataKey:void 0,activeCoordinate:d.activeCoordinate}))}});var kQ=aM("mouseMove"),kR=bo();function kS(a,b){return b instanceof HTMLElement?"HTMLElement <".concat(b.tagName,' class="').concat(b.className,'">'):b===window?"global.window":b}function kT(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function kU(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?kT(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):kT(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}kR.startListening({actionCreator:kQ,effect:(a,b)=>{var c=a.payload,d=b.getState(),e=jH(d,d.tooltip.settings.shared),f=kM(d,kN(c));"axis"===e&&((null==f?void 0:f.activeIndex)!=null?b.dispatch(bL({activeIndex:f.activeIndex,activeDataKey:void 0,activeCoordinate:f.activeCoordinate})):b.dispatch(bJ()))}});var kV=aU({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(a,b){a.xAxis[b.payload.id]=b.payload},removeXAxis(a,b){delete a.xAxis[b.payload.id]},addYAxis(a,b){a.yAxis[b.payload.id]=b.payload},removeYAxis(a,b){delete a.yAxis[b.payload.id]},addZAxis(a,b){a.zAxis[b.payload.id]=b.payload},removeZAxis(a,b){delete a.zAxis[b.payload.id]},updateYAxisWidth(a,b){var{id:c,width:d}=b.payload;a.yAxis[c]&&(a.yAxis[c]=kU(kU({},a.yAxis[c]),{},{width:d}))}}}),{addXAxis:kW,removeXAxis:kX,addYAxis:kY,removeYAxis:kZ,addZAxis:k$,removeZAxis:k_,updateYAxisWidth:k0}=kV.actions,k1=kV.reducer,k2=aU({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(a){a.countOfBars+=1},removeBar(a){a.countOfBars-=1},addCartesianGraphicalItem(a,b){a.cartesianItems.push(b.payload)},replaceCartesianGraphicalItem(a,b){var{prev:c,next:d}=b.payload,e=aI(a).cartesianItems.indexOf(c);e>-1&&(a.cartesianItems[e]=d)},removeCartesianGraphicalItem(a,b){var c=aI(a).cartesianItems.indexOf(b.payload);c>-1&&a.cartesianItems.splice(c,1)},addPolarGraphicalItem(a,b){a.polarItems.push(b.payload)},removePolarGraphicalItem(a,b){var c=aI(a).polarItems.indexOf(b.payload);c>-1&&a.polarItems.splice(c,1)}}}),{addBar:k3,removeBar:k4,addCartesianGraphicalItem:k5,replaceCartesianGraphicalItem:k6,removeCartesianGraphicalItem:k7,addPolarGraphicalItem:k8,removePolarGraphicalItem:k9}=k2.actions,la=k2.reducer,lb=aU({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(a,b)=>{a.dots.push(b.payload)},removeDot:(a,b)=>{var c=aI(a).dots.findIndex(a=>a===b.payload);-1!==c&&a.dots.splice(c,1)},addArea:(a,b)=>{a.areas.push(b.payload)},removeArea:(a,b)=>{var c=aI(a).areas.findIndex(a=>a===b.payload);-1!==c&&a.areas.splice(c,1)},addLine:(a,b)=>{a.lines.push(b.payload)},removeLine:(a,b)=>{var c=aI(a).lines.findIndex(a=>a===b.payload);-1!==c&&a.lines.splice(c,1)}}}),{addDot:lc,removeDot:ld,addArea:le,removeArea:lf,addLine:lg,removeLine:lh}=lb.actions,li=lb.reducer,lj={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},lk=aU({name:"brush",initialState:lj,reducers:{setBrushSettings:(a,b)=>null==b.payload?lj:b.payload}}),{setBrushSettings:ll}=lk.actions,lm=lk.reducer,ln=aU({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(a,b){a.size.width=b.payload.width,a.size.height=b.payload.height},setLegendSettings(a,b){a.settings.align=b.payload.align,a.settings.layout=b.payload.layout,a.settings.verticalAlign=b.payload.verticalAlign,a.settings.itemSorter=b.payload.itemSorter},addLegendPayload(a,b){a.payload.push(b.payload)},removeLegendPayload(a,b){var c=aI(a).payload.indexOf(b.payload);c>-1&&a.payload.splice(c,1)}}}),{setLegendSize:lo,setLegendSettings:lp,addLegendPayload:lq,removeLegendPayload:lr}=ln.actions,ls=ln.reducer,lt={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},lu=aU({name:"rootProps",initialState:lt,reducers:{updateOptions:(a,b)=>{var c;a.accessibilityLayer=b.payload.accessibilityLayer,a.barCategoryGap=b.payload.barCategoryGap,a.barGap=null!=(c=b.payload.barGap)?c:lt.barGap,a.barSize=b.payload.barSize,a.maxBarSize=b.payload.maxBarSize,a.stackOffset=b.payload.stackOffset,a.syncId=b.payload.syncId,a.syncMethod=b.payload.syncMethod,a.className=b.payload.className}}}),lv=lu.reducer,{updateOptions:lw}=lu.actions,lx=aU({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(a,b){a.radiusAxis[b.payload.id]=b.payload},removeRadiusAxis(a,b){delete a.radiusAxis[b.payload.id]},addAngleAxis(a,b){a.angleAxis[b.payload.id]=b.payload},removeAngleAxis(a,b){delete a.angleAxis[b.payload.id]}}}),{addRadiusAxis:ly,removeRadiusAxis:lz,addAngleAxis:lA,removeAngleAxis:lB}=lx.actions,lC=lx.reducer,lD=aU({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(a,b)=>b.payload}}),{updatePolarOptions:lE}=lD.actions,lF=lD.reducer,lG=aM("keyDown"),lH=aM("focus"),lI=bo();lI.startListening({actionCreator:lG,effect:(a,b)=>{var c=b.getState();if(!1!==c.rootProps.accessibilityLayer){var{keyboardInteraction:d}=c.tooltip,e=a.payload;if("ArrowRight"===e||"ArrowLeft"===e||"Enter"===e){var f=Number(jM(d,j0(c))),g=kn(c);if("Enter"===e){var h=kH(c,"axis","hover",String(d.index));b.dispatch(bO({active:!d.active,activeIndex:d.index,activeDataKey:d.dataKey,activeCoordinate:h}));return}var i=f+("ArrowRight"===e?1:-1)*("left-to-right"===jD(c)?1:-1);if(null!=g&&!(i>=g.length)&&!(i<0)){var j=kH(c,"axis","hover",String(i));b.dispatch(bO({active:!0,activeIndex:i.toString(),activeDataKey:void 0,activeCoordinate:j}))}}}}}),lI.startListening({actionCreator:lH,effect:(a,b)=>{var c=b.getState();if(!1!==c.rootProps.accessibilityLayer){var{keyboardInteraction:d}=c.tooltip;if(!d.active&&null==d.index){var e=kH(c,"axis","hover",String("0"));b.dispatch(bO({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:e}))}}}});var lJ=aM("externalEvent"),lK=bo();lK.startListening({actionCreator:lJ,effect:(a,b)=>{if(null!=a.payload.handler){var c=b.getState(),d={activeCoordinate:kx(c),activeDataKey:ku(c),activeIndex:ks(c),activeLabel:kt(c),activeTooltipIndex:ks(c),isTooltipActive:ky(c)};a.payload.handler(d,a.payload.reactEvent)}}});var lL=cg([jQ],a=>a.tooltipItemPayloads),lM=cg([lL,jP,(a,b,c)=>b,(a,b,c)=>c],(a,b,c,d)=>{var e=a.find(a=>a.settings.dataKey===d);if(null!=e){var{positions:f}=e;if(null!=f)return b(f,c)}}),lN=aM("touchMove"),lO=bo();lO.startListening({actionCreator:lN,effect:(a,b)=>{var c=a.payload,d=b.getState(),e=jH(d,d.tooltip.settings.shared);if("axis"===e){var f=kM(d,kN({clientX:c.touches[0].clientX,clientY:c.touches[0].clientY,currentTarget:c.currentTarget}));(null==f?void 0:f.activeIndex)!=null&&b.dispatch(bL({activeIndex:f.activeIndex,activeDataKey:void 0,activeCoordinate:f.activeCoordinate}))}else if("item"===e){var g,h=c.touches[0],i=document.elementFromPoint(h.clientX,h.clientY);if(!i||!i.getAttribute)return;var j=i.getAttribute(cW),k=null!=(g=i.getAttribute(cX))?g:void 0,l=lM(b.getState(),j,k);b.dispatch(bH({activeDataKey:k,activeIndex:j,activeCoordinate:l}))}}});var lP=T({brush:lm,cartesianAxis:k1,chartData:bU,graphicalItems:la,layout:b$,legend:ls,options:bs,polarAxis:lC,polarOptions:lF,referenceElements:li,rootProps:lv,tooltip:bP}),lQ=function(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return function(a){let b,c,d,e=function(a){let{thunk:b=!0,immutableCheck:c=!0,serializableCheck:d=!0,actionCreatorCheck:e=!0}=a??{},f=new aN;return b&&("boolean"==typeof b?f.push(X):f.push(W(b.extraArgument))),f},{reducer:f,middleware:g,devTools:h=!0,duplicateMiddlewareCheck:i=!0,preloadedState:j,enhancers:k}=a||{};if("function"==typeof f)b=f;else if(S(f))b=T(f);else throw Error(bp(1));c="function"==typeof g?g(e):e();let l=U;h&&(l=aL({trace:!1,..."object"==typeof h&&h}));let m=(d=function(...a){return b=>(c,d)=>{let e=b(c,d),f=()=>{throw Error(O(15))},g={getState:e.getState,dispatch:(a,...b)=>f(a,...b)};return f=U(...a.map(a=>a(g)))(e.dispatch),{...e,dispatch:f}}}(...c),function(a){let{autoBatch:b=!0}=a??{},c=new aN(d);return b&&c.push(((a={type:"raf"})=>b=>(...c)=>{let d=b(...c),e=!0,f=!1,g=!1,h=new Set,i="tick"===a.type?queueMicrotask:"raf"===a.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:aQ(10):"callback"===a.type?a.queueNotification:aQ(a.timeout),j=()=>{g=!1,f&&(f=!1,h.forEach(a=>a()))};return Object.assign({},d,{subscribe(a){let b=d.subscribe(()=>e&&a());return h.add(a),()=>{b(),h.delete(a)}},dispatch(a){try{return(f=!(e=!a?.meta?.RTK_autoBatch))&&!g&&(g=!0,i(j)),d.dispatch(a)}finally{e=!0}}})})("object"==typeof b?b:void 0)),c});return function a(b,c,d){if("function"!=typeof b)throw Error(O(2));if("function"==typeof c&&"function"==typeof d||"function"==typeof d&&"function"==typeof arguments[3])throw Error(O(0));if("function"==typeof c&&void 0===d&&(d=c,c=void 0),void 0!==d){if("function"!=typeof d)throw Error(O(1));return d(a)(b,c)}let e=b,f=c,g=new Map,h=g,i=0,j=!1;function k(){h===g&&(h=new Map,g.forEach((a,b)=>{h.set(b,a)}))}function l(){if(j)throw Error(O(3));return f}function m(a){if("function"!=typeof a)throw Error(O(4));if(j)throw Error(O(5));let b=!0;k();let c=i++;return h.set(c,a),function(){if(b){if(j)throw Error(O(6));b=!1,k(),h.delete(c),g=null}}}function n(a){if(!S(a))throw Error(O(7));if(void 0===a.type)throw Error(O(8));if("string"!=typeof a.type)throw Error(O(17));if(j)throw Error(O(9));try{j=!0,f=e(f,a)}finally{j=!1}return(g=h).forEach(a=>{a()}),a}return n({type:R.INIT}),{dispatch:n,subscribe:m,getState:l,replaceReducer:function(a){if("function"!=typeof a)throw Error(O(10));e=a,n({type:R.REPLACE})},[P]:function(){return{subscribe(a){if("object"!=typeof a||null===a)throw Error(O(11));function b(){a.next&&a.next(l())}return b(),{unsubscribe:m(b)}},[P](){return this}}}}}(b,j,l(..."function"==typeof k?k(m):m()))}({reducer:lP,preloadedState:a,middleware:a=>a({serializableCheck:!1}).concat([kP.middleware,kR.middleware,lI.middleware,lK.middleware,lO.middleware]),devTools:{serialize:{replacer:kS},name:"recharts-".concat(b)}})};function lR(a){var{preloadedState:b,children:c,reduxStoreName:d}=a,e=c2(),g=(0,f.useRef)(null);return e?c:(null==g.current&&(g.current=lQ(b,d)),f.createElement(bB,{context:cj,store:g.current},c))}var lS=a=>{var{chartData:b}=a,c=cl(),d=c2();return(0,f.useEffect)(()=>d?()=>{}:(c(bR(b)),()=>{c(bR(void 0))}),[b,c,d]),null};function lT(a){var{layout:b,width:c,height:d,margin:e}=a;return cl(),c2(),null}function lU(a){return cl(),null}var lV=c(9632),lW=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],lX=["points","pathLength"],lY={svg:["viewBox","children"],polygon:lX,polyline:lX},lZ=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],l$=(a,b,c)=>{if(null===a||"object"!=typeof a&&"function"!=typeof a)return null;var d=null;return Object.keys(a).forEach(e=>{var f=a[e];lZ.includes(e)&&"function"==typeof f&&(d||(d={}),d[e]=a=>(f(b,c,a),null))}),d},l_=a=>"string"==typeof a?a:a?a.displayName||a.name||"Component":"",l0=null,l1=null,l2=a=>{if(a===l0&&Array.isArray(l1))return l1;var b=[];return f.Children.forEach(a,a=>{null==a||((0,lV.isFragment)(a)?b=b.concat(l2(a.props.children)):b.push(a))}),l1=b,l0=a,b};function l3(a,b){var c=[],d=[];return d=Array.isArray(b)?b.map(a=>l_(a)):[l_(b)],l2(a).forEach(a=>{var b=x()(a,"type.displayName")||x()(a,"type.name");-1!==d.indexOf(b)&&c.push(a)}),c}var l4=(a,b,c)=>{if(!a||"function"==typeof a||"boolean"==typeof a)return null;var d=a;if((0,f.isValidElement)(a)&&(d=a.props),"object"!=typeof d&&"function"!=typeof d)return null;var e={};return Object.keys(d).forEach(a=>{var f;((a,b,c,d)=>{var e,f=null!=(e=d&&(null==lY?void 0:lY[d]))?e:[];return b.startsWith("data-")||"function"!=typeof a&&(d&&f.includes(b)||lW.includes(b))||c&&lZ.includes(b)})(null==(f=d)?void 0:f[a],a,b,c)&&(e[a]=d[a])}),e},l5=()=>cp(a=>a.rootProps.accessibilityLayer),l6=["children","width","height","viewBox","className","style","title","desc"];function l7(){return(l7=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var l8=(0,f.forwardRef)((a,b)=>{var{children:c,width:d,height:e,viewBox:g,className:h,style:i,title:j,desc:k}=a,l=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,l6),m=g||{width:d,height:e,x:0,y:0},n=t("recharts-surface",h);return f.createElement("svg",l7({},l4(l,!0,"svg"),{className:n,width:d,height:e,style:i,viewBox:"".concat(m.x," ").concat(m.y," ").concat(m.width," ").concat(m.height),ref:b}),f.createElement("title",null,j),f.createElement("desc",null,k),c)}),l9=["children"];function ma(){return(ma=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var mb={width:"100%",height:"100%"},mc=(0,f.forwardRef)((a,b)=>{var c,d,e=c8(),g=c9(),h=l5();if(!gX(e)||!gX(g))return null;var{children:i,otherAttributes:j,title:k,desc:l}=a;return c="number"==typeof j.tabIndex?j.tabIndex:h?0:void 0,d="string"==typeof j.role?j.role:h?"application":void 0,f.createElement(l8,ma({},j,{title:k,desc:l,role:d,tabIndex:c,width:e,height:g,style:mb,ref:b}),i)}),md=a=>{var{children:b}=a,c=cp(c4);if(!c)return null;var{width:d,height:e,y:g,x:h}=c;return f.createElement(l8,{width:d,height:e,x:h,y:g},b)},me=(0,f.forwardRef)((a,b)=>{var{children:c}=a,d=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,l9);return c2()?f.createElement(md,null,c):f.createElement(mc,ma({ref:b},d),c)});function mf(a){return a.tooltip.syncInteraction}new(c(1117));var mg=(0,f.createContext)(null),mh=(0,f.createContext)(null);function mi(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}var mj=(0,f.forwardRef)((a,b)=>{var{children:c,className:d,height:e,onClick:g,onContextMenu:h,onDoubleClick:i,onMouseDown:j,onMouseEnter:k,onMouseLeave:l,onMouseMove:m,onMouseUp:n,onTouchEnd:o,onTouchMove:p,onTouchStart:q,style:r,width:s}=a,u=cl(),[v,w]=(0,f.useState)(null),[x,y]=(0,f.useState)(null);cl(),cp(hQ),cp(hS),cl(),cp(hR),cp(kn),dc(),c5(),cp(a=>a.rootProps.className),cp(hQ),cp(hS),cl();var z=function(){cl();var[a,b]=(0,f.useState)(null);return cp(cS),b}(),A=(0,f.useCallback)(a=>{z(a),"function"==typeof b&&b(a),w(a),y(a)},[z,b,w,y]),B=(0,f.useCallback)(a=>{u(kO(a)),u(lJ({handler:g,reactEvent:a}))},[u,g]),C=(0,f.useCallback)(a=>{u(kQ(a)),u(lJ({handler:k,reactEvent:a}))},[u,k]),D=(0,f.useCallback)(a=>{u(bJ()),u(lJ({handler:l,reactEvent:a}))},[u,l]),E=(0,f.useCallback)(a=>{u(kQ(a)),u(lJ({handler:m,reactEvent:a}))},[u,m]),F=(0,f.useCallback)(()=>{u(lH())},[u]),G=(0,f.useCallback)(a=>{u(lG(a.key))},[u]),H=(0,f.useCallback)(a=>{u(lJ({handler:h,reactEvent:a}))},[u,h]),I=(0,f.useCallback)(a=>{u(lJ({handler:i,reactEvent:a}))},[u,i]),J=(0,f.useCallback)(a=>{u(lJ({handler:j,reactEvent:a}))},[u,j]),K=(0,f.useCallback)(a=>{u(lJ({handler:n,reactEvent:a}))},[u,n]),L=(0,f.useCallback)(a=>{u(lJ({handler:q,reactEvent:a}))},[u,q]),M=(0,f.useCallback)(a=>{u(lN(a)),u(lJ({handler:p,reactEvent:a}))},[u,p]),N=(0,f.useCallback)(a=>{u(lJ({handler:o,reactEvent:a}))},[u,o]);return f.createElement(mg.Provider,{value:v},f.createElement(mh.Provider,{value:x},f.createElement("div",{className:t("recharts-wrapper",d),style:function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?mi(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):mi(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({position:"relative",cursor:"default",width:s,height:e},r),onClick:B,onContextMenu:H,onDoubleClick:I,onFocus:F,onKeyDown:G,onMouseDown:J,onMouseEnter:C,onMouseLeave:D,onMouseMove:E,onMouseUp:K,onTouchEnd:N,onTouchMove:M,onTouchStart:L,ref:A},c)))}),mk=cg([c$],a=>{if(a)return{top:a.top,bottom:a.bottom,left:a.left,right:a.right}}),ml=cg([mk,cQ,cR],(a,b,c)=>{if(a&&null!=b&&null!=c)return{x:a.left,y:a.top,width:Math.max(0,b-a.left-a.right),height:Math.max(0,c-a.top-a.bottom)}}),mm=()=>cp(ml),mn=(0,f.createContext)(void 0),mo=a=>{var{children:b}=a,[c]=(0,f.useState)("".concat(E("recharts"),"-clip")),d=mm();if(null==d)return null;var{x:e,y:g,width:h,height:i}=d;return f.createElement(mn.Provider,{value:c},f.createElement("defs",null,f.createElement("clipPath",{id:c},f.createElement("rect",{x:e,y:g,height:i,width:h}))),b)},mp=["children","className","width","height","style","compact","title","desc"],mq=(0,f.forwardRef)((a,b)=>{var{children:c,className:d,width:e,height:g,style:h,compact:i,title:j,desc:k}=a,l=l4(function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,mp),!1);return i?f.createElement(me,{otherAttributes:l,title:j,desc:k},c):f.createElement(mj,{className:d,style:h,width:e,height:g,onClick:a.onClick,onMouseLeave:a.onMouseLeave,onMouseEnter:a.onMouseEnter,onMouseMove:a.onMouseMove,onMouseDown:a.onMouseDown,onMouseUp:a.onMouseUp,onContextMenu:a.onContextMenu,onDoubleClick:a.onDoubleClick,onTouchStart:a.onTouchStart,onTouchMove:a.onTouchMove,onTouchEnd:a.onTouchEnd},f.createElement(me,{otherAttributes:l,title:j,desc:k,ref:b},f.createElement(mo,null,c)))});function mr(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function ms(a,b){var c=function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?mr(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):mr(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({},a);return Object.keys(b).reduce((a,c)=>(void 0===a[c]&&void 0!==b[c]&&(a[c]=b[c]),a),c)}var mt=["width","height"];function mu(){return(mu=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var mv={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},mw=(0,f.forwardRef)(function(a,b){var c,d=ms(a.categoricalChartProps,mv),{width:e,height:g}=d,h=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(d,mt);if(!gX(e)||!gX(g))return null;var{chartName:i,defaultTooltipEventType:j,validateTooltipEventTypes:k,tooltipPayloadSearcher:l,categoricalChartProps:m}=a;return f.createElement(lR,{preloadedState:{options:{chartName:i,defaultTooltipEventType:j,validateTooltipEventTypes:k,tooltipPayloadSearcher:l,eventEmitter:void 0}},reduxStoreName:null!=(c=m.id)?c:i},f.createElement(lS,{chartData:m.data}),f.createElement(lT,{width:e,height:g,layout:d.layout,margin:d.margin}),f.createElement(lU,{accessibilityLayer:d.accessibilityLayer,barCategoryGap:d.barCategoryGap,maxBarSize:d.maxBarSize,stackOffset:d.stackOffset,barGap:d.barGap,barSize:d.barSize,syncId:d.syncId,syncMethod:d.syncMethod,className:d.className}),f.createElement(mq,mu({},h,{width:e,height:g,ref:b})))}),mx=["item"],my=(0,f.forwardRef)((a,b)=>f.createElement(mw,{chartName:"ScatterChart",defaultTooltipEventType:"item",validateTooltipEventTypes:mx,tooltipPayloadSearcher:bq,categoricalChartProps:a,ref:b})),mz={isSsr:!0};function mA(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function mB(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?mA(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):mA(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var mC={widthCache:{},cacheCount:0},mD={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},mE="recharts_measurement_span",mF=function(a){var b,c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==a||mz.isSsr)return{width:0,height:0};var d=(Object.keys(b=mB({},c)).forEach(a=>{b[a]||delete b[a]}),b),e=JSON.stringify({text:a,copyStyle:d});if(mC.widthCache[e])return mC.widthCache[e];try{var f=document.getElementById(mE);f||((f=document.createElement("span")).setAttribute("id",mE),f.setAttribute("aria-hidden","true"),document.body.appendChild(f));var g=mB(mB({},mD),d);Object.assign(f.style,g),f.textContent="".concat(a);var h=f.getBoundingClientRect(),i={width:h.width,height:h.height};return mC.widthCache[e]=i,++mC.cacheCount>2e3&&(mC.cacheCount=0,mC.widthCache={}),i}catch(a){return{width:0,height:0}}};class mG{static create(a){return new mG(a)}constructor(a){this.scale=a}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(a){var{bandAware:b,position:c}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==a){if(c)switch(c){case"start":default:return this.scale(a);case"middle":var d=this.bandwidth?this.bandwidth()/2:0;return this.scale(a)+d;case"end":var e=this.bandwidth?this.bandwidth():0;return this.scale(a)+e}if(b){var f=this.bandwidth?this.bandwidth()/2:0;return this.scale(a)+f}return this.scale(a)}}isInRange(a){var b=this.range(),c=b[0],d=b[b.length-1];return c<=d?a>=c&&a<=d:a>=d&&a<=c}}!function(a,b,c){var d;(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:1e-4,enumerable:!0,configurable:!0,writable:!0}):a[b]=1e-4}(mG,"EPS",1e-4);var mH=function(a){var{width:b,height:c}=a,d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,e=(d%180+180)%180*Math.PI/180,f=Math.atan(c/b);return Math.abs(e>f&&e<Math.PI-f?c/Math.sin(e):b/Math.cos(e))};function mI(a,b,c){if(b<1)return[];if(1===b&&void 0===c)return a;for(var d=[],e=0;e<a.length;e+=b)if(void 0!==c&&!0!==c(a[e]))return;else d.push(a[e]);return d}function mJ(a,b,c,d,e){if(a*b<a*d||a*b>a*e)return!1;var f=c();return a*(b-a*f/2-d)>=0&&a*(b+a*f/2-e)<=0}function mK(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function mL(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?mK(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):mK(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function mM(a,b,c){var d,{tick:e,ticks:f,viewBox:g,minTickGap:h,orientation:i,interval:j,tickFormatter:k,unit:l,angle:m}=a;if(!f||!f.length||!e)return[];if(B(j)||mz.isSsr)return null!=(d=mI(f,(B(j)?j:0)+1))?d:[];var n="top"===i||"bottom"===i?"width":"height",o=l&&"width"===n?mF(l,{fontSize:b,letterSpacing:c}):{width:0,height:0},p=(a,d)=>{var e,f="function"==typeof k?k(a.value,d):a.value;return"width"===n?(e=mF(f,{fontSize:b,letterSpacing:c}),mH({width:e.width+o.width,height:e.height+o.height},m)):mF(f,{fontSize:b,letterSpacing:c})[n]},q=f.length>=2?y(f[1].coordinate-f[0].coordinate):1,r=function(a,b,c){var d="width"===c,{x:e,y:f,width:g,height:h}=a;return 1===b?{start:d?e:f,end:d?e+g:f+h}:{start:d?e+g:f+h,end:d?e:f}}(g,q,n);return"equidistantPreserveStart"===j?function(a,b,c,d,e){for(var f,g=(d||[]).slice(),{start:h,end:i}=b,j=0,k=1,l=h;k<=g.length;)if(f=function(){var b,f=null==d?void 0:d[j];if(void 0===f)return{v:mI(d,k)};var g=j,m=()=>(void 0===b&&(b=c(f,g)),b),n=f.coordinate,o=0===j||mJ(a,n,m,l,i);o||(j=0,l=h,k+=1),o&&(l=n+a*(m()/2+e),j+=k)}())return f.v;return[]}(q,r,p,f,h):("preserveStart"===j||"preserveStartEnd"===j?function(a,b,c,d,e,f){var g=(d||[]).slice(),h=g.length,{start:i,end:j}=b;if(f){var k=d[h-1],l=c(k,h-1),m=a*(k.coordinate+a*l/2-j);g[h-1]=k=mL(mL({},k),{},{tickCoord:m>0?k.coordinate-m*a:k.coordinate}),mJ(a,k.tickCoord,()=>l,i,j)&&(j=k.tickCoord-a*(l/2+e),g[h-1]=mL(mL({},k),{},{isShow:!0}))}for(var n=f?h-1:h,o=function(b){var d,f=g[b],h=()=>(void 0===d&&(d=c(f,b)),d);if(0===b){var k=a*(f.coordinate-a*h()/2-i);g[b]=f=mL(mL({},f),{},{tickCoord:k<0?f.coordinate-k*a:f.coordinate})}else g[b]=f=mL(mL({},f),{},{tickCoord:f.coordinate});mJ(a,f.tickCoord,h,i,j)&&(i=f.tickCoord+a*(h()/2+e),g[b]=mL(mL({},f),{},{isShow:!0}))},p=0;p<n;p++)o(p);return g}(q,r,p,f,h,"preserveStartEnd"===j):function(a,b,c,d,e){for(var f=(d||[]).slice(),g=f.length,{start:h}=b,{end:i}=b,j=function(b){var d,j=f[b],k=()=>(void 0===d&&(d=c(j,b)),d);if(b===g-1){var l=a*(j.coordinate+a*k()/2-i);f[b]=j=mL(mL({},j),{},{tickCoord:l>0?j.coordinate-l*a:j.coordinate})}else f[b]=j=mL(mL({},j),{},{tickCoord:j.coordinate});mJ(a,j.tickCoord,k,h,i)&&(i=j.tickCoord-a*(k()/2+e),f[b]=mL(mL({},j),{},{isShow:!0}))},k=g-1;k>=0;k--)j(k);return f}(q,r,p,f,h)).filter(a=>a.isShow)}function mN(a,b){for(var c in a)if(({}).hasOwnProperty.call(a,c)&&(!({}).hasOwnProperty.call(b,c)||a[c]!==b[c]))return!1;for(var d in b)if(({}).hasOwnProperty.call(b,d)&&!({}).hasOwnProperty.call(a,d))return!1;return!0}var mO=["children","className"];function mP(){return(mP=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var mQ=f.forwardRef((a,b)=>{var{children:c,className:d}=a,e=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,mO),g=t("recharts-layer",d);return f.createElement("g",mP({className:g},l4(e,!0),{ref:b}),c)}),mR=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,mS=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,mT=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,mU=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,mV={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},mW=Object.keys(mV);class mX{static parse(a){var b,[,c,d]=null!=(b=mU.exec(a))?b:[];return new mX(parseFloat(c),null!=d?d:"")}constructor(a,b){this.num=a,this.unit=b,this.num=a,this.unit=b,z(a)&&(this.unit=""),""===b||mT.test(b)||(this.num=NaN,this.unit=""),mW.includes(b)&&(this.num=a*mV[b],this.unit="px")}add(a){return this.unit!==a.unit?new mX(NaN,""):new mX(this.num+a.num,this.unit)}subtract(a){return this.unit!==a.unit?new mX(NaN,""):new mX(this.num-a.num,this.unit)}multiply(a){return""!==this.unit&&""!==a.unit&&this.unit!==a.unit?new mX(NaN,""):new mX(this.num*a.num,this.unit||a.unit)}divide(a){return""!==this.unit&&""!==a.unit&&this.unit!==a.unit?new mX(NaN,""):new mX(this.num/a.num,this.unit||a.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return z(this.num)}}function mY(a){if(a.includes("NaN"))return"NaN";for(var b=a;b.includes("*")||b.includes("/");){var c,[,d,e,f]=null!=(c=mR.exec(b))?c:[],g=mX.parse(null!=d?d:""),h=mX.parse(null!=f?f:""),i="*"===e?g.multiply(h):g.divide(h);if(i.isNaN())return"NaN";b=b.replace(mR,i.toString())}for(;b.includes("+")||/.-\d+(?:\.\d+)?/.test(b);){var j,[,k,l,m]=null!=(j=mS.exec(b))?j:[],n=mX.parse(null!=k?k:""),o=mX.parse(null!=m?m:""),p="+"===l?n.add(o):n.subtract(o);if(p.isNaN())return"NaN";b=b.replace(mS,p.toString())}return b}var mZ=/\(([^()]*)\)/;function m$(a){var b=function(a){try{var b;return b=a.replace(/\s+/g,""),b=function(a){for(var b,c=a;null!=(b=mZ.exec(c));){var[,d]=b;c=c.replace(mZ,mY(d))}return c}(b),b=mY(b)}catch(a){return"NaN"}}(a.slice(5,-1));return"NaN"===b?"":b}var m_=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],m0=["dx","dy","angle","className","breakAll"];function m1(){return(m1=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function m2(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var m3=/[ \f\n\r\t\v\u2028\u2029]+/,m4=a=>{var{children:b,breakAll:c,style:d}=a;try{let a;var e=[];a=b,null==a||(e=c?b.toString().split(""):b.toString().split(m3));var f=e.map(a=>({word:a,width:mF(a,d).width})),g=c?0:mF("\xa0",d).width;return{wordsWithComputedWidth:f,spaceWidth:g}}catch(a){return null}},m5=a=>[{words:null==a?[]:a.toString().split(m3)}],m6="#808080",m7=(0,f.forwardRef)((a,b)=>{var c,{x:d=0,y:e=0,lineHeight:g="1em",capHeight:h="0.71em",scaleToFit:i=!1,textAnchor:j="start",verticalAnchor:k="end",fill:l=m6}=a,m=m2(a,m_),n=(0,f.useMemo)(()=>(a=>{var{width:b,scaleToFit:c,children:d,style:e,breakAll:f,maxLines:g}=a;if((b||c)&&!mz.isSsr){var h=m4({breakAll:f,children:d,style:e});if(!h)return m5(d);var{wordsWithComputedWidth:i,spaceWidth:j}=h;return((a,b,c,d,e)=>{var f,{maxLines:g,children:h,style:i,breakAll:j}=a,k=B(g),l=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return a.reduce((a,b)=>{var{word:f,width:g}=b,h=a[a.length-1];return h&&(null==d||e||h.width+g+c<Number(d))?(h.words.push(f),h.width+=g+c):a.push({words:[f],width:g}),a},[])},m=l(b),n=a=>a.reduce((a,b)=>a.width>b.width?a:b);if(!k||e||!(m.length>g||n(m).width>Number(d)))return m;for(var o=a=>{var b=l(m4({breakAll:j,style:i,children:h.slice(0,a)+"…"}).wordsWithComputedWidth);return[b.length>g||n(b).width>Number(d),b]},p=0,q=h.length-1,r=0;p<=q&&r<=h.length-1;){var s=Math.floor((p+q)/2),[t,u]=o(s-1),[v]=o(s);if(t||v||(p=s+1),t&&v&&(q=s-1),!t&&v){f=u;break}r++}return f||m})({breakAll:f,children:d,maxLines:g,style:e},i,j,b,c)}return m5(d)})({breakAll:m.breakAll,children:m.children,maxLines:m.maxLines,scaleToFit:i,style:m.style,width:m.width}),[m.breakAll,m.children,m.maxLines,i,m.style,m.width]),{dx:o,dy:p,angle:q,className:r,breakAll:s}=m,u=m2(m,m0);if(!C(d)||!C(e))return null;var v=d+(B(o)?o:0),w=e+(B(p)?p:0);switch(k){case"start":c=m$("calc(".concat(h,")"));break;case"middle":c=m$("calc(".concat((n.length-1)/2," * -").concat(g," + (").concat(h," / 2))"));break;default:c=m$("calc(".concat(n.length-1," * -").concat(g,")"))}var x=[];if(i){var y=n[0].width,{width:z}=m;x.push("scale(".concat(B(z)?z/y:1,")"))}return q&&x.push("rotate(".concat(q,", ").concat(v,", ").concat(w,")")),x.length&&(u.transform=x.join(" ")),f.createElement("text",m1({},l4(u,!0),{ref:b,x:v,y:w,className:t("recharts-text",r),textAnchor:j,fill:l.includes("url")?m6:l}),n.map((a,b)=>{var d=a.words.join(s?"":" ");return f.createElement("tspan",{x:v,dy:0===b?c:g,key:"".concat(d,"-").concat(b)},d)}))});m7.displayName="Text";var m8=["offset"],m9=["labelRef"];function na(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function nb(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function nc(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?nb(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):nb(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function nd(){return(nd=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var ne=a=>null!=a&&"function"==typeof a;function nf(a){var b,{offset:c=5}=a,d=nc({offset:c},na(a,m8)),{viewBox:e,position:g,value:h,children:i,content:j,className:k="",textBreakAll:l,labelRef:m}=d,n=c5(),o=e||n;if(!o||null==h&&null==i&&!(0,f.isValidElement)(j)&&"function"!=typeof j)return null;if((0,f.isValidElement)(j)){var{labelRef:p}=d,q=na(d,m9);return(0,f.cloneElement)(j,q)}if("function"==typeof j){if(b=(0,f.createElement)(j,d),(0,f.isValidElement)(b))return b}else b=(a=>{var{value:b,formatter:c}=a,d=null==a.children?b:a.children;return"function"==typeof c?c(d):d})(d);var r="cx"in o&&B(o.cx),s=l4(d,!0);if(r&&("insideStart"===g||"insideEnd"===g||"end"===g))return((a,b,c)=>{let d,e;var g,h,{position:i,viewBox:j,offset:k,className:l}=a,{cx:m,cy:n,innerRadius:o,outerRadius:p,startAngle:q,endAngle:r,clockWise:s}=j,u=(o+p)/2,v=(d=q,y((e=r)-d)*Math.min(Math.abs(e-d),360)),w=v>=0?1:-1;"insideStart"===i?(g=q+w*k,h=s):"insideEnd"===i?(g=r-w*k,h=!s):"end"===i&&(g=r+w*k,h=s),h=v<=0?h:!h;var x=cD(m,n,u,g),z=cD(m,n,u,g+(h?1:-1)*359),A="M".concat(x.x,",").concat(x.y,"\n    A").concat(u,",").concat(u,",0,1,").concat(+!h,",\n    ").concat(z.x,",").concat(z.y),B=null==a.id?E("recharts-radial-line-"):a.id;return f.createElement("text",nd({},c,{dominantBaseline:"central",className:t("recharts-radial-bar-label",l)}),f.createElement("defs",null,f.createElement("path",{id:B,d:A})),f.createElement("textPath",{xlinkHref:"#".concat(B)},b))})(d,b,s);var u=r?(a=>{var{viewBox:b,offset:c,position:d}=a,{cx:e,cy:f,innerRadius:g,outerRadius:h,startAngle:i,endAngle:j}=b,k=(i+j)/2;if("outside"===d){var{x:l,y:m}=cD(e,f,h+c,k);return{x:l,y:m,textAnchor:l>=e?"start":"end",verticalAnchor:"middle"}}if("center"===d)return{x:e,y:f,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===d)return{x:e,y:f,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===d)return{x:e,y:f,textAnchor:"middle",verticalAnchor:"end"};var{x:n,y:o}=cD(e,f,(g+h)/2,k);return{x:n,y:o,textAnchor:"middle",verticalAnchor:"middle"}})(d):((a,b)=>{var{parentViewBox:c,offset:d,position:e}=a,{x:f,y:g,width:h,height:i}=b,j=i>=0?1:-1,k=j*d,l=j>0?"end":"start",m=j>0?"start":"end",n=h>=0?1:-1,o=n*d,p=n>0?"end":"start",q=n>0?"start":"end";if("top"===e)return nc(nc({},{x:f+h/2,y:g-j*d,textAnchor:"middle",verticalAnchor:l}),c?{height:Math.max(g-c.y,0),width:h}:{});if("bottom"===e)return nc(nc({},{x:f+h/2,y:g+i+k,textAnchor:"middle",verticalAnchor:m}),c?{height:Math.max(c.y+c.height-(g+i),0),width:h}:{});if("left"===e){var r={x:f-o,y:g+i/2,textAnchor:p,verticalAnchor:"middle"};return nc(nc({},r),c?{width:Math.max(r.x-c.x,0),height:i}:{})}if("right"===e){var s={x:f+h+o,y:g+i/2,textAnchor:q,verticalAnchor:"middle"};return nc(nc({},s),c?{width:Math.max(c.x+c.width-s.x,0),height:i}:{})}var t=c?{width:h,height:i}:{};return"insideLeft"===e?nc({x:f+o,y:g+i/2,textAnchor:q,verticalAnchor:"middle"},t):"insideRight"===e?nc({x:f+h-o,y:g+i/2,textAnchor:p,verticalAnchor:"middle"},t):"insideTop"===e?nc({x:f+h/2,y:g+k,textAnchor:"middle",verticalAnchor:m},t):"insideBottom"===e?nc({x:f+h/2,y:g+i-k,textAnchor:"middle",verticalAnchor:l},t):"insideTopLeft"===e?nc({x:f+o,y:g+k,textAnchor:q,verticalAnchor:m},t):"insideTopRight"===e?nc({x:f+h-o,y:g+k,textAnchor:p,verticalAnchor:m},t):"insideBottomLeft"===e?nc({x:f+o,y:g+i-k,textAnchor:q,verticalAnchor:l},t):"insideBottomRight"===e?nc({x:f+h-o,y:g+i-k,textAnchor:p,verticalAnchor:l},t):e&&"object"==typeof e&&(B(e.x)||A(e.x))&&(B(e.y)||A(e.y))?nc({x:f+F(e.x,h),y:g+F(e.y,i),textAnchor:"end",verticalAnchor:"end"},t):nc({x:f+h/2,y:g+i/2,textAnchor:"middle",verticalAnchor:"middle"},t)})(d,o);return f.createElement(m7,nd({ref:m,className:t("recharts-label",k)},s,u,{breakAll:l}),b)}nf.displayName="Label";var ng=a=>{var{cx:b,cy:c,angle:d,startAngle:e,endAngle:f,r:g,radius:h,innerRadius:i,outerRadius:j,x:k,y:l,top:m,left:n,width:o,height:p,clockWise:q,labelViewBox:r}=a;if(r)return r;if(B(o)&&B(p)){if(B(k)&&B(l))return{x:k,y:l,width:o,height:p};if(B(m)&&B(n))return{x:m,y:n,width:o,height:p}}return B(k)&&B(l)?{x:k,y:l,width:0,height:0}:B(b)&&B(c)?{cx:b,cy:c,startAngle:e||d||0,endAngle:f||d||0,innerRadius:i||0,outerRadius:j||h||g||0,clockWise:q}:a.viewBox?a.viewBox:void 0};nf.parseViewBox=ng,nf.renderCallByParent=function(a,b){var c=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!a||!a.children&&c&&!a.label)return null;var{children:d,labelRef:e}=a,g=ng(a),h=l3(d,nf).map((a,c)=>(0,f.cloneElement)(a,{viewBox:b||g,key:"label-".concat(c)}));return c?[((a,b,c)=>{if(!a)return null;var d={viewBox:b,labelRef:c};return!0===a?f.createElement(nf,nd({key:"label-implicit"},d)):C(a)?f.createElement(nf,nd({key:"label-implicit",value:a},d)):(0,f.isValidElement)(a)?a.type===nf?(0,f.cloneElement)(a,nc({key:"label-implicit"},d)):f.createElement(nf,nd({key:"label-implicit",content:a},d)):ne(a)?f.createElement(nf,nd({key:"label-implicit",content:a},d)):a&&"object"==typeof a?f.createElement(nf,nd({},a,{key:"label-implicit"},d)):null})(a.label,b||g,e),...h]:h};var nh=["viewBox"],ni=["viewBox"];function nj(){return(nj=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function nk(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function nl(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?nk(Object(c),!0).forEach(function(b){nn(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):nk(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function nm(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function nn(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}class no extends f.Component{constructor(a){super(a),this.tickRefs=f.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}shouldComponentUpdate(a,b){var{viewBox:c}=a,d=nm(a,nh),e=this.props,{viewBox:f}=e,g=nm(e,ni);return!mN(c,f)||!mN(d,g)||!mN(b,this.state)}getTickLineCoord(a){var b,c,d,e,f,g,{x:h,y:i,width:j,height:k,orientation:l,tickSize:m,mirror:n,tickMargin:o}=this.props,p=n?-1:1,q=a.tickSize||m,r=B(a.tickCoord)?a.tickCoord:a.coordinate;switch(l){case"top":b=c=a.coordinate,g=(d=(e=i+!n*k)-p*q)-p*o,f=r;break;case"left":d=e=a.coordinate,f=(b=(c=h+!n*j)-p*q)-p*o,g=r;break;case"right":d=e=a.coordinate,f=(b=(c=h+n*j)+p*q)+p*o,g=r;break;default:b=c=a.coordinate,g=(d=(e=i+n*k)+p*q)+p*o,f=r}return{line:{x1:b,y1:d,x2:c,y2:e},tick:{x:f,y:g}}}getTickTextAnchor(){var a,{orientation:b,mirror:c}=this.props;switch(b){case"left":a=c?"start":"end";break;case"right":a=c?"end":"start";break;default:a="middle"}return a}getTickVerticalAnchor(){var{orientation:a,mirror:b}=this.props;switch(a){case"left":case"right":return"middle";case"top":return b?"start":"end";default:return b?"end":"start"}}renderAxisLine(){var{x:a,y:b,width:c,height:d,orientation:e,mirror:g,axisLine:h}=this.props,i=nl(nl(nl({},l4(this.props,!1)),l4(h,!1)),{},{fill:"none"});if("top"===e||"bottom"===e){var j=+("top"===e&&!g||"bottom"===e&&g);i=nl(nl({},i),{},{x1:a,y1:b+j*d,x2:a+c,y2:b+j*d})}else{var k=+("left"===e&&!g||"right"===e&&g);i=nl(nl({},i),{},{x1:a+k*c,y1:b,x2:a+k*c,y2:b+d})}return f.createElement("line",nj({},i,{className:t("recharts-cartesian-axis-line",x()(h,"className"))}))}static renderTickItem(a,b,c){var d,e=t(b.className,"recharts-cartesian-axis-tick-value");if(f.isValidElement(a))d=f.cloneElement(a,nl(nl({},b),{},{className:e}));else if("function"==typeof a)d=a(nl(nl({},b),{},{className:e}));else{var g="recharts-cartesian-axis-tick-value";"boolean"!=typeof a&&(g=t(g,a.className)),d=f.createElement(m7,nj({},b,{className:g}),c)}return d}renderTicks(a,b){var c=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:d,stroke:e,tick:g,tickFormatter:h,unit:i}=this.props,j=mM(nl(nl({},this.props),{},{ticks:c}),a,b),k=this.getTickTextAnchor(),l=this.getTickVerticalAnchor(),m=l4(this.props,!1),n=l4(g,!1),o=nl(nl({},m),{},{fill:"none"},l4(d,!1)),p=j.map((a,b)=>{var{line:c,tick:p}=this.getTickLineCoord(a),q=nl(nl(nl(nl({textAnchor:k,verticalAnchor:l},m),{},{stroke:"none",fill:e},n),p),{},{index:b,payload:a,visibleTicksCount:j.length,tickFormatter:h});return f.createElement(mQ,nj({className:"recharts-cartesian-axis-tick",key:"tick-".concat(a.value,"-").concat(a.coordinate,"-").concat(a.tickCoord)},l$(this.props,a,b)),d&&f.createElement("line",nj({},o,c,{className:t("recharts-cartesian-axis-tick-line",x()(d,"className"))})),g&&no.renderTickItem(g,q,"".concat("function"==typeof h?h(a.value,b):a.value).concat(i||"")))});return p.length>0?f.createElement("g",{className:"recharts-cartesian-axis-ticks"},p):null}render(){var{axisLine:a,width:b,height:c,className:d,hide:e}=this.props;if(e)return null;var{ticks:g}=this.props;return null!=b&&b<=0||null!=c&&c<=0?null:f.createElement(mQ,{className:t("recharts-cartesian-axis",d),ref:a=>{if(a){var b=a.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(b);var c=b[0];if(c){var d=window.getComputedStyle(c).fontSize,e=window.getComputedStyle(c).letterSpacing;(d!==this.state.fontSize||e!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(c).fontSize,letterSpacing:window.getComputedStyle(c).letterSpacing})}}}},a&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,g),nf.renderCallByParent(this.props))}}nn(no,"displayName","CartesianAxis"),nn(no,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var np=["x1","y1","x2","y2","key"],nq=["offset"],nr=["xAxisId","yAxisId"],ns=["xAxisId","yAxisId"];function nt(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function nu(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?nt(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):nt(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function nv(){return(nv=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function nw(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var nx=a=>{var{fill:b}=a;if(!b||"none"===b)return null;var{fillOpacity:c,x:d,y:e,width:g,height:h,ry:i}=a;return f.createElement("rect",{x:d,y:e,ry:i,width:g,height:h,stroke:"none",fill:b,fillOpacity:c,className:"recharts-cartesian-grid-bg"})};function ny(a,b){var c;if(f.isValidElement(a))c=f.cloneElement(a,b);else if("function"==typeof a)c=a(b);else{var{x1:d,y1:e,x2:g,y2:h,key:i}=b,j=l4(nw(b,np),!1),{offset:k}=j,l=nw(j,nq);c=f.createElement("line",nv({},l,{x1:d,y1:e,x2:g,y2:h,fill:"none",key:i}))}return c}function nz(a){var{x:b,width:c,horizontal:d=!0,horizontalPoints:e}=a;if(!d||!e||!e.length)return null;var{xAxisId:g,yAxisId:h}=a,i=nw(a,nr),j=e.map((a,e)=>ny(d,nu(nu({},i),{},{x1:b,y1:a,x2:b+c,y2:a,key:"line-".concat(e),index:e})));return f.createElement("g",{className:"recharts-cartesian-grid-horizontal"},j)}function nA(a){var{y:b,height:c,vertical:d=!0,verticalPoints:e}=a;if(!d||!e||!e.length)return null;var{xAxisId:g,yAxisId:h}=a,i=nw(a,ns),j=e.map((a,e)=>ny(d,nu(nu({},i),{},{x1:a,y1:b,x2:a,y2:b+c,key:"line-".concat(e),index:e})));return f.createElement("g",{className:"recharts-cartesian-grid-vertical"},j)}function nB(a){var{horizontalFill:b,fillOpacity:c,x:d,y:e,width:g,height:h,horizontalPoints:i,horizontal:j=!0}=a;if(!j||!b||!b.length)return null;var k=i.map(a=>Math.round(a+e-e)).sort((a,b)=>a-b);e!==k[0]&&k.unshift(0);var l=k.map((a,i)=>{var j=k[i+1]?k[i+1]-a:e+h-a;if(j<=0)return null;var l=i%b.length;return f.createElement("rect",{key:"react-".concat(i),y:a,x:d,height:j,width:g,stroke:"none",fill:b[l],fillOpacity:c,className:"recharts-cartesian-grid-bg"})});return f.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},l)}function nC(a){var{vertical:b=!0,verticalFill:c,fillOpacity:d,x:e,y:g,width:h,height:i,verticalPoints:j}=a;if(!b||!c||!c.length)return null;var k=j.map(a=>Math.round(a+e-e)).sort((a,b)=>a-b);e!==k[0]&&k.unshift(0);var l=k.map((a,b)=>{var j=k[b+1]?k[b+1]-a:e+h-a;if(j<=0)return null;var l=b%c.length;return f.createElement("rect",{key:"react-".concat(b),x:a,y:g,width:j,height:i,stroke:"none",fill:c[l],fillOpacity:d,className:"recharts-cartesian-grid-bg"})});return f.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},l)}var nD=(a,b)=>{var{xAxis:c,width:d,height:e,offset:f}=a;return cI(mM(nu(nu(nu({},no.defaultProps),c),{},{ticks:cJ(c,!0),viewBox:{x:0,y:0,width:d,height:e}})),f.left,f.left+f.width,b)},nE=(a,b)=>{var{yAxis:c,width:d,height:e,offset:f}=a;return cI(mM(nu(nu(nu({},no.defaultProps),c),{},{ticks:cJ(c,!0),viewBox:{x:0,y:0,width:d,height:e}})),f.top,f.top+f.height,b)},nF={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function nG(a){var b=c8(),c=c9(),d=c7(),e=nu(nu({},ms(a,nF)),{},{x:B(a.x)?a.x:d.left,y:B(a.y)?a.y:d.top,width:B(a.width)?a.width:d.width,height:B(a.height)?a.height:d.height}),{xAxisId:g,yAxisId:h,x:i,y:j,width:k,height:l,syncWithTicks:m,horizontalValues:n,verticalValues:o}=e,p=c2(),q=cp(a=>jx(a,"xAxis",g,p)),r=cp(a=>jx(a,"yAxis",h,p));if(!B(k)||k<=0||!B(l)||l<=0||!B(i)||i!==+i||!B(j)||j!==+j)return null;var s=e.verticalCoordinatesGenerator||nD,t=e.horizontalCoordinatesGenerator||nE,{horizontalPoints:u,verticalPoints:v}=e;if((!u||!u.length)&&"function"==typeof t){var w=n&&n.length,x=t({yAxis:r?nu(nu({},r),{},{ticks:w?n:r.ticks}):void 0,width:b,height:c,offset:d},!!w||m);K(Array.isArray(x),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof x,"]")),Array.isArray(x)&&(u=x)}if((!v||!v.length)&&"function"==typeof s){var y=o&&o.length,z=s({xAxis:q?nu(nu({},q),{},{ticks:y?o:q.ticks}):void 0,width:b,height:c,offset:d},!!y||m);K(Array.isArray(z),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof z,"]")),Array.isArray(z)&&(v=z)}return f.createElement("g",{className:"recharts-cartesian-grid"},f.createElement(nx,{fill:e.fill,fillOpacity:e.fillOpacity,x:e.x,y:e.y,width:e.width,height:e.height,ry:e.ry}),f.createElement(nB,nv({},e,{horizontalPoints:u})),f.createElement(nC,nv({},e,{verticalPoints:v})),f.createElement(nz,nv({},e,{offset:d,horizontalPoints:u,xAxis:q,yAxis:r})),f.createElement(nA,nv({},e,{offset:d,verticalPoints:v,xAxis:q,yAxis:r})))}nG.displayName="CartesianGrid";var nH=["children"],nI=["dangerouslySetInnerHTML","ticks"];function nJ(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function nK(){return(nK=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function nL(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function nM(a){cl();var b=(0,f.useMemo)(()=>{var{children:b}=a;return nL(a,nH)},[a]),c=cp(a=>id(a,b.id));return b===c?a.children:null}var nN=a=>{var{xAxisId:b,className:c}=a,d=cp(c0),e=c2(),g="xAxis",h=cp(a=>ji(a,g,b,e)),i=cp(a=>jy(a,g,b,e)),j=cp(a=>jp(a,b)),k=cp(a=>((a,b)=>{var c=c$(a),d=id(a,b);if(null!=d){var e=jq(a,d.orientation,d.mirror)[b];return null==e?{x:c.left,y:0}:{x:c.left,y:e}}})(a,b));if(null==j||null==k)return null;var{dangerouslySetInnerHTML:l,ticks:m}=a,n=nL(a,nI);return f.createElement(no,nK({},n,{scale:h,x:k.x,y:k.y,width:j.width,height:j.height,className:t("recharts-".concat(g," ").concat(g),c),viewBox:d,ticks:i}))},nO=a=>{var b,c,d,e,g;return f.createElement(nM,{interval:null!=(b=a.interval)?b:"preserveEnd",id:a.xAxisId,scale:a.scale,type:a.type,padding:a.padding,allowDataOverflow:a.allowDataOverflow,domain:a.domain,dataKey:a.dataKey,allowDuplicatedCategory:a.allowDuplicatedCategory,allowDecimals:a.allowDecimals,tickCount:a.tickCount,includeHidden:null!=(c=a.includeHidden)&&c,reversed:a.reversed,ticks:a.ticks,height:a.height,orientation:a.orientation,mirror:a.mirror,hide:a.hide,unit:a.unit,name:a.name,angle:null!=(d=a.angle)?d:0,minTickGap:null!=(e=a.minTickGap)?e:5,tick:null==(g=a.tick)||g,tickFormatter:a.tickFormatter},f.createElement(nN,a))};class nP extends f.Component{render(){return f.createElement(nO,this.props)}}nJ(nP,"displayName","XAxis"),nJ(nP,"defaultProps",{allowDataOverflow:ic.allowDataOverflow,allowDecimals:ic.allowDecimals,allowDuplicatedCategory:ic.allowDuplicatedCategory,height:ic.height,hide:!1,mirror:ic.mirror,orientation:ic.orientation,padding:ic.padding,reversed:ic.reversed,scale:ic.scale,tickCount:ic.tickCount,type:ic.type,xAxisId:0});var nQ=["dangerouslySetInnerHTML","ticks"];function nR(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function nS(){return(nS=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function nT(a){return cl(),null}var nU=a=>{var b,{yAxisId:c,className:d,width:e,label:g}=a,h=(0,f.useRef)(null),i=(0,f.useRef)(null),j=cp(c0),k=c2(),l=cl(),m="yAxis",n=cp(a=>ji(a,m,c,k)),o=cp(a=>js(a,c)),p=cp(a=>((a,b)=>{var c=c$(a),d=ig(a,b);if(null!=d){var e=jr(a,d.orientation,d.mirror)[b];return null==e?{x:0,y:c.top}:{x:e,y:c.top}}})(a,c)),q=cp(a=>jy(a,m,c,k));if((0,f.useLayoutEffect)(()=>{if(!("auto"!==e||!o||ne(g)||(0,f.isValidElement)(g))){var a,b=h.current,d=null==b||null==(a=b.tickRefs)?void 0:a.current,{tickSize:j,tickMargin:k}=b.props,m=(a=>{var{ticks:b,label:c,labelGapWithTick:d=5,tickSize:e=0,tickMargin:f=0}=a,g=0;if(b){b.forEach(a=>{if(a){var b=a.getBoundingClientRect();b.width>g&&(g=b.width)}});var h=c?c.getBoundingClientRect().width:0;return Math.round(g+(e+f)+h+(c?d:0))}return 0})({ticks:d,label:i.current,labelGapWithTick:5,tickSize:j,tickMargin:k});Math.round(o.width)!==Math.round(m)&&l(k0({id:c,width:m}))}},[h,null==h||null==(b=h.current)||null==(b=b.tickRefs)?void 0:b.current,null==o?void 0:o.width,o,l,g,c,e]),null==o||null==p)return null;var{dangerouslySetInnerHTML:r,ticks:s}=a,u=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,nQ);return f.createElement(no,nS({},u,{ref:h,labelRef:i,scale:n,x:p.x,y:p.y,width:o.width,height:o.height,className:t("recharts-".concat(m," ").concat(m),d),viewBox:j,ticks:q}))},nV=a=>{var b,c,d,e,g;return f.createElement(f.Fragment,null,f.createElement(nT,{interval:null!=(b=a.interval)?b:"preserveEnd",id:a.yAxisId,scale:a.scale,type:a.type,domain:a.domain,allowDataOverflow:a.allowDataOverflow,dataKey:a.dataKey,allowDuplicatedCategory:a.allowDuplicatedCategory,allowDecimals:a.allowDecimals,tickCount:a.tickCount,padding:a.padding,includeHidden:null!=(c=a.includeHidden)&&c,reversed:a.reversed,ticks:a.ticks,width:a.width,orientation:a.orientation,mirror:a.mirror,hide:a.hide,unit:a.unit,name:a.name,angle:null!=(d=a.angle)?d:0,minTickGap:null!=(e=a.minTickGap)?e:5,tick:null==(g=a.tick)||g,tickFormatter:a.tickFormatter}),f.createElement(nU,a))},nW={allowDataOverflow:ie.allowDataOverflow,allowDecimals:ie.allowDecimals,allowDuplicatedCategory:ie.allowDuplicatedCategory,hide:!1,mirror:ie.mirror,orientation:ie.orientation,padding:ie.padding,reversed:ie.reversed,scale:ie.scale,tickCount:ie.tickCount,type:ie.type,width:ie.width,yAxisId:0};class nX extends f.Component{render(){return f.createElement(nV,this.props)}}nR(nX,"displayName","YAxis"),nR(nX,"defaultProps",nW);var nY=c(1215);function nZ(){return(nZ=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function n$(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function n_(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?n$(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):n$(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function n0(a){return Array.isArray(a)&&C(a[0])&&C(a[1])?a.join(" ~ "):a}var n1=a=>{var{separator:b=" : ",contentStyle:c={},itemStyle:d={},labelStyle:e={},payload:g,formatter:h,itemSorter:i,wrapperClassName:j,labelClassName:k,label:l,labelFormatter:m,accessibilityLayer:n=!1}=a,o=n_({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},c),p=n_({margin:0},e),q=null!=l,r=q?l:"",s=t("recharts-default-tooltip",j),u=t("recharts-tooltip-label",k);return q&&m&&null!=g&&(r=m(l,g)),f.createElement("div",nZ({className:s,style:o},n?{role:"status","aria-live":"assertive"}:{}),f.createElement("p",{className:u,style:p},f.isValidElement(r)?r:"".concat(r)),(()=>{if(g&&g.length){var a=(i?cr()(g,i):g).map((a,c)=>{if("none"===a.type)return null;var e=a.formatter||h||n0,{value:i,name:j}=a,k=i,l=j;if(e){var m=e(i,j,a,c,g);if(Array.isArray(m))[k,l]=m;else{if(null==m)return null;k=m}}var n=n_({display:"block",paddingTop:4,paddingBottom:4,color:a.color||"#000"},d);return f.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(c),style:n},C(l)?f.createElement("span",{className:"recharts-tooltip-item-name"},l):null,C(l)?f.createElement("span",{className:"recharts-tooltip-item-separator"},b):null,f.createElement("span",{className:"recharts-tooltip-item-value"},k),f.createElement("span",{className:"recharts-tooltip-item-unit"},a.unit||""))});return f.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},a)}return null})())},n2="recharts-tooltip-wrapper",n3={visibility:"hidden"};function n4(a){var{allowEscapeViewBox:b,coordinate:c,key:d,offsetTopLeft:e,position:f,reverseDirection:g,tooltipDimension:h,viewBox:i,viewBoxDimension:j}=a;if(f&&B(f[d]))return f[d];var k=c[d]-h-(e>0?e:0),l=c[d]+e;if(b[d])return g[d]?k:l;var m=i[d];return null==m?0:g[d]?k<m?Math.max(l,m):Math.max(k,m):null==j?0:l+h>m+j?Math.max(k,m):Math.max(l,m)}function n5(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function n6(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?n5(Object(c),!0).forEach(function(b){n7(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):n5(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function n7(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}class n8 extends f.PureComponent{constructor(){super(...arguments),n7(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),n7(this,"handleKeyDown",a=>{if("Escape"===a.key){var b,c,d,e;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(b=null==(c=this.props.coordinate)?void 0:c.x)?b:0,y:null!=(d=null==(e=this.props.coordinate)?void 0:e.y)?d:0}})}})}componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var a,b;this.state.dismissed&&((null==(a=this.props.coordinate)?void 0:a.x)!==this.state.dismissedAtCoordinate.x||(null==(b=this.props.coordinate)?void 0:b.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:a,allowEscapeViewBox:b,animationDuration:c,animationEasing:d,children:e,coordinate:g,hasPayload:h,isAnimationActive:i,offset:j,position:k,reverseDirection:l,useTranslate3d:m,viewBox:n,wrapperStyle:o,lastBoundingBox:p,innerRef:q,hasPortalFromProps:r}=this.props,{cssClasses:s,cssProperties:u}=function(a){var b,c,d,{allowEscapeViewBox:e,coordinate:f,offsetTopLeft:g,position:h,reverseDirection:i,tooltipBox:j,useTranslate3d:k,viewBox:l}=a;return{cssProperties:b=j.height>0&&j.width>0&&f?function(a){var{translateX:b,translateY:c,useTranslate3d:d}=a;return{transform:d?"translate3d(".concat(b,"px, ").concat(c,"px, 0)"):"translate(".concat(b,"px, ").concat(c,"px)")}}({translateX:c=n4({allowEscapeViewBox:e,coordinate:f,key:"x",offsetTopLeft:g,position:h,reverseDirection:i,tooltipDimension:j.width,viewBox:l,viewBoxDimension:l.width}),translateY:d=n4({allowEscapeViewBox:e,coordinate:f,key:"y",offsetTopLeft:g,position:h,reverseDirection:i,tooltipDimension:j.height,viewBox:l,viewBoxDimension:l.height}),useTranslate3d:k}):n3,cssClasses:function(a){var{coordinate:b,translateX:c,translateY:d}=a;return t(n2,{["".concat(n2,"-right")]:B(c)&&b&&B(b.x)&&c>=b.x,["".concat(n2,"-left")]:B(c)&&b&&B(b.x)&&c<b.x,["".concat(n2,"-bottom")]:B(d)&&b&&B(b.y)&&d>=b.y,["".concat(n2,"-top")]:B(d)&&b&&B(b.y)&&d<b.y})}({translateX:c,translateY:d,coordinate:f})}}({allowEscapeViewBox:b,coordinate:g,offsetTopLeft:j,position:k,reverseDirection:l,tooltipBox:{height:p.height,width:p.width},useTranslate3d:m,viewBox:n}),v=r?{}:n6(n6({transition:i&&a?"transform ".concat(c,"ms ").concat(d):void 0},u),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&h?"visible":"hidden",position:"absolute",top:0,left:0}),w=n6(n6({},v),{},{visibility:!this.state.dismissed&&a&&h?"visible":"hidden"},o);return f.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:s,style:w,ref:q},e)}}var n9=c(3854),oa=c.n(n9);function ob(a,b,c){return!0===b?oa()(a,c):"function"==typeof b?oa()(a,b):a}function oc(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[b,c]=(0,f.useState)({height:0,left:0,top:0,width:0}),d=(0,f.useCallback)(a=>{if(null!=a){var d=a.getBoundingClientRect(),e={height:d.height,left:d.left,top:d.top,width:d.width};(Math.abs(e.height-b.height)>1||Math.abs(e.left-b.left)>1||Math.abs(e.top-b.top)>1||Math.abs(e.width-b.width)>1)&&c({height:e.height,left:e.left,top:e.top,width:e.width})}},[b.width,b.height,b.top,b.left,...a]);return[b,d]}function od(){}function oe(a,b,c){a._context.bezierCurveTo((2*a._x0+a._x1)/3,(2*a._y0+a._y1)/3,(a._x0+2*a._x1)/3,(a._y0+2*a._y1)/3,(a._x0+4*a._x1+b)/6,(a._y0+4*a._y1+c)/6)}function of(a){this._context=a}function og(a){this._context=a}function oh(a){this._context=a}of.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:oe(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:oe(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}},og.prototype={areaStart:od,areaEnd:od,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._x2=a,this._y2=b;break;case 1:this._point=2,this._x3=a,this._y3=b;break;case 2:this._point=3,this._x4=a,this._y4=b,this._context.moveTo((this._x0+4*this._x1+a)/6,(this._y0+4*this._y1+b)/6);break;default:oe(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}},oh.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var c=(this._x0+4*this._x1+a)/6,d=(this._y0+4*this._y1+b)/6;this._line?this._context.lineTo(c,d):this._context.moveTo(c,d);break;case 3:this._point=4;default:oe(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}};class oi{constructor(a,b){this._context=a,this._x=b}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+a)/2,this._y0,this._x0,b,a,b):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+b)/2,a,this._y0,a,b)}this._x0=a,this._y0=b}}function oj(a){this._context=a}function ok(a){this._context=a}function ol(a){return new ok(a)}oj.prototype={areaStart:od,areaEnd:od,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(a,b){a*=1,b*=1,this._point?this._context.lineTo(a,b):(this._point=1,this._context.moveTo(a,b))}};function om(a,b,c){var d=a._x1-a._x0,e=b-a._x1,f=(a._y1-a._y0)/(d||e<0&&-0),g=(c-a._y1)/(e||d<0&&-0);return((f<0?-1:1)+(g<0?-1:1))*Math.min(Math.abs(f),Math.abs(g),.5*Math.abs((f*e+g*d)/(d+e)))||0}function on(a,b){var c=a._x1-a._x0;return c?(3*(a._y1-a._y0)/c-b)/2:b}function oo(a,b,c){var d=a._x0,e=a._y0,f=a._x1,g=a._y1,h=(f-d)/3;a._context.bezierCurveTo(d+h,e+h*b,f-h,g-h*c,f,g)}function op(a){this._context=a}function oq(a){this._context=new or(a)}function or(a){this._context=a}function os(a){this._context=a}function ot(a){var b,c,d=a.length-1,e=Array(d),f=Array(d),g=Array(d);for(e[0]=0,f[0]=2,g[0]=a[0]+2*a[1],b=1;b<d-1;++b)e[b]=1,f[b]=4,g[b]=4*a[b]+2*a[b+1];for(e[d-1]=2,f[d-1]=7,g[d-1]=8*a[d-1]+a[d],b=1;b<d;++b)c=e[b]/f[b-1],f[b]-=c,g[b]-=c*g[b-1];for(e[d-1]=g[d-1]/f[d-1],b=d-2;b>=0;--b)e[b]=(g[b]-e[b+1])/f[b];for(b=0,f[d-1]=(a[d]+e[d-1])/2;b<d-1;++b)f[b]=2*a[b+1]-e[b+1];return[e,f]}function ou(a,b){this._context=a,this._t=b}ok.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:this._context.lineTo(a,b)}}},op.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:oo(this,this._t0,on(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){var c=NaN;if(b*=1,(a*=1)!==this._x1||b!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;break;case 2:this._point=3,oo(this,on(this,c=om(this,a,b)),c);break;default:oo(this,this._t0,c=om(this,a,b))}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b,this._t0=c}}},(oq.prototype=Object.create(op.prototype)).point=function(a,b){op.prototype.point.call(this,b,a)},or.prototype={moveTo:function(a,b){this._context.moveTo(b,a)},closePath:function(){this._context.closePath()},lineTo:function(a,b){this._context.lineTo(b,a)},bezierCurveTo:function(a,b,c,d,e,f){this._context.bezierCurveTo(b,a,d,c,f,e)}},os.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var a=this._x,b=this._y,c=a.length;if(c)if(this._line?this._context.lineTo(a[0],b[0]):this._context.moveTo(a[0],b[0]),2===c)this._context.lineTo(a[1],b[1]);else for(var d=ot(a),e=ot(b),f=0,g=1;g<c;++f,++g)this._context.bezierCurveTo(d[0][f],e[0][f],d[1][f],e[1][f],a[g],b[g]);(this._line||0!==this._line&&1===c)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(a,b){this._x.push(+a),this._y.push(+b)}},ou.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,b),this._context.lineTo(a,b);else{var c=this._x*(1-this._t)+a*this._t;this._context.lineTo(c,this._y),this._context.lineTo(c,b)}}this._x=a,this._y=b}};let ov=Math.PI,ow=2*ov,ox=ow-1e-6;function oy(a){this._+=a[0];for(let b=1,c=a.length;b<c;++b)this._+=arguments[b]+a[b]}class oz{constructor(a){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==a?oy:function(a){let b=Math.floor(a);if(!(b>=0))throw Error(`invalid digits: ${a}`);if(b>15)return oy;let c=10**b;return function(a){this._+=a[0];for(let b=1,d=a.length;b<d;++b)this._+=Math.round(arguments[b]*c)/c+a[b]}}(a)}moveTo(a,b){this._append`M${this._x0=this._x1=+a},${this._y0=this._y1=+b}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(a,b){this._append`L${this._x1=+a},${this._y1=+b}`}quadraticCurveTo(a,b,c,d){this._append`Q${+a},${+b},${this._x1=+c},${this._y1=+d}`}bezierCurveTo(a,b,c,d,e,f){this._append`C${+a},${+b},${+c},${+d},${this._x1=+e},${this._y1=+f}`}arcTo(a,b,c,d,e){if(a*=1,b*=1,c*=1,d*=1,(e*=1)<0)throw Error(`negative radius: ${e}`);let f=this._x1,g=this._y1,h=c-a,i=d-b,j=f-a,k=g-b,l=j*j+k*k;if(null===this._x1)this._append`M${this._x1=a},${this._y1=b}`;else if(l>1e-6)if(Math.abs(k*h-i*j)>1e-6&&e){let m=c-f,n=d-g,o=h*h+i*i,p=Math.sqrt(o),q=Math.sqrt(l),r=e*Math.tan((ov-Math.acos((o+l-(m*m+n*n))/(2*p*q)))/2),s=r/q,t=r/p;Math.abs(s-1)>1e-6&&this._append`L${a+s*j},${b+s*k}`,this._append`A${e},${e},0,0,${+(k*m>j*n)},${this._x1=a+t*h},${this._y1=b+t*i}`}else this._append`L${this._x1=a},${this._y1=b}`}arc(a,b,c,d,e,f){if(a*=1,b*=1,c*=1,f=!!f,c<0)throw Error(`negative radius: ${c}`);let g=c*Math.cos(d),h=c*Math.sin(d),i=a+g,j=b+h,k=1^f,l=f?d-e:e-d;null===this._x1?this._append`M${i},${j}`:(Math.abs(this._x1-i)>1e-6||Math.abs(this._y1-j)>1e-6)&&this._append`L${i},${j}`,c&&(l<0&&(l=l%ow+ow),l>ox?this._append`A${c},${c},0,1,${k},${a-g},${b-h}A${c},${c},0,1,${k},${this._x1=i},${this._y1=j}`:l>1e-6&&this._append`A${c},${c},0,${+(l>=ov)},${k},${this._x1=a+c*Math.cos(e)},${this._y1=b+c*Math.sin(e)}`)}rect(a,b,c,d){this._append`M${this._x0=this._x1=+a},${this._y0=this._y1=+b}h${c*=1}v${+d}h${-c}Z`}toString(){return this._}}function oA(a){let b=3;return a.digits=function(c){if(!arguments.length)return b;if(null==c)b=null;else{let a=Math.floor(c);if(!(a>=0))throw RangeError(`invalid digits: ${c}`);b=a}return a},()=>new oz(b)}function oB(a){return a[0]}function oC(a){return a[1]}function oD(a,b){var c=cw(!0),d=null,e=ol,f=null,g=oA(h);function h(h){var i,j,k,l=(h=cv(h)).length,m=!1;for(null==d&&(f=e(k=g())),i=0;i<=l;++i)!(i<l&&c(j=h[i],i,h))===m&&((m=!m)?f.lineStart():f.lineEnd()),m&&f.point(+a(j,i,h),+b(j,i,h));if(k)return f=null,k+""||null}return a="function"==typeof a?a:void 0===a?oB:cw(a),b="function"==typeof b?b:void 0===b?oC:cw(b),h.x=function(b){return arguments.length?(a="function"==typeof b?b:cw(+b),h):a},h.y=function(a){return arguments.length?(b="function"==typeof a?a:cw(+a),h):b},h.defined=function(a){return arguments.length?(c="function"==typeof a?a:cw(!!a),h):c},h.curve=function(a){return arguments.length?(e=a,null!=d&&(f=e(d)),h):e},h.context=function(a){return arguments.length?(null==a?d=f=null:f=e(d=a),h):d},h}function oE(a,b,c){var d=null,e=cw(!0),f=null,g=ol,h=null,i=oA(j);function j(j){var k,l,m,n,o,p=(j=cv(j)).length,q=!1,r=Array(p),s=Array(p);for(null==f&&(h=g(o=i())),k=0;k<=p;++k){if(!(k<p&&e(n=j[k],k,j))===q)if(q=!q)l=k,h.areaStart(),h.lineStart();else{for(h.lineEnd(),h.lineStart(),m=k-1;m>=l;--m)h.point(r[m],s[m]);h.lineEnd(),h.areaEnd()}q&&(r[k]=+a(n,k,j),s[k]=+b(n,k,j),h.point(d?+d(n,k,j):r[k],c?+c(n,k,j):s[k]))}if(o)return h=null,o+""||null}function k(){return oD().defined(e).curve(g).context(f)}return a="function"==typeof a?a:void 0===a?oB:cw(+a),b="function"==typeof b?b:void 0===b?cw(0):cw(+b),c="function"==typeof c?c:void 0===c?oC:cw(+c),j.x=function(b){return arguments.length?(a="function"==typeof b?b:cw(+b),d=null,j):a},j.x0=function(b){return arguments.length?(a="function"==typeof b?b:cw(+b),j):a},j.x1=function(a){return arguments.length?(d=null==a?null:"function"==typeof a?a:cw(+a),j):d},j.y=function(a){return arguments.length?(b="function"==typeof a?a:cw(+a),c=null,j):b},j.y0=function(a){return arguments.length?(b="function"==typeof a?a:cw(+a),j):b},j.y1=function(a){return arguments.length?(c=null==a?null:"function"==typeof a?a:cw(+a),j):c},j.lineX0=j.lineY0=function(){return k().x(a).y(b)},j.lineY1=function(){return k().x(a).y(c)},j.lineX1=function(){return k().x(d).y(b)},j.defined=function(a){return arguments.length?(e="function"==typeof a?a:cw(!!a),j):e},j.curve=function(a){return arguments.length?(g=a,null!=f&&(h=g(f)),j):g},j.context=function(a){return arguments.length?(null==a?f=h=null:h=g(f=a),j):f},j}function oF(){return(oF=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function oG(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function oH(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?oG(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):oG(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}oz.prototype;var oI={curveBasisClosed:function(a){return new og(a)},curveBasisOpen:function(a){return new oh(a)},curveBasis:function(a){return new of(a)},curveBumpX:function(a){return new oi(a,!0)},curveBumpY:function(a){return new oi(a,!1)},curveLinearClosed:function(a){return new oj(a)},curveLinear:ol,curveMonotoneX:function(a){return new op(a)},curveMonotoneY:function(a){return new oq(a)},curveNatural:function(a){return new os(a)},curveStep:function(a){return new ou(a,.5)},curveStepAfter:function(a){return new ou(a,1)},curveStepBefore:function(a){return new ou(a,0)}},oJ=a=>gW(a.x)&&gW(a.y),oK=a=>a.x,oL=a=>a.y,oM=a=>{var{className:b,points:c,path:d,pathRef:e}=a;if((!c||!c.length)&&!d)return null;var g=c&&c.length?(a=>{var b,{type:c="linear",points:d=[],baseLine:e,layout:f,connectNulls:g=!1}=a,h=((a,b)=>{if("function"==typeof a)return a;var c="curve".concat(J(a));return("curveMonotone"===c||"curveBump"===c)&&b?oI["".concat(c).concat("vertical"===b?"Y":"X")]:oI[c]||ol})(c,f),i=g?d.filter(oJ):d;if(Array.isArray(e)){var j=g?e.filter(a=>oJ(a)):e,k=i.map((a,b)=>oH(oH({},a),{},{base:j[b]}));return(b="vertical"===f?oE().y(oL).x1(oK).x0(a=>a.base.x):oE().x(oK).y1(oL).y0(a=>a.base.y)).defined(oJ).curve(h),b(k)}return(b="vertical"===f&&B(e)?oE().y(oL).x1(oK).x0(e):B(e)?oE().x(oK).y1(oL).y0(e):oD().x(oK).y(oL)).defined(oJ).curve(h),b(i)})(a):d;return f.createElement("path",oF({},l4(a,!1),((a,b)=>{if(!a||"function"==typeof a||"boolean"==typeof a)return null;var c=a;if((0,f.isValidElement)(a)&&(c=a.props),"object"!=typeof c&&"function"!=typeof c)return null;var d={};return Object.keys(c).forEach(a=>{lZ.includes(a)&&(d[a]=b||(b=>c[a](c,b)))}),d})(a),{className:t("recharts-curve",b),d:null===g?void 0:g,ref:e}))},oN=["x","y","top","left","width","height","className"];function oO(){return(oO=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function oP(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}var oQ=a=>{var{x:b=0,y:c=0,top:d=0,left:e=0,width:g=0,height:h=0,className:i}=a,j=function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?oP(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):oP(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({x:b,y:c,top:d,left:e,width:g,height:h},function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,oN));return B(b)&&B(c)&&B(g)&&B(h)&&B(d)&&B(e)?f.createElement("path",oO({},l4(j,!0),{className:t("recharts-cross",i),d:"M".concat(b,",").concat(d,"v").concat(h,"M").concat(e,",").concat(c,"h").concat(g)})):null},oR=c(2728),oS=c.n(oR),oT=(a,b)=>[0,3*a,3*b-6*a,3*a-3*b+1],oU=(a,b)=>a.map((a,c)=>a*b**c).reduce((a,b)=>a+b),oV=(a,b)=>c=>oU(oT(a,b),c),oW=function(){let a,b;for(var c,d,e,f,g=arguments.length,h=Array(g),i=0;i<g;i++)h[i]=arguments[i];if(1===h.length)switch(h[0]){case"linear":[c,e,d,f]=[0,0,1,1];break;case"ease":[c,e,d,f]=[.25,.1,.25,1];break;case"ease-in":[c,e,d,f]=[.42,0,1,1];break;case"ease-out":[c,e,d,f]=[.42,0,.58,1];break;case"ease-in-out":[c,e,d,f]=[0,0,.58,1];break;default:var j=h[0].split("(");"cubic-bezier"===j[0]&&4===j[1].split(")")[0].split(",").length&&([c,e,d,f]=j[1].split(")")[0].split(",").map(a=>parseFloat(a)))}else 4===h.length&&([c,e,d,f]=h);var k=oV(c,d),l=oV(e,f),m=(a=c,b=d,c=>oU([...oT(a,b).map((a,b)=>a*b).slice(1),0],c)),n=a=>a>1?1:a<0?0:a,o=a=>{for(var b=a>1?1:a,c=b,d=0;d<8;++d){var e=k(c)-b,f=m(c);if(1e-4>Math.abs(e-b)||f<1e-4)break;c=n(c-e/f)}return l(c)};return o.isStepper=!1,o},oX=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:b=100,damping:c=8,dt:d=17}=a,e=(a,e,f)=>{var g=f+(-(a-e)*b-f*c)*d/1e3,h=f*d/1e3+a;return 1e-4>Math.abs(h-e)&&1e-4>Math.abs(g)?[e,0]:[h,g]};return e.isStepper=!0,e.dt=d,e};function oY(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function oZ(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?oY(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):oY(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var o$=(a,b)=>Object.keys(b).reduce((c,d)=>oZ(oZ({},c),{},{[d]:a(d,b[d])}),{});function o_(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function o0(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?o_(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):o_(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var o1=(a,b,c)=>a+(b-a)*c,o2=a=>{var{from:b,to:c}=a;return b!==c},o3=(a,b,c)=>{var d=o$((b,c)=>{if(o2(c)){var[d,e]=a(c.from,c.to,c.velocity);return o0(o0({},c),{},{from:d,velocity:e})}return c},b);return c<1?o$((a,b)=>o2(b)?o0(o0({},b),{},{velocity:o1(b.velocity,d[a].velocity,c),from:o1(b.from,d[a].from,c)}):b,b):o3(a,d,c-1)};class o4{setTimeout(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,c=performance.now(),d=null,e=f=>{f-c>=b?a(f):"function"==typeof requestAnimationFrame&&(d=requestAnimationFrame(e))};return d=requestAnimationFrame(e),()=>{cancelAnimationFrame(d)}}}var o5=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function o6(){return(o6=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function o7(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function o8(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?o7(Object(c),!0).forEach(function(b){o9(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):o7(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function o9(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}class pa extends f.PureComponent{constructor(a,b){super(a,b),o9(this,"mounted",!1),o9(this,"manager",null),o9(this,"stopJSAnimation",null),o9(this,"unSubscribe",null);var{isActive:c,attributeName:d,from:e,to:f,children:g,duration:h,animationManager:i}=this.props;if(this.manager=i,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!c||h<=0){this.state={style:{}},"function"==typeof g&&(this.state={style:f});return}if(e){if("function"==typeof g){this.state={style:e};return}this.state={style:d?{[d]:e}:e}}else this.state={style:{}}}componentDidMount(){var{isActive:a,canBegin:b}=this.props;this.mounted=!0,a&&b&&this.runAnimation(this.props)}componentDidUpdate(a){var{isActive:b,canBegin:c,attributeName:d,shouldReAnimate:e,to:f,from:g}=this.props,{style:h}=this.state;if(c){if(!b){this.state&&h&&(d&&h[d]!==f||!d&&h!==f)&&this.setState({style:d?{[d]:f}:f});return}if(!oS()(a.to,f)||!a.canBegin||!a.isActive){var i=!a.canBegin||!a.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var j=i||e?g:a.to;this.state&&h&&(d&&h[d]!==j||!d&&h!==j)&&this.setState({style:d?{[d]:j}:j}),this.runAnimation(o8(o8({},this.props),{},{from:j,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:a}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),a&&a()}handleStyleChange(a){this.changeStyle(a)}changeStyle(a){this.mounted&&this.setState({style:a})}runJSAnimation(a){var b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,{from:A,to:B,duration:C,easing:D,begin:E,onAnimationEnd:F,onAnimationStart:G}=a,H=(w=(a=>{if("string"==typeof a)switch(a){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return oW(a);case"spring":return oX();default:if("cubic-bezier"===a.split("(")[0])return oW(a)}return"function"==typeof a?a:null})(D),x=this.changeStyle,y=this.manager.getTimeoutController(),z=[Object.keys(A),Object.keys(B)].reduce((a,b)=>a.filter(a=>b.includes(a))),!0===w.isStepper?(b=A,c=B,d=w,e=z,f=x,g=y,i=e.reduce((a,d)=>o0(o0({},a),{},{[d]:{from:b[d],velocity:0,to:c[d]}}),{}),j=null,k=a=>{h||(h=a);var e=(a-h)/d.dt;i=o3(d,i,e),f(o0(o0(o0({},b),c),o$((a,b)=>b.from,i))),h=a,Object.values(i).filter(o2).length&&(j=g.setTimeout(k))},()=>(j=g.setTimeout(k),()=>{j()})):(l=A,m=B,n=w,o=C,p=z,q=x,r=y,t=null,u=p.reduce((a,b)=>o0(o0({},a),{},{[b]:[l[b],m[b]]}),{}),v=a=>{s||(s=a);var b=(a-s)/o,c=o$((a,c)=>o1(...c,n(b)),u);if(q(o0(o0(o0({},l),m),c)),b<1)t=r.setTimeout(v);else{var d=o$((a,b)=>o1(...b,n(1)),u);q(o0(o0(o0({},l),m),d))}},()=>(t=r.setTimeout(v),()=>{t()})));this.manager.start([G,E,()=>{this.stopJSAnimation=H()},C,F])}runAnimation(a){let b;var{begin:c,duration:d,attributeName:e,to:f,easing:g,onAnimationStart:h,onAnimationEnd:i,children:j}=a;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof g||"function"==typeof j||"spring"===g)return void this.runJSAnimation(a);var k=e?{[e]:f}:f,l=(b=Object.keys(k),b.map(a=>"".concat(a.replace(/([A-Z])/g,a=>"-".concat(a.toLowerCase()))," ").concat(d,"ms ").concat(g)).join(","));this.manager.start([h,c,o8(o8({},k),{},{transition:l}),d,i])}render(){var a=this.props,{children:b,begin:c,duration:d,attributeName:e,easing:g,isActive:h,from:i,to:j,canBegin:k,onAnimationEnd:l,shouldReAnimate:m,onAnimationReStart:n,animationManager:o}=a,p=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,o5),q=f.Children.count(b),r=this.state.style;if("function"==typeof b)return b(r);if(!h||0===q||d<=0)return b;var s=a=>{var{style:b={},className:c}=a.props;return(0,f.cloneElement)(a,o8(o8({},p),{},{style:o8(o8({},b),r),className:c}))};return 1===q?s(f.Children.only(b)):f.createElement("div",null,f.Children.map(b,a=>s(a)))}}o9(pa,"displayName","Animate"),o9(pa,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var pb=(0,f.createContext)(null);function pc(a){var b,c,d,e,g,h,i,j=(0,f.useContext)(pb);return f.createElement(pa,o6({},a,{animationManager:null!=(h=null!=(i=a.animationManager)?i:j)?h:(b=new o4,c=()=>null,d=!1,e=null,g=a=>{if(!d){if(Array.isArray(a)){if(!a.length)return;var[f,...h]=a;if("number"==typeof f){e=b.setTimeout(g.bind(null,h),f);return}g(f),e=b.setTimeout(g.bind(null,h));return}"object"==typeof a&&c(a),"function"==typeof a&&a()}},{stop:()=>{d=!0},start:a=>{d=!1,e&&(e(),e=null),g(a)},subscribe:a=>(c=a,()=>{c=()=>null}),getTimeoutController:()=>b})}))}function pd(){return(pd=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var pe=(a,b,c,d,e)=>{var f,g=Math.min(Math.abs(c)/2,Math.abs(d)/2),h=d>=0?1:-1,i=c>=0?1:-1,j=+(d>=0&&c>=0||d<0&&c<0);if(g>0&&e instanceof Array){for(var k=[0,0,0,0],l=0;l<4;l++)k[l]=e[l]>g?g:e[l];f="M".concat(a,",").concat(b+h*k[0]),k[0]>0&&(f+="A ".concat(k[0],",").concat(k[0],",0,0,").concat(j,",").concat(a+i*k[0],",").concat(b)),f+="L ".concat(a+c-i*k[1],",").concat(b),k[1]>0&&(f+="A ".concat(k[1],",").concat(k[1],",0,0,").concat(j,",\n        ").concat(a+c,",").concat(b+h*k[1])),f+="L ".concat(a+c,",").concat(b+d-h*k[2]),k[2]>0&&(f+="A ".concat(k[2],",").concat(k[2],",0,0,").concat(j,",\n        ").concat(a+c-i*k[2],",").concat(b+d)),f+="L ".concat(a+i*k[3],",").concat(b+d),k[3]>0&&(f+="A ".concat(k[3],",").concat(k[3],",0,0,").concat(j,",\n        ").concat(a,",").concat(b+d-h*k[3])),f+="Z"}else if(g>0&&e===+e&&e>0){var m=Math.min(g,e);f="M ".concat(a,",").concat(b+h*m,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+i*m,",").concat(b,"\n            L ").concat(a+c-i*m,",").concat(b,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+c,",").concat(b+h*m,"\n            L ").concat(a+c,",").concat(b+d-h*m,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+c-i*m,",").concat(b+d,"\n            L ").concat(a+i*m,",").concat(b+d,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a,",").concat(b+d-h*m," Z")}else f="M ".concat(a,",").concat(b," h ").concat(c," v ").concat(d," h ").concat(-c," Z");return f},pf={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},pg=a=>{var b=ms(a,pf),c=(0,f.useRef)(null),[d,e]=(0,f.useState)(-1);(0,f.useEffect)(()=>{if(c.current&&c.current.getTotalLength)try{var a=c.current.getTotalLength();a&&e(a)}catch(a){}},[]);var{x:g,y:h,width:i,height:j,radius:k,className:l}=b,{animationEasing:m,animationDuration:n,animationBegin:o,isAnimationActive:p,isUpdateAnimationActive:q}=b;if(g!==+g||h!==+h||i!==+i||j!==+j||0===i||0===j)return null;var r=t("recharts-rectangle",l);return q?f.createElement(pc,{canBegin:d>0,from:{width:i,height:j,x:g,y:h},to:{width:i,height:j,x:g,y:h},duration:n,animationEasing:m,isActive:q},a=>{var{width:e,height:g,x:h,y:i}=a;return f.createElement(pc,{canBegin:d>0,from:"0px ".concat(-1===d?1:d,"px"),to:"".concat(d,"px 0px"),attributeName:"strokeDasharray",begin:o,duration:n,isActive:p,easing:m},f.createElement("path",pd({},l4(b,!0),{className:r,d:pe(h,i,e,g,k),ref:c})))}):f.createElement("path",pd({},l4(b,!0),{className:r,d:pe(g,h,i,j,k)}))};function ph(a){var{cx:b,cy:c,radius:d,startAngle:e,endAngle:f}=a;return{points:[cD(b,c,d,e),cD(b,c,d,f)],cx:b,cy:c,radius:d,startAngle:e,endAngle:f}}function pi(){return(pi=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var pj=a=>{var{cx:b,cy:c,radius:d,angle:e,sign:f,isExternal:g,cornerRadius:h,cornerIsExternal:i}=a,j=h*(g?1:-1)+d,k=Math.asin(h/j)/cC,l=i?e:e+f*k,m=cD(b,c,j,l);return{center:m,circleTangency:cD(b,c,d,l),lineTangency:cD(b,c,j*Math.cos(k*cC),i?e-f*k:e),theta:k}},pk=a=>{var{cx:b,cy:c,innerRadius:d,outerRadius:e,startAngle:f,endAngle:g}=a,h=((a,b)=>y(b-a)*Math.min(Math.abs(b-a),359.999))(f,g),i=f+h,j=cD(b,c,e,f),k=cD(b,c,e,i),l="M ".concat(j.x,",").concat(j.y,"\n    A ").concat(e,",").concat(e,",0,\n    ").concat(+(Math.abs(h)>180),",").concat(+(f>i),",\n    ").concat(k.x,",").concat(k.y,"\n  ");if(d>0){var m=cD(b,c,d,f),n=cD(b,c,d,i);l+="L ".concat(n.x,",").concat(n.y,"\n            A ").concat(d,",").concat(d,",0,\n            ").concat(+(Math.abs(h)>180),",").concat(+(f<=i),",\n            ").concat(m.x,",").concat(m.y," Z")}else l+="L ".concat(b,",").concat(c," Z");return l},pl={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},pm=a=>{var b,c=ms(a,pl),{cx:d,cy:e,innerRadius:g,outerRadius:h,cornerRadius:i,forceCornerRadius:j,cornerIsExternal:k,startAngle:l,endAngle:m,className:n}=c;if(h<g||l===m)return null;var o=t("recharts-sector",n),p=h-g,q=F(i,p,0,!0);return b=q>0&&360>Math.abs(l-m)?(a=>{var{cx:b,cy:c,innerRadius:d,outerRadius:e,cornerRadius:f,forceCornerRadius:g,cornerIsExternal:h,startAngle:i,endAngle:j}=a,k=y(j-i),{circleTangency:l,lineTangency:m,theta:n}=pj({cx:b,cy:c,radius:e,angle:i,sign:k,cornerRadius:f,cornerIsExternal:h}),{circleTangency:o,lineTangency:p,theta:q}=pj({cx:b,cy:c,radius:e,angle:j,sign:-k,cornerRadius:f,cornerIsExternal:h}),r=h?Math.abs(i-j):Math.abs(i-j)-n-q;if(r<0)return g?"M ".concat(m.x,",").concat(m.y,"\n        a").concat(f,",").concat(f,",0,0,1,").concat(2*f,",0\n        a").concat(f,",").concat(f,",0,0,1,").concat(-(2*f),",0\n      "):pk({cx:b,cy:c,innerRadius:d,outerRadius:e,startAngle:i,endAngle:j});var s="M ".concat(m.x,",").concat(m.y,"\n    A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(l.x,",").concat(l.y,"\n    A").concat(e,",").concat(e,",0,").concat(+(r>180),",").concat(+(k<0),",").concat(o.x,",").concat(o.y,"\n    A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(p.x,",").concat(p.y,"\n  ");if(d>0){var{circleTangency:t,lineTangency:u,theta:v}=pj({cx:b,cy:c,radius:d,angle:i,sign:k,isExternal:!0,cornerRadius:f,cornerIsExternal:h}),{circleTangency:w,lineTangency:x,theta:z}=pj({cx:b,cy:c,radius:d,angle:j,sign:-k,isExternal:!0,cornerRadius:f,cornerIsExternal:h}),A=h?Math.abs(i-j):Math.abs(i-j)-v-z;if(A<0&&0===f)return"".concat(s,"L").concat(b,",").concat(c,"Z");s+="L".concat(x.x,",").concat(x.y,"\n      A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(w.x,",").concat(w.y,"\n      A").concat(d,",").concat(d,",0,").concat(+(A>180),",").concat(+(k>0),",").concat(t.x,",").concat(t.y,"\n      A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(u.x,",").concat(u.y,"Z")}else s+="L".concat(b,",").concat(c,"Z");return s})({cx:d,cy:e,innerRadius:g,outerRadius:h,cornerRadius:Math.min(q,p/2),forceCornerRadius:j,cornerIsExternal:k,startAngle:l,endAngle:m}):pk({cx:d,cy:e,innerRadius:g,outerRadius:h,startAngle:l,endAngle:m}),f.createElement("path",pi({},l4(c,!0),{className:o,d:b}))};function pn(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function po(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?pn(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):pn(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function pp(){return(pp=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function pq(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function pr(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?pq(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):pq(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function ps(a){var b,c,d,{coordinate:e,payload:g,index:h,offset:i,tooltipAxisBandSize:j,layout:k,cursor:l,tooltipEventType:m,chartName:n}=a;if(!l||!e||"ScatterChart"!==n&&"axis"!==m)return null;if("ScatterChart"===n)c=e,d=oQ;else if("BarChart"===n)b=j/2,c={stroke:"none",fill:"#ccc",x:"horizontal"===k?e.x-b:i.left+.5,y:"horizontal"===k?i.top+.5:e.y-b,width:"horizontal"===k?j:i.width-1,height:"horizontal"===k?i.height-1:j},d=pg;else if("radial"===k){var{cx:o,cy:p,radius:q,startAngle:r,endAngle:s}=ph(e);c={cx:o,cy:p,startAngle:r,endAngle:s,innerRadius:q,outerRadius:q},d=pm}else c={points:function(a,b,c){var d,e,f,g;if("horizontal"===a)f=d=b.x,e=c.top,g=c.top+c.height;else if("vertical"===a)g=e=b.y,d=c.left,f=c.left+c.width;else if(null!=b.cx&&null!=b.cy)if("centric"!==a)return ph(b);else{var{cx:h,cy:i,innerRadius:j,outerRadius:k,angle:l}=b,m=cD(h,i,j,l),n=cD(h,i,k,l);d=m.x,e=m.y,f=n.x,g=n.y}return[{x:d,y:e},{x:f,y:g}]}(k,e,i)},d=oM;var u="object"==typeof l&&"className"in l?l.className:void 0,v=pr(pr(pr(pr({stroke:"#ccc",pointerEvents:"none"},i),c),l4(l,!1)),{},{payload:g,payloadIndex:h,className:t("recharts-tooltip-cursor",u)});return(0,f.isValidElement)(l)?(0,f.cloneElement)(l,v):(0,f.createElement)(d,v)}function pt(a){var b,c,d,e=(b=cp(jW),c=cp(kn),d=cp(kk),((a,b,c)=>{if(a&&a.scale&&a.scale.bandwidth){var d=a.scale.bandwidth();if(!c||d>0)return d}if(a&&b&&b.length>=2){for(var e=cr()(b,a=>a.coordinate),f=1/0,g=1,h=e.length;g<h;g++){var i=e[g],j=e[g-1];f=Math.min((i.coordinate||0)-(j.coordinate||0),f)}return f===1/0?0:f}return c?void 0:0})(po(po({},b),{},{scale:d}),c)),g=c7(),h=dc(),i=cp(hP);return f.createElement(ps,pp({},a,{coordinate:a.coordinate,index:a.index,payload:a.payload,offset:g,layout:h,tooltipAxisBandSize:e,chartName:i}))}function pu(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function pv(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?pu(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):pu(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function pw(a){return a.dataKey}var px=[],py={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!mz.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function pz(a){var b,c,d=ms(a,py),{active:e,allowEscapeViewBox:g,animationDuration:h,animationEasing:i,content:j,filterNull:k,isAnimationActive:l,offset:m,payloadUniqBy:n,position:o,reverseDirection:p,useTranslate3d:q,wrapperStyle:r,cursor:s,shared:t,trigger:u,defaultIndex:v,portal:w,axisId:x}=d;cl();var y="number"==typeof v?String(v):v,z=c5(),A=l5(),B=cp(a=>jH(a,t)),{activeIndex:C,isActive:D}=cp(a=>kL(a,B,u,y)),E=cp(a=>kK(a,B,u,y)),F=cp(a=>kJ(a,B,u,y)),G=cp(a=>kI(a,B,u,y)),H=(0,f.useContext)(mg),I=null!=e?e:D,[J,K]=oc([E,I]),L="axis"===B?F:void 0;cp(a=>((a,b,c)=>{if(null!=b){var d=jQ(a);return"axis"===b?"hover"===c?d.axisInteraction.hover.dataKey:d.axisInteraction.click.dataKey:"hover"===c?d.itemInteraction.hover.dataKey:d.itemInteraction.click.dataKey}})(a,B,u)),cp(hS),cp(hQ),cp(hR),null==(b=cp(mf))||b.active;var M=null!=w?w:H;if(null==M)return null;var N=null!=E?E:px;I||(N=px),k&&N.length&&(N=ob(E.filter(a=>null!=a.value&&(!0!==a.hide||d.includeHidden)),n,pw));var O=N.length>0,P=f.createElement(n8,{allowEscapeViewBox:g,animationDuration:h,animationEasing:i,isAnimationActive:l,active:I,coordinate:G,hasPayload:O,offset:m,position:o,reverseDirection:p,useTranslate3d:q,viewBox:z,wrapperStyle:r,lastBoundingBox:J,innerRef:K,hasPortalFromProps:!!w},(c=pv(pv({},d),{},{payload:N,label:L,active:I,coordinate:G,accessibilityLayer:A}),f.isValidElement(j)?f.cloneElement(j,c):"function"==typeof j?f.createElement(j,c):f.createElement(n1,c)));return f.createElement(f.Fragment,null,(0,nY.createPortal)(P,M),I&&f.createElement(pt,{cursor:s,tooltipEventType:B,coordinate:G,payload:E,index:C}))}let pA=Math.cos,pB=Math.sin,pC=Math.sqrt,pD=Math.PI,pE=2*pD,pF={draw(a,b){let c=pC(b/pD);a.moveTo(c,0),a.arc(0,0,c,0,pE)}},pG=pC(1/3),pH=2*pG,pI=pB(pD/10)/pB(7*pD/10),pJ=pB(pE/10)*pI,pK=-pA(pE/10)*pI,pL=pC(3),pM=pC(3)/2,pN=1/pC(12),pO=(pN/2+1)*3;pC(3),pC(3);var pP=["type","size","sizeType"];function pQ(){return(pQ=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function pR(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function pS(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?pR(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):pR(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var pT={symbolCircle:pF,symbolCross:{draw(a,b){let c=pC(b/5)/2;a.moveTo(-3*c,-c),a.lineTo(-c,-c),a.lineTo(-c,-3*c),a.lineTo(c,-3*c),a.lineTo(c,-c),a.lineTo(3*c,-c),a.lineTo(3*c,c),a.lineTo(c,c),a.lineTo(c,3*c),a.lineTo(-c,3*c),a.lineTo(-c,c),a.lineTo(-3*c,c),a.closePath()}},symbolDiamond:{draw(a,b){let c=pC(b/pH),d=c*pG;a.moveTo(0,-c),a.lineTo(d,0),a.lineTo(0,c),a.lineTo(-d,0),a.closePath()}},symbolSquare:{draw(a,b){let c=pC(b),d=-c/2;a.rect(d,d,c,c)}},symbolStar:{draw(a,b){let c=pC(.8908130915292852*b),d=pJ*c,e=pK*c;a.moveTo(0,-c),a.lineTo(d,e);for(let b=1;b<5;++b){let f=pE*b/5,g=pA(f),h=pB(f);a.lineTo(h*c,-g*c),a.lineTo(g*d-h*e,h*d+g*e)}a.closePath()}},symbolTriangle:{draw(a,b){let c=-pC(b/(3*pL));a.moveTo(0,2*c),a.lineTo(-pL*c,-c),a.lineTo(pL*c,-c),a.closePath()}},symbolWye:{draw(a,b){let c=pC(b/pO),d=c/2,e=c*pN,f=c*pN+c,g=-d;a.moveTo(d,e),a.lineTo(d,f),a.lineTo(g,f),a.lineTo(-.5*d-pM*e,pM*d+-.5*e),a.lineTo(-.5*d-pM*f,pM*d+-.5*f),a.lineTo(-.5*g-pM*f,pM*g+-.5*f),a.lineTo(-.5*d+pM*e,-.5*e-pM*d),a.lineTo(-.5*d+pM*f,-.5*f-pM*d),a.lineTo(-.5*g+pM*f,-.5*f-pM*g),a.closePath()}}},pU=Math.PI/180,pV=a=>{var{type:b="circle",size:c=64,sizeType:d="area"}=a,e=pS(pS({},function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,pP)),{},{type:b,size:c,sizeType:d}),{className:g,cx:h,cy:i}=e,j=l4(e,!0);return h===+h&&i===+i&&c===+c?f.createElement("path",pQ({},j,{className:t("recharts-symbols",g),transform:"translate(".concat(h,", ").concat(i,")"),d:(()=>{var a=pT["symbol".concat(J(b))]||pF;return(function(a,b){let c=null,d=oA(e);function e(){let e;if(c||(c=e=d()),a.apply(this,arguments).draw(c,+b.apply(this,arguments)),e)return c=null,e+""||null}return a="function"==typeof a?a:cw(a||pF),b="function"==typeof b?b:cw(void 0===b?64:+b),e.type=function(b){return arguments.length?(a="function"==typeof b?b:cw(b),e):a},e.size=function(a){return arguments.length?(b="function"==typeof a?a:cw(+a),e):b},e.context=function(a){return arguments.length?(c=null==a?null:a,e):c},e})().type(a).size(((a,b,c)=>{if("area"===b)return a;switch(c){case"cross":return 5*a*a/9;case"diamond":return .5*a*a/Math.sqrt(3);case"square":return a*a;case"star":var d=18*pU;return 1.25*a*a*(Math.tan(d)-Math.tan(2*d)*Math.tan(d)**2);case"triangle":return Math.sqrt(3)*a*a/4;case"wye":return(21-10*Math.sqrt(3))*a*a/8;default:return Math.PI*a*a/4}})(c,d,b))()})()})):null};function pW(){return(pW=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function pX(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function pY(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}pV.registerSymbol=(a,b)=>{pT["symbol".concat(J(a))]=b};class pZ extends f.PureComponent{renderIcon(a,b){var{inactiveColor:c}=this.props,d=32/6,e=32/3,g=a.inactive?c:a.color,h=null!=b?b:a.type;if("none"===h)return null;if("plainline"===h)return f.createElement("line",{strokeWidth:4,fill:"none",stroke:g,strokeDasharray:a.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===h)return f.createElement("path",{strokeWidth:4,fill:"none",stroke:g,d:"M0,".concat(16,"h").concat(e,"\n            A").concat(d,",").concat(d,",0,1,1,").concat(2*e,",").concat(16,"\n            H").concat(32,"M").concat(2*e,",").concat(16,"\n            A").concat(d,",").concat(d,",0,1,1,").concat(e,",").concat(16),className:"recharts-legend-icon"});if("rect"===h)return f.createElement("path",{stroke:"none",fill:g,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(f.isValidElement(a.legendIcon)){var i=function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?pX(Object(c),!0).forEach(function(b){pY(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):pX(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({},a);return delete i.legendIcon,f.cloneElement(a.legendIcon,i)}return f.createElement(pV,{fill:g,cx:16,cy:16,size:32,sizeType:"diameter",type:h})}renderItems(){var{payload:a,iconSize:b,layout:c,formatter:d,inactiveColor:e,iconType:g}=this.props,h={x:0,y:0,width:32,height:32},i={display:"horizontal"===c?"inline-block":"block",marginRight:10},j={display:"inline-block",verticalAlign:"middle",marginRight:4};return a.map((a,c)=>{var k=a.formatter||d,l=t({"recharts-legend-item":!0,["legend-item-".concat(c)]:!0,inactive:a.inactive});if("none"===a.type)return null;var m=a.inactive?e:a.color,n=k?k(a.value,a,c):a.value;return f.createElement("li",pW({className:l,style:i,key:"legend-item-".concat(c)},l$(this.props,a,c)),f.createElement(l8,{width:b,height:b,viewBox:h,style:j,"aria-label":"".concat(n," legend icon")},this.renderIcon(a,g)),f.createElement("span",{className:"recharts-legend-item-text",style:{color:m}},n))})}render(){var{payload:a,layout:b,align:c}=this.props;return a&&a.length?f.createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===b?c:"left"}},this.renderItems()):null}}pY(pZ,"displayName","Legend"),pY(pZ,"defaultProps",{align:"center",iconSize:14,inactiveColor:"#ccc",layout:"horizontal",verticalAlign:"middle"});var p$=["contextPayload"];function p_(){return(p_=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function p0(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function p1(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?p0(Object(c),!0).forEach(function(b){p2(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):p0(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function p2(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function p3(a){return a.value}function p4(a){var{contextPayload:b}=a,c=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,p$),d=ob(b,a.payloadUniqBy,p3),e=p1(p1({},c),{},{payload:d});return f.isValidElement(a.content)?f.cloneElement(a.content,e):"function"==typeof a.content?f.createElement(a.content,e):f.createElement(pZ,e)}function p5(a){return cl(),null}function p6(a){return cl(),null}function p7(a){var b,c=cp(ct),d=(0,f.useContext)(mh),e=null!=(b=cp(a=>a.layout.margin))?b:da,{width:g,height:h,wrapperStyle:i,portal:j}=a,[k,l]=oc([c]),m=c8(),n=c9(),o=m-(e.left||0)-(e.right||0),p=p8.getWidthOrHeight(a.layout,h,g,o),q=j?i:p1(p1({position:"absolute",width:(null==p?void 0:p.width)||g||"auto",height:(null==p?void 0:p.height)||h||"auto"},function(a,b,c,d,e,f){var g,h,{layout:i,align:j,verticalAlign:k}=b;return a&&(void 0!==a.left&&null!==a.left||void 0!==a.right&&null!==a.right)||(g="center"===j&&"vertical"===i?{left:((d||0)-f.width)/2}:"right"===j?{right:c&&c.right||0}:{left:c&&c.left||0}),a&&(void 0!==a.top&&null!==a.top||void 0!==a.bottom&&null!==a.bottom)||(h="middle"===k?{top:((e||0)-f.height)/2}:"bottom"===k?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),p1(p1({},g),h)}(i,a,e,m,n,k)),i),r=null!=j?j:d;if(null==r)return null;var s=f.createElement("div",{className:"recharts-legend-wrapper",style:q,ref:l},f.createElement(p5,{layout:a.layout,align:a.align,verticalAlign:a.verticalAlign,itemSorter:a.itemSorter}),f.createElement(p6,{width:k.width,height:k.height}),f.createElement(p4,p_({},a,p,{margin:e,chartWidth:m,chartHeight:n,contextPayload:c})));return(0,nY.createPortal)(s,r)}class p8 extends f.PureComponent{static getWidthOrHeight(a,b,c,d){return"vertical"===a&&B(b)?{height:b}:"horizontal"===a?{width:c||d}:null}render(){return f.createElement(p7,this.props)}}p2(p8,"displayName","Legend"),p2(p8,"defaultProps",{align:"center",iconSize:14,itemSorter:"value",layout:"horizontal",verticalAlign:"bottom"});var p9=c(9474),qa=c.n(p9),qb=["valueAccessor"],qc=["data","dataKey","clockWise","id","textBreakAll"];function qd(){return(qd=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function qe(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function qf(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?qe(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):qe(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function qg(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var qh=a=>Array.isArray(a.value)?qa()(a.value):a.value;function qi(a){var{valueAccessor:b=qh}=a,c=qg(a,qb),{data:d,dataKey:e,clockWise:g,id:h,textBreakAll:i}=c,j=qg(c,qc);return d&&d.length?f.createElement(mQ,{className:"recharts-label-list"},d.map((a,c)=>{var d=null==e?b(a,c):cG(a&&a.payload,e),k=null==h?{}:{id:"".concat(h,"-").concat(c)};return f.createElement(nf,qd({},l4(a,!0),j,k,{parentViewBox:a.parentViewBox,value:d,textBreakAll:i,viewBox:nf.parseViewBox(null==g?a:qf(qf({},a),{},{clockWise:g})),key:"label-".concat(c),index:c}))})):null}function qj(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function qk(a){return cl(),null}qi.displayName="LabelList",qi.renderCallByParent=function(a,b){var c,d=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!a||!a.children&&d&&!a.label)return null;var{children:e}=a,g=l3(e,qi).map((a,c)=>(0,f.cloneElement)(a,{data:b,key:"labelList-".concat(c)}));return d?[(c=a.label,c?!0===c?f.createElement(qi,{key:"labelList-implicit",data:b}):f.isValidElement(c)||ne(c)?f.createElement(qi,{key:"labelList-implicit",data:b,content:c}):"object"==typeof c?f.createElement(qi,qd({data:b},c,{key:"labelList-implicit"})):null:null),...g]:g};class ql extends f.Component{render(){return f.createElement(qk,{domain:this.props.domain,id:this.props.zAxisId,dataKey:this.props.dataKey,name:this.props.name,unit:this.props.unit,range:this.props.range,scale:this.props.scale,type:this.props.type,allowDuplicatedCategory:ih.allowDuplicatedCategory,allowDataOverflow:ih.allowDataOverflow,reversed:ih.reversed,includeHidden:ih.includeHidden})}}qj(ql,"displayName","ZAxis"),qj(ql,"defaultProps",{zAxisId:0,range:ih.range,scale:ih.scale,type:ih.type});var qm=a=>null;qm.displayName="Cell";var qn=c(2867),qo=c.n(qn);function qp(){return(qp=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var qq=(a,b,c,d,e)=>{var f=c-d;return"M ".concat(a,",").concat(b)+"L ".concat(a+c,",").concat(b)+"L ".concat(a+c-f/2,",").concat(b+e)+"L ".concat(a+c-f/2-d,",").concat(b+e)+"L ".concat(a,",").concat(b," Z")},qr={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},qs=a=>{var b=ms(a,qr),c=(0,f.useRef)(),[d,e]=(0,f.useState)(-1);(0,f.useEffect)(()=>{if(c.current&&c.current.getTotalLength)try{var a=c.current.getTotalLength();a&&e(a)}catch(a){}},[]);var{x:g,y:h,upperWidth:i,lowerWidth:j,height:k,className:l}=b,{animationEasing:m,animationDuration:n,animationBegin:o,isUpdateAnimationActive:p}=b;if(g!==+g||h!==+h||i!==+i||j!==+j||k!==+k||0===i&&0===j||0===k)return null;var q=t("recharts-trapezoid",l);return p?f.createElement(pc,{canBegin:d>0,from:{upperWidth:0,lowerWidth:0,height:k,x:g,y:h},to:{upperWidth:i,lowerWidth:j,height:k,x:g,y:h},duration:n,animationEasing:m,isActive:p},a=>{var{upperWidth:e,lowerWidth:g,height:h,x:i,y:j}=a;return f.createElement(pc,{canBegin:d>0,from:"0px ".concat(-1===d?1:d,"px"),to:"".concat(d,"px 0px"),attributeName:"strokeDasharray",begin:o,duration:n,easing:m},f.createElement("path",qp({},l4(b,!0),{className:q,d:qq(i,j,e,g,h),ref:c})))}):f.createElement("g",null,f.createElement("path",qp({},l4(b,!0),{className:q,d:qq(g,h,i,j,k)})))},qt=["option","shapeType","propTransformer","activeClassName","isActive"];function qu(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function qv(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?qu(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):qu(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function qw(a,b){return qv(qv({},b),a)}function qx(a){var{shapeType:b,elementProps:c}=a;switch(b){case"rectangle":return f.createElement(pg,c);case"trapezoid":return f.createElement(qs,c);case"sector":return f.createElement(pm,c);case"symbols":if("symbols"===b)return f.createElement(pV,c);break;default:return null}}function qy(a){var b,{option:c,shapeType:d,propTransformer:e=qw,activeClassName:g="recharts-active-shape",isActive:h}=a,i=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,qt);if((0,f.isValidElement)(c))b=(0,f.cloneElement)(c,qv(qv({},i),(0,f.isValidElement)(c)?c.props:c));else if("function"==typeof c)b=c(i);else if(qo()(c)&&"boolean"!=typeof c){var j=e(c,i);b=f.createElement(qx,{shapeType:d,elementProps:j})}else b=f.createElement(qx,{shapeType:d,elementProps:i});return h?f.createElement(mQ,{className:g},b):b}var qz=["option","isActive"];function qA(){return(qA=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function qB(a){var{option:b,isActive:c}=a,d=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,qz);return"string"==typeof b?f.createElement(qy,qA({option:f.createElement(pV,qA({type:b},d)),isActive:c,shapeType:"symbols"},d)):f.createElement(qy,qA({option:b,isActive:c,shapeType:"symbols"},d))}function qC(a){var{fn:b,args:c}=a;return cl(),c2(),null}function qD(a){return cl(),(0,f.useRef)(null),null}var qE=["children"],qF=()=>{},qG=(0,f.createContext)({addErrorBar:qF,removeErrorBar:qF}),qH=(0,f.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function qI(a){var{children:b}=a,c=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,qE);return f.createElement(qH.Provider,{value:c},b)}var qJ=a=>{var{children:b,xAxisId:c,yAxisId:d,zAxisId:e,dataKey:g,data:h,stackId:i,hide:j,type:k,barSize:l}=a,[m,n]=f.useState([]),o=(0,f.useCallback)(a=>{n(b=>[...b,a])},[n]),p=(0,f.useCallback)(a=>{n(b=>b.filter(b=>b!==a))},[n]),q=c2();return f.createElement(qG.Provider,{value:{addErrorBar:o,removeErrorBar:p}},f.createElement(qD,{type:k,data:h,xAxisId:c,yAxisId:d,zAxisId:e,dataKey:g,errorBars:m,stackId:i,hide:j,barSize:l,isPanorama:q}),b)};function qK(a,b){var c,d,e=cp(b=>id(b,a)),f=cp(a=>ig(a,b)),g=null!=(c=null==e?void 0:e.allowDataOverflow)?c:ic.allowDataOverflow,h=null!=(d=null==f?void 0:f.allowDataOverflow)?d:ie.allowDataOverflow;return{needClip:g||h,needClipX:g,needClipY:h}}function qL(a){var{xAxisId:b,yAxisId:c,clipPathId:d}=a,e=mm(),{needClipX:g,needClipY:h,needClip:i}=qK(b,c);if(!i)return null;var{x:j,y:k,width:l,height:m}=e;return f.createElement("clipPath",{id:"clipPath-".concat(d)},f.createElement("rect",{x:g?j:j-l/2,y:h?k:k-m/2,width:g?l:2*l,height:h?m:2*m}))}var qM=cg([io,(a,b,c,d,e)=>e],(a,b)=>{if(a.some(a=>"scatter"===a.type&&b.dataKey===a.dataKey&&b.data===a.data))return b}),qN=cg([(a,b,c,d,e,f,g)=>gV(a,b,c,g),(a,b,c,d,e,f,g)=>jA(a,"xAxis",b,g),(a,b,c,d,e,f,g)=>jz(a,"xAxis",b,g),(a,b,c,d,e,f,g)=>jA(a,"yAxis",c,g),(a,b,c,d,e,f,g)=>jz(a,"yAxis",c,g),(a,b,c,d)=>jC(a,"zAxis",d,!1),qM,(a,b,c,d,e,f)=>f],(a,b,c,d,e,f,g,h)=>{var i,{chartData:j,dataStartIndex:k,dataEndIndex:l}=a;if(null!=g&&null!=(i=(null==g?void 0:g.data)!=null&&g.data.length>0?g.data:null==j?void 0:j.slice(k,l+1))&&null!=b&&null!=d&&null!=c&&null!=e&&(null==c?void 0:c.length)!==0&&(null==e?void 0:e.length)!==0)return function(a){var{displayedData:b,xAxis:c,yAxis:d,zAxis:e,scatterSettings:f,xAxisTicks:g,yAxisTicks:h,cells:i}=a,j=null==c.dataKey?f.dataKey:c.dataKey,k=null==d.dataKey?f.dataKey:d.dataKey,l=e&&e.dataKey,m=e?e.range:ql.defaultProps.range,n=m&&m[0],o=c.scale.bandwidth?c.scale.bandwidth():0,p=d.scale.bandwidth?d.scale.bandwidth():0;return b.map((a,b)=>{var m=cG(a,j),q=cG(a,k),r=null!=l&&cG(a,l)||"-",s=[{name:null==c.dataKey?f.name:c.name||c.dataKey,unit:c.unit||"",value:m,payload:a,dataKey:j,type:f.tooltipType},{name:null==d.dataKey?f.name:d.name||d.dataKey,unit:d.unit||"",value:q,payload:a,dataKey:k,type:f.tooltipType}];"-"!==r&&s.push({name:e.name||e.dataKey,unit:e.unit||"",value:r,payload:a,dataKey:l,type:f.tooltipType});var t=cL({axis:c,ticks:g,bandSize:o,entry:a,index:b,dataKey:j}),u=cL({axis:d,ticks:h,bandSize:p,entry:a,index:b,dataKey:k}),v="-"!==r?e.scale(r):n,w=Math.sqrt(Math.max(v,0)/Math.PI);return qU(qU({},a),{},{cx:t,cy:u,x:t-w,y:u-w,width:2*w,height:2*w,size:v,node:{x:m,y:q,z:r},tooltipPayload:s,tooltipPosition:{x:t,y:u},payload:a},i&&i[b]&&i[b].props)})}({displayedData:i,xAxis:b,yAxis:d,zAxis:f,scatterSettings:g,xAxisTicks:c,yAxisTicks:e,cells:h})});function qO(a){var{legendPayload:b}=a;return cl(),c2(),null}var qP=["onMouseEnter","onClick","onMouseLeave"],qQ=["animationBegin","animationDuration","animationEasing","hide","isAnimationActive","legendType","lineJointType","lineType","shape","xAxisId","yAxisId","zAxisId"];function qR(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function qS(){return(qS=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function qT(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function qU(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?qT(Object(c),!0).forEach(function(b){qV(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):qT(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function qV(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function qW(a){var b,c,{points:d,props:e}=a,{line:g,lineType:h,lineJointType:i}=e;if(!g)return null;var j=l4(e,!1),k=l4(g,!1);if("joint"===h)b=d.map(a=>({x:a.cx,y:a.cy}));else if("fitting"===h){var{xmin:l,xmax:m,a:n,b:o}=(a=>{if(!a||!a.length)return null;for(var b=a.length,c=0,d=0,e=0,f=0,g=1/0,h=-1/0,i=0,j=0,k=0;k<b;k++)i=a[k].cx||0,j=a[k].cy||0,c+=i,d+=j,e+=i*j,f+=i*i,g=Math.min(g,i),h=Math.max(h,i);var l=b*f!=c*c?(b*e-c*d)/(b*f-c*c):0;return{xmin:g,xmax:h,a:l,b:(d-l*c)/b}})(d);b=[{x:l,y:n*l+o},{x:m,y:n*m+o}]}var p=qU(qU(qU({},j),{},{fill:"none",stroke:j&&j.fill},k),{},{points:b});return c=f.isValidElement(g)?f.cloneElement(g,p):"function"==typeof g?g(p):f.createElement(oM,qS({},p,{type:i})),f.createElement(mQ,{className:"recharts-scatter-line",key:"recharts-scatter-line"},c)}function qX(a){var b,c,d,e,g,{points:h,showLabels:i,allOtherScatterProps:j}=a,{shape:k,activeShape:l,dataKey:m}=j,n=l4(j,!1),o=cp(ks),{onMouseEnter:p,onClick:q,onMouseLeave:r}=j,s=qR(j,qP),t=(b=j.dataKey,c=cl(),(a,d)=>e=>{null==p||p(a,d,e),c(bH({activeIndex:String(d),activeDataKey:b,activeCoordinate:a.tooltipPosition}))}),u=(d=cl(),(a,b)=>c=>{null==r||r(a,b,c),d(bI())}),v=(e=j.dataKey,g=cl(),(a,b)=>c=>{null==q||q(a,b,c),g(bK({activeIndex:String(b),activeDataKey:e,activeCoordinate:a.tooltipPosition}))});return null==h?null:f.createElement(f.Fragment,null,f.createElement(qW,{points:h,props:j}),h.map((a,b)=>{var c=l&&o===String(b),d=c?l:k,e=qU(qU(qU({key:"symbol-".concat(b)},n),a),{},{[cW]:b,[cX]:String(m)});return f.createElement(mQ,qS({className:"recharts-scatter-symbol"},l$(s,a,b),{onMouseEnter:t(a,b),onMouseLeave:u(a,b),onClick:v(a,b),key:"symbol-".concat(null==a?void 0:a.cx,"-").concat(null==a?void 0:a.cy,"-").concat(null==a?void 0:a.size,"-").concat(b)}),f.createElement(qB,qS({option:d,isActive:c},e)))}),i&&qi.renderCallByParent(j,h))}function qY(a){var{previousPointsRef:b,props:c}=a,{points:d,isAnimationActive:e,animationBegin:g,animationDuration:h,animationEasing:i}=c,j=b.current,k=function(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",c=(0,f.useRef)(E(b)),d=(0,f.useRef)(a);return d.current!==a&&(c.current=E(b),d.current=a),c.current}(c,"recharts-scatter-"),[l,m]=(0,f.useState)(!1),n=(0,f.useCallback)(()=>{m(!1)},[]),o=(0,f.useCallback)(()=>{m(!0)},[]);return f.createElement(pc,{begin:g,duration:h,isActive:e,easing:i,from:{t:0},to:{t:1},onAnimationEnd:n,onAnimationStart:o,key:k},a=>{var{t:e}=a,g=1===e?d:d.map((a,b)=>{var c=j&&j[b];if(c){var d=H(c.cx,a.cx),f=H(c.cy,a.cy),g=H(c.size,a.size);return qU(qU({},a),{},{cx:d(e),cy:f(e),size:g(e)})}var h=H(0,a.size);return qU(qU({},a),{},{size:h(e)})});return e>0&&(b.current=g),f.createElement(mQ,null,f.createElement(qX,{points:g,allOtherScatterProps:c,showLabels:!l}))})}function qZ(a){var{points:b,isAnimationActive:c}=a,d=(0,f.useRef)(null),e=d.current;return c&&b&&b.length&&(!e||e!==b)?f.createElement(qY,{props:a,previousPointsRef:d}):f.createElement(qX,{points:b,allOtherScatterProps:a,showLabels:!0})}function q$(a){var{dataKey:b,points:c,stroke:d,strokeWidth:e,fill:f,name:g,hide:h,tooltipType:i}=a;return{dataDefinedOnItem:null==c?void 0:c.map(a=>a.tooltipPayload),positions:null==c?void 0:c.map(a=>a.tooltipPosition),settings:{stroke:d,strokeWidth:e,fill:f,nameKey:void 0,dataKey:b,name:cP(g,b),hide:h,type:i,color:f,unit:""}}}var q_=(a,b,c)=>({x:a.cx,y:a.cy,value:"x"===c?+a.node.x:+a.node.y,errorVal:cG(a,b)});function q0(a){var b=(0,f.useRef)(E("recharts-scatter-")),{hide:c,points:d,className:e,needClip:g,xAxisId:h,yAxisId:i,id:j,children:k}=a;if(c)return null;var l=t("recharts-scatter",e),m=null==j?b.current:j;return f.createElement(mQ,{className:l,clipPath:g?"url(#clipPath-".concat(m,")"):null},g&&f.createElement("defs",null,f.createElement(qL,{clipPathId:m,xAxisId:h,yAxisId:i})),f.createElement(qI,{xAxisId:h,yAxisId:i,data:d,dataPointFormatter:q_,errorBarOffset:0},k),f.createElement(mQ,{key:"recharts-scatter-symbols"},f.createElement(qZ,a)))}var q1={xAxisId:0,yAxisId:0,zAxisId:0,legendType:"circle",lineType:"joint",lineJointType:"linear",data:[],shape:"circle",hide:!1,isAnimationActive:!mz.isSsr,animationBegin:0,animationDuration:400,animationEasing:"linear"};function q2(a){var b=ms(a,q1),{animationBegin:c,animationDuration:d,animationEasing:e,hide:g,isAnimationActive:h,legendType:i,lineJointType:j,lineType:k,shape:l,xAxisId:m,yAxisId:n,zAxisId:o}=b,p=qR(b,qQ),{needClip:q}=qK(m,n),r=(0,f.useMemo)(()=>l3(a.children,qm),[a.children]),s=(0,f.useMemo)(()=>({name:a.name,tooltipType:a.tooltipType,data:a.data,dataKey:a.dataKey}),[a.data,a.dataKey,a.name,a.tooltipType]),t=c2(),u=cp(a=>qN(a,m,n,o,s,r,t));return null==q?null:f.createElement(f.Fragment,null,f.createElement(qC,{fn:q$,args:qU(qU({},a),{},{points:u})}),f.createElement(q0,qS({},p,{xAxisId:m,yAxisId:n,zAxisId:o,lineType:k,lineJointType:j,legendType:i,shape:l,hide:g,isAnimationActive:h,animationBegin:c,animationDuration:d,animationEasing:e,points:u,needClip:q})))}class q3 extends f.Component{render(){return f.createElement(qJ,{type:"scatter",data:this.props.data,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:this.props.zAxisId,dataKey:this.props.dataKey,stackId:void 0,hide:this.props.hide,barSize:void 0},f.createElement(qO,{legendPayload:(a=>{var{dataKey:b,name:c,fill:d,legendType:e,hide:f}=a;return[{inactive:f,dataKey:b,type:e,color:d,value:cP(c,b),payload:a}]})(this.props)}),f.createElement(q2,this.props))}}qV(q3,"displayName","Scatter"),qV(q3,"defaultProps",q1);let q4={"Fully OA":"#10B981",Hybrid:"#F59E0B",Diamond:"#8B5CF6",Subscription:"#EF4444",Unknown:"#6B7280"},q5=()=>{let{filteredJournals:a,selectedJournals:b,addSelectedJournal:c,removeSelectedJournal:d}=k(),g=(0,f.useMemo)(()=>{let c=a;if(c.length>5e3){let a=[...c].sort((a,b)=>b.impact_proxy-a.impact_proxy);c=[...a.slice(0,2e3),...a.slice(2e3).sort(()=>.5-Math.random()).slice(0,3e3)]}return c.filter(a=>null!=a.apc_usd&&!isNaN(a.apc_usd)).map(a=>({x:a.impact_proxy,y:a.apc_usd,title:a.title,oa_type:a.oa_type,cost_efficiency:"number"==typeof a.cost_efficiency&&999999===a.cost_efficiency?1/0:a.cost_efficiency,issn_l:a.issn_l,publisher:a.publisher,selected:b.has(a.issn_l)}))},[a,b]),h=(0,f.useMemo)(()=>{let a={};return g.forEach(b=>{a[b.oa_type]||(a[b.oa_type]=[]),a[b.oa_type].push(b)}),a},[g]),i=a=>{b.has(a.issn_l)?d(a.issn_l):c(a.issn_l)};return(0,e.jsxs)("div",{className:"h-full",children:[(0,e.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,e.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"SJR vs APC Cost"}),(0,e.jsxs)("div",{className:"text-sm text-gray-600",children:[g.length," journals with APC data (of ",a.length," total)"]})]}),(0,e.jsx)(N,{width:"100%",height:"90%",children:(0,e.jsxs)(my,{margin:{top:20,right:20,bottom:60,left:60},children:[(0,e.jsx)(nG,{strokeDasharray:"3 3"}),(0,e.jsx)(nP,{type:"number",dataKey:"x",name:"SJR",domain:[0,"dataMax"],label:{value:"SJR (SCImago Journal Rank)",position:"insideBottom",offset:-10}}),(0,e.jsx)(nX,{type:"number",dataKey:"y",name:"APC (USD)",domain:[0,"dataMax"],label:{value:"APC (USD)",angle:-90,position:"insideLeft"}}),(0,e.jsx)(pz,{content:(0,e.jsx)(({active:a,payload:b})=>{if(a&&b&&b.length){let a=b[0].payload;return(0,e.jsxs)("div",{className:"bg-white p-3 border border-gray-300 rounded-lg shadow-lg",children:[(0,e.jsx)("p",{className:"font-semibold text-gray-900 mb-1",children:a.title}),(0,e.jsxs)("p",{className:"text-sm text-gray-600",children:["SJR: ",a.x.toFixed(2)]}),(0,e.jsxs)("p",{className:"text-sm text-gray-600",children:["APC: ",null!=a.y?`$${a.y.toLocaleString()}`:"N/A"]}),(0,e.jsxs)("p",{className:"text-sm text-gray-600",children:["OA Type: ",a.oa_type]}),(0,e.jsxs)("p",{className:"text-sm text-gray-600",children:["Publisher: ",a.publisher]}),(0,e.jsxs)("p",{className:"text-sm text-gray-600",children:["Cost Efficiency: ",a.cost_efficiency===1/0?"∞":a.cost_efficiency.toFixed(4)]})]})}return null},{})}),(0,e.jsx)(p8,{verticalAlign:"bottom",height:36}),Object.entries(h).map(([a,b])=>(0,e.jsx)(q3,{name:a,data:b,fill:q4[a]||"#6B7280",onClick:i,style:{cursor:"pointer"}},a))]})}),(0,e.jsx)("div",{className:"mt-4 text-xs text-gray-500",children:"Click points to select journals. Selected journals will be highlighted in the table below."})]})};function q6(a,b){return"function"==typeof a?a(b):a}function q7(a,b){return c=>{b.setState(b=>({...b,[a]:q6(c,b[a])}))}}function q8(a){return a instanceof Function}function q9(a,b,c){let d,e=[];return f=>{let g,h;c.key&&c.debug&&(g=Date.now());let i=a(f);if(!(i.length!==e.length||i.some((a,b)=>e[b]!==a)))return d;if(e=i,c.key&&c.debug&&(h=Date.now()),d=b(...i),null==c||null==c.onChange||c.onChange(d),c.key&&c.debug&&null!=c&&c.debug()){let a=Math.round((Date.now()-g)*100)/100,b=Math.round((Date.now()-h)*100)/100,d=b/16,e=(a,b)=>{for(a=String(a);a.length<b;)a=" "+a;return a};console.info(`%c⏱ ${e(b,5)} /${e(a,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*d,120))}deg 100% 31%);`,null==c?void 0:c.key)}return d}}function ra(a,b,c,d){return{debug:()=>{var c;return null!=(c=null==a?void 0:a.debugAll)?c:a[b]},key:!1,onChange:d}}let rb="debugHeaders";function rc(a,b,c){var d;let e={id:null!=(d=c.id)?d:b.id,column:b,index:c.index,isPlaceholder:!!c.isPlaceholder,placeholderId:c.placeholderId,depth:c.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{let a=[],b=c=>{c.subHeaders&&c.subHeaders.length&&c.subHeaders.map(b),a.push(c)};return b(e),a},getContext:()=>({table:a,header:e,column:b})};return a._features.forEach(b=>{null==b.createHeader||b.createHeader(e,a)}),e}function rd(a,b,c,d){var e,f;let g=0,h=function(a,b){void 0===b&&(b=1),g=Math.max(g,b),a.filter(a=>a.getIsVisible()).forEach(a=>{var c;null!=(c=a.columns)&&c.length&&h(a.columns,b+1)},0)};h(a);let i=[],j=(a,b)=>{let e={depth:b,id:[d,`${b}`].filter(Boolean).join("_"),headers:[]},f=[];a.forEach(a=>{let g,h=[...f].reverse()[0],i=a.column.depth===e.depth,j=!1;if(i&&a.column.parent?g=a.column.parent:(g=a.column,j=!0),h&&(null==h?void 0:h.column)===g)h.subHeaders.push(a);else{let e=rc(c,g,{id:[d,b,g.id,null==a?void 0:a.id].filter(Boolean).join("_"),isPlaceholder:j,placeholderId:j?`${f.filter(a=>a.column===g).length}`:void 0,depth:b,index:f.length});e.subHeaders.push(a),f.push(e)}e.headers.push(a),a.headerGroup=e}),i.push(e),b>0&&j(f,b-1)};j(b.map((a,b)=>rc(c,a,{depth:g,index:b})),g-1),i.reverse();let k=a=>a.filter(a=>a.column.getIsVisible()).map(a=>{let b=0,c=0,d=[0];return a.subHeaders&&a.subHeaders.length?(d=[],k(a.subHeaders).forEach(a=>{let{colSpan:c,rowSpan:e}=a;b+=c,d.push(e)})):b=1,c+=Math.min(...d),a.colSpan=b,a.rowSpan=c,{colSpan:b,rowSpan:c}});return k(null!=(e=null==(f=i[0])?void 0:f.headers)?e:[]),i}let re=(a,b,c,d,e,f,g)=>{let h={id:b,index:d,original:c,depth:e,parentId:g,_valuesCache:{},_uniqueValuesCache:{},getValue:b=>{if(h._valuesCache.hasOwnProperty(b))return h._valuesCache[b];let c=a.getColumn(b);if(null!=c&&c.accessorFn)return h._valuesCache[b]=c.accessorFn(h.original,d),h._valuesCache[b]},getUniqueValues:b=>{if(h._uniqueValuesCache.hasOwnProperty(b))return h._uniqueValuesCache[b];let c=a.getColumn(b);if(null!=c&&c.accessorFn)return c.columnDef.getUniqueValues?h._uniqueValuesCache[b]=c.columnDef.getUniqueValues(h.original,d):h._uniqueValuesCache[b]=[h.getValue(b)],h._uniqueValuesCache[b]},renderValue:b=>{var c;return null!=(c=h.getValue(b))?c:a.options.renderFallbackValue},subRows:null!=f?f:[],getLeafRows:()=>(function(a,b){let c=[],d=a=>{a.forEach(a=>{c.push(a);let e=b(a);null!=e&&e.length&&d(e)})};return d(a),c})(h.subRows,a=>a.subRows),getParentRow:()=>h.parentId?a.getRow(h.parentId,!0):void 0,getParentRows:()=>{let a=[],b=h;for(;;){let c=b.getParentRow();if(!c)break;a.push(c),b=c}return a.reverse()},getAllCells:q9(()=>[a.getAllLeafColumns()],b=>b.map(b=>(function(a,b,c,d){let e={id:`${b.id}_${c.id}`,row:b,column:c,getValue:()=>b.getValue(d),renderValue:()=>{var b;return null!=(b=e.getValue())?b:a.options.renderFallbackValue},getContext:q9(()=>[a,c,b,e],(a,b,c,d)=>({table:a,column:b,row:c,cell:d,getValue:d.getValue,renderValue:d.renderValue}),ra(a.options,"debugCells","cell.getContext"))};return a._features.forEach(d=>{null==d.createCell||d.createCell(e,c,b,a)},{}),e})(a,h,b,b.id)),ra(a.options,"debugRows","getAllCells")),_getAllCellsByColumnId:q9(()=>[h.getAllCells()],a=>a.reduce((a,b)=>(a[b.column.id]=b,a),{}),ra(a.options,"debugRows","getAllCellsByColumnId"))};for(let b=0;b<a._features.length;b++){let c=a._features[b];null==c||null==c.createRow||c.createRow(h,a)}return h},rf=(a,b,c)=>{var d,e;let f=null==c||null==(d=c.toString())?void 0:d.toLowerCase();return!!(null==(e=a.getValue(b))||null==(e=e.toString())||null==(e=e.toLowerCase())?void 0:e.includes(f))};rf.autoRemove=a=>rp(a);let rg=(a,b,c)=>{var d;return!!(null==(d=a.getValue(b))||null==(d=d.toString())?void 0:d.includes(c))};rg.autoRemove=a=>rp(a);let rh=(a,b,c)=>{var d;return(null==(d=a.getValue(b))||null==(d=d.toString())?void 0:d.toLowerCase())===(null==c?void 0:c.toLowerCase())};rh.autoRemove=a=>rp(a);let ri=(a,b,c)=>{var d;return null==(d=a.getValue(b))?void 0:d.includes(c)};ri.autoRemove=a=>rp(a);let rj=(a,b,c)=>!c.some(c=>{var d;return!(null!=(d=a.getValue(b))&&d.includes(c))});rj.autoRemove=a=>rp(a)||!(null!=a&&a.length);let rk=(a,b,c)=>c.some(c=>{var d;return null==(d=a.getValue(b))?void 0:d.includes(c)});rk.autoRemove=a=>rp(a)||!(null!=a&&a.length);let rl=(a,b,c)=>a.getValue(b)===c;rl.autoRemove=a=>rp(a);let rm=(a,b,c)=>a.getValue(b)==c;rm.autoRemove=a=>rp(a);let rn=(a,b,c)=>{let[d,e]=c,f=a.getValue(b);return f>=d&&f<=e};rn.resolveFilterValue=a=>{let[b,c]=a,d="number"!=typeof b?parseFloat(b):b,e="number"!=typeof c?parseFloat(c):c,f=null===b||Number.isNaN(d)?-1/0:d,g=null===c||Number.isNaN(e)?1/0:e;if(f>g){let a=f;f=g,g=a}return[f,g]},rn.autoRemove=a=>rp(a)||rp(a[0])&&rp(a[1]);let ro={includesString:rf,includesStringSensitive:rg,equalsString:rh,arrIncludes:ri,arrIncludesAll:rj,arrIncludesSome:rk,equals:rl,weakEquals:rm,inNumberRange:rn};function rp(a){return null==a||""===a}function rq(a,b,c){return!!a&&!!a.autoRemove&&a.autoRemove(b,c)||void 0===b||"string"==typeof b&&!b}let rr={sum:(a,b,c)=>c.reduce((b,c)=>{let d=c.getValue(a);return b+("number"==typeof d?d:0)},0),min:(a,b,c)=>{let d;return c.forEach(b=>{let c=b.getValue(a);null!=c&&(d>c||void 0===d&&c>=c)&&(d=c)}),d},max:(a,b,c)=>{let d;return c.forEach(b=>{let c=b.getValue(a);null!=c&&(d<c||void 0===d&&c>=c)&&(d=c)}),d},extent:(a,b,c)=>{let d,e;return c.forEach(b=>{let c=b.getValue(a);null!=c&&(void 0===d?c>=c&&(d=e=c):(d>c&&(d=c),e<c&&(e=c)))}),[d,e]},mean:(a,b)=>{let c=0,d=0;if(b.forEach(b=>{let e=b.getValue(a);null!=e&&(e*=1)>=e&&(++c,d+=e)}),c)return d/c},median:(a,b)=>{if(!b.length)return;let c=b.map(b=>b.getValue(a));if(!function(a){return Array.isArray(a)&&a.every(a=>"number"==typeof a)}(c))return;if(1===c.length)return c[0];let d=Math.floor(c.length/2),e=c.sort((a,b)=>a-b);return c.length%2!=0?e[d]:(e[d-1]+e[d])/2},unique:(a,b)=>Array.from(new Set(b.map(b=>b.getValue(a))).values()),uniqueCount:(a,b)=>new Set(b.map(b=>b.getValue(a))).size,count:(a,b)=>b.length},rs=()=>({left:[],right:[]}),rt={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},ru=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),rv=null;function rw(a){return"touchstart"===a.type}function rx(a,b){return b?"center"===b?a.getCenterVisibleLeafColumns():"left"===b?a.getLeftVisibleLeafColumns():a.getRightVisibleLeafColumns():a.getVisibleLeafColumns()}let ry=()=>({pageIndex:0,pageSize:10}),rz=()=>({top:[],bottom:[]}),rA=(a,b,c,d,e)=>{var f;let g=e.getRow(b,!0);c?(g.getCanMultiSelect()||Object.keys(a).forEach(b=>delete a[b]),g.getCanSelect()&&(a[b]=!0)):delete a[b],d&&null!=(f=g.subRows)&&f.length&&g.getCanSelectSubRows()&&g.subRows.forEach(b=>rA(a,b.id,c,d,e))};function rB(a,b){let c=a.getState().rowSelection,d=[],e={},f=function(a,b){return a.map(a=>{var b;let g=rC(a,c);if(g&&(d.push(a),e[a.id]=a),null!=(b=a.subRows)&&b.length&&(a={...a,subRows:f(a.subRows)}),g)return a}).filter(Boolean)};return{rows:f(b.rows),flatRows:d,rowsById:e}}function rC(a,b){var c;return null!=(c=b[a.id])&&c}function rD(a,b,c){var d;if(!(null!=(d=a.subRows)&&d.length))return!1;let e=!0,f=!1;return a.subRows.forEach(a=>{if((!f||e)&&(a.getCanSelect()&&(rC(a,b)?f=!0:e=!1),a.subRows&&a.subRows.length)){let c=rD(a,b);"all"===c?f=!0:("some"===c&&(f=!0),e=!1)}}),e?"all":!!f&&"some"}let rE=/([0-9]+)/gm;function rF(a,b){return a===b?0:a>b?1:-1}function rG(a){return"number"==typeof a?isNaN(a)||a===1/0||a===-1/0?"":String(a):"string"==typeof a?a:""}function rH(a,b){let c=a.split(rE).filter(Boolean),d=b.split(rE).filter(Boolean);for(;c.length&&d.length;){let a=c.shift(),b=d.shift(),e=parseInt(a,10),f=parseInt(b,10),g=[e,f].sort();if(isNaN(g[0])){if(a>b)return 1;if(b>a)return -1;continue}if(isNaN(g[1]))return isNaN(e)?-1:1;if(e>f)return 1;if(f>e)return -1}return c.length-d.length}let rI={alphanumeric:(a,b,c)=>rH(rG(a.getValue(c)).toLowerCase(),rG(b.getValue(c)).toLowerCase()),alphanumericCaseSensitive:(a,b,c)=>rH(rG(a.getValue(c)),rG(b.getValue(c))),text:(a,b,c)=>rF(rG(a.getValue(c)).toLowerCase(),rG(b.getValue(c)).toLowerCase()),textCaseSensitive:(a,b,c)=>rF(rG(a.getValue(c)),rG(b.getValue(c))),datetime:(a,b,c)=>{let d=a.getValue(c),e=b.getValue(c);return d>e?1:d<e?-1:0},basic:(a,b,c)=>rF(a.getValue(c),b.getValue(c))},rJ=[{createTable:a=>{a.getHeaderGroups=q9(()=>[a.getAllColumns(),a.getVisibleLeafColumns(),a.getState().columnPinning.left,a.getState().columnPinning.right],(b,c,d,e)=>{var f,g;let h=null!=(f=null==d?void 0:d.map(a=>c.find(b=>b.id===a)).filter(Boolean))?f:[],i=null!=(g=null==e?void 0:e.map(a=>c.find(b=>b.id===a)).filter(Boolean))?g:[];return rd(b,[...h,...c.filter(a=>!(null!=d&&d.includes(a.id))&&!(null!=e&&e.includes(a.id))),...i],a)},ra(a.options,rb,"getHeaderGroups")),a.getCenterHeaderGroups=q9(()=>[a.getAllColumns(),a.getVisibleLeafColumns(),a.getState().columnPinning.left,a.getState().columnPinning.right],(b,c,d,e)=>rd(b,c=c.filter(a=>!(null!=d&&d.includes(a.id))&&!(null!=e&&e.includes(a.id))),a,"center"),ra(a.options,rb,"getCenterHeaderGroups")),a.getLeftHeaderGroups=q9(()=>[a.getAllColumns(),a.getVisibleLeafColumns(),a.getState().columnPinning.left],(b,c,d)=>{var e;return rd(b,null!=(e=null==d?void 0:d.map(a=>c.find(b=>b.id===a)).filter(Boolean))?e:[],a,"left")},ra(a.options,rb,"getLeftHeaderGroups")),a.getRightHeaderGroups=q9(()=>[a.getAllColumns(),a.getVisibleLeafColumns(),a.getState().columnPinning.right],(b,c,d)=>{var e;return rd(b,null!=(e=null==d?void 0:d.map(a=>c.find(b=>b.id===a)).filter(Boolean))?e:[],a,"right")},ra(a.options,rb,"getRightHeaderGroups")),a.getFooterGroups=q9(()=>[a.getHeaderGroups()],a=>[...a].reverse(),ra(a.options,rb,"getFooterGroups")),a.getLeftFooterGroups=q9(()=>[a.getLeftHeaderGroups()],a=>[...a].reverse(),ra(a.options,rb,"getLeftFooterGroups")),a.getCenterFooterGroups=q9(()=>[a.getCenterHeaderGroups()],a=>[...a].reverse(),ra(a.options,rb,"getCenterFooterGroups")),a.getRightFooterGroups=q9(()=>[a.getRightHeaderGroups()],a=>[...a].reverse(),ra(a.options,rb,"getRightFooterGroups")),a.getFlatHeaders=q9(()=>[a.getHeaderGroups()],a=>a.map(a=>a.headers).flat(),ra(a.options,rb,"getFlatHeaders")),a.getLeftFlatHeaders=q9(()=>[a.getLeftHeaderGroups()],a=>a.map(a=>a.headers).flat(),ra(a.options,rb,"getLeftFlatHeaders")),a.getCenterFlatHeaders=q9(()=>[a.getCenterHeaderGroups()],a=>a.map(a=>a.headers).flat(),ra(a.options,rb,"getCenterFlatHeaders")),a.getRightFlatHeaders=q9(()=>[a.getRightHeaderGroups()],a=>a.map(a=>a.headers).flat(),ra(a.options,rb,"getRightFlatHeaders")),a.getCenterLeafHeaders=q9(()=>[a.getCenterFlatHeaders()],a=>a.filter(a=>{var b;return!(null!=(b=a.subHeaders)&&b.length)}),ra(a.options,rb,"getCenterLeafHeaders")),a.getLeftLeafHeaders=q9(()=>[a.getLeftFlatHeaders()],a=>a.filter(a=>{var b;return!(null!=(b=a.subHeaders)&&b.length)}),ra(a.options,rb,"getLeftLeafHeaders")),a.getRightLeafHeaders=q9(()=>[a.getRightFlatHeaders()],a=>a.filter(a=>{var b;return!(null!=(b=a.subHeaders)&&b.length)}),ra(a.options,rb,"getRightLeafHeaders")),a.getLeafHeaders=q9(()=>[a.getLeftHeaderGroups(),a.getCenterHeaderGroups(),a.getRightHeaderGroups()],(a,b,c)=>{var d,e,f,g,h,i;return[...null!=(d=null==(e=a[0])?void 0:e.headers)?d:[],...null!=(f=null==(g=b[0])?void 0:g.headers)?f:[],...null!=(h=null==(i=c[0])?void 0:i.headers)?h:[]].map(a=>a.getLeafHeaders()).flat()},ra(a.options,rb,"getLeafHeaders"))}},{getInitialState:a=>({columnVisibility:{},...a}),getDefaultOptions:a=>({onColumnVisibilityChange:q7("columnVisibility",a)}),createColumn:(a,b)=>{a.toggleVisibility=c=>{a.getCanHide()&&b.setColumnVisibility(b=>({...b,[a.id]:null!=c?c:!a.getIsVisible()}))},a.getIsVisible=()=>{var c,d;let e=a.columns;return null==(c=e.length?e.some(a=>a.getIsVisible()):null==(d=b.getState().columnVisibility)?void 0:d[a.id])||c},a.getCanHide=()=>{var c,d;return(null==(c=a.columnDef.enableHiding)||c)&&(null==(d=b.options.enableHiding)||d)},a.getToggleVisibilityHandler=()=>b=>{null==a.toggleVisibility||a.toggleVisibility(b.target.checked)}},createRow:(a,b)=>{a._getAllVisibleCells=q9(()=>[a.getAllCells(),b.getState().columnVisibility],a=>a.filter(a=>a.column.getIsVisible()),ra(b.options,"debugRows","_getAllVisibleCells")),a.getVisibleCells=q9(()=>[a.getLeftVisibleCells(),a.getCenterVisibleCells(),a.getRightVisibleCells()],(a,b,c)=>[...a,...b,...c],ra(b.options,"debugRows","getVisibleCells"))},createTable:a=>{let b=(b,c)=>q9(()=>[c(),c().filter(a=>a.getIsVisible()).map(a=>a.id).join("_")],a=>a.filter(a=>null==a.getIsVisible?void 0:a.getIsVisible()),ra(a.options,"debugColumns",b));a.getVisibleFlatColumns=b("getVisibleFlatColumns",()=>a.getAllFlatColumns()),a.getVisibleLeafColumns=b("getVisibleLeafColumns",()=>a.getAllLeafColumns()),a.getLeftVisibleLeafColumns=b("getLeftVisibleLeafColumns",()=>a.getLeftLeafColumns()),a.getRightVisibleLeafColumns=b("getRightVisibleLeafColumns",()=>a.getRightLeafColumns()),a.getCenterVisibleLeafColumns=b("getCenterVisibleLeafColumns",()=>a.getCenterLeafColumns()),a.setColumnVisibility=b=>null==a.options.onColumnVisibilityChange?void 0:a.options.onColumnVisibilityChange(b),a.resetColumnVisibility=b=>{var c;a.setColumnVisibility(b?{}:null!=(c=a.initialState.columnVisibility)?c:{})},a.toggleAllColumnsVisible=b=>{var c;b=null!=(c=b)?c:!a.getIsAllColumnsVisible(),a.setColumnVisibility(a.getAllLeafColumns().reduce((a,c)=>({...a,[c.id]:b||!(null!=c.getCanHide&&c.getCanHide())}),{}))},a.getIsAllColumnsVisible=()=>!a.getAllLeafColumns().some(a=>!(null!=a.getIsVisible&&a.getIsVisible())),a.getIsSomeColumnsVisible=()=>a.getAllLeafColumns().some(a=>null==a.getIsVisible?void 0:a.getIsVisible()),a.getToggleAllColumnsVisibilityHandler=()=>b=>{var c;a.toggleAllColumnsVisible(null==(c=b.target)?void 0:c.checked)}}},{getInitialState:a=>({columnOrder:[],...a}),getDefaultOptions:a=>({onColumnOrderChange:q7("columnOrder",a)}),createColumn:(a,b)=>{a.getIndex=q9(a=>[rx(b,a)],b=>b.findIndex(b=>b.id===a.id),ra(b.options,"debugColumns","getIndex")),a.getIsFirstColumn=c=>{var d;return(null==(d=rx(b,c)[0])?void 0:d.id)===a.id},a.getIsLastColumn=c=>{var d;let e=rx(b,c);return(null==(d=e[e.length-1])?void 0:d.id)===a.id}},createTable:a=>{a.setColumnOrder=b=>null==a.options.onColumnOrderChange?void 0:a.options.onColumnOrderChange(b),a.resetColumnOrder=b=>{var c;a.setColumnOrder(b?[]:null!=(c=a.initialState.columnOrder)?c:[])},a._getOrderColumnsFn=q9(()=>[a.getState().columnOrder,a.getState().grouping,a.options.groupedColumnMode],(a,b,c)=>d=>{let e=[];if(null!=a&&a.length){let b=[...a],c=[...d];for(;c.length&&b.length;){let a=b.shift(),d=c.findIndex(b=>b.id===a);d>-1&&e.push(c.splice(d,1)[0])}e=[...e,...c]}else e=d;return function(a,b,c){if(!(null!=b&&b.length)||!c)return a;let d=a.filter(a=>!b.includes(a.id));return"remove"===c?d:[...b.map(b=>a.find(a=>a.id===b)).filter(Boolean),...d]}(e,b,c)},ra(a.options,"debugTable","_getOrderColumnsFn"))}},{getInitialState:a=>({columnPinning:rs(),...a}),getDefaultOptions:a=>({onColumnPinningChange:q7("columnPinning",a)}),createColumn:(a,b)=>{a.pin=c=>{let d=a.getLeafColumns().map(a=>a.id).filter(Boolean);b.setColumnPinning(a=>{var b,e,f,g,h,i;return"right"===c?{left:(null!=(f=null==a?void 0:a.left)?f:[]).filter(a=>!(null!=d&&d.includes(a))),right:[...(null!=(g=null==a?void 0:a.right)?g:[]).filter(a=>!(null!=d&&d.includes(a))),...d]}:"left"===c?{left:[...(null!=(h=null==a?void 0:a.left)?h:[]).filter(a=>!(null!=d&&d.includes(a))),...d],right:(null!=(i=null==a?void 0:a.right)?i:[]).filter(a=>!(null!=d&&d.includes(a)))}:{left:(null!=(b=null==a?void 0:a.left)?b:[]).filter(a=>!(null!=d&&d.includes(a))),right:(null!=(e=null==a?void 0:a.right)?e:[]).filter(a=>!(null!=d&&d.includes(a)))}})},a.getCanPin=()=>a.getLeafColumns().some(a=>{var c,d,e;return(null==(c=a.columnDef.enablePinning)||c)&&(null==(d=null!=(e=b.options.enableColumnPinning)?e:b.options.enablePinning)||d)}),a.getIsPinned=()=>{let c=a.getLeafColumns().map(a=>a.id),{left:d,right:e}=b.getState().columnPinning,f=c.some(a=>null==d?void 0:d.includes(a)),g=c.some(a=>null==e?void 0:e.includes(a));return f?"left":!!g&&"right"},a.getPinnedIndex=()=>{var c,d;let e=a.getIsPinned();return e?null!=(c=null==(d=b.getState().columnPinning)||null==(d=d[e])?void 0:d.indexOf(a.id))?c:-1:0}},createRow:(a,b)=>{a.getCenterVisibleCells=q9(()=>[a._getAllVisibleCells(),b.getState().columnPinning.left,b.getState().columnPinning.right],(a,b,c)=>{let d=[...null!=b?b:[],...null!=c?c:[]];return a.filter(a=>!d.includes(a.column.id))},ra(b.options,"debugRows","getCenterVisibleCells")),a.getLeftVisibleCells=q9(()=>[a._getAllVisibleCells(),b.getState().columnPinning.left],(a,b)=>(null!=b?b:[]).map(b=>a.find(a=>a.column.id===b)).filter(Boolean).map(a=>({...a,position:"left"})),ra(b.options,"debugRows","getLeftVisibleCells")),a.getRightVisibleCells=q9(()=>[a._getAllVisibleCells(),b.getState().columnPinning.right],(a,b)=>(null!=b?b:[]).map(b=>a.find(a=>a.column.id===b)).filter(Boolean).map(a=>({...a,position:"right"})),ra(b.options,"debugRows","getRightVisibleCells"))},createTable:a=>{a.setColumnPinning=b=>null==a.options.onColumnPinningChange?void 0:a.options.onColumnPinningChange(b),a.resetColumnPinning=b=>{var c,d;return a.setColumnPinning(b?rs():null!=(c=null==(d=a.initialState)?void 0:d.columnPinning)?c:rs())},a.getIsSomeColumnsPinned=b=>{var c,d,e;let f=a.getState().columnPinning;return b?!!(null==(c=f[b])?void 0:c.length):!!((null==(d=f.left)?void 0:d.length)||(null==(e=f.right)?void 0:e.length))},a.getLeftLeafColumns=q9(()=>[a.getAllLeafColumns(),a.getState().columnPinning.left],(a,b)=>(null!=b?b:[]).map(b=>a.find(a=>a.id===b)).filter(Boolean),ra(a.options,"debugColumns","getLeftLeafColumns")),a.getRightLeafColumns=q9(()=>[a.getAllLeafColumns(),a.getState().columnPinning.right],(a,b)=>(null!=b?b:[]).map(b=>a.find(a=>a.id===b)).filter(Boolean),ra(a.options,"debugColumns","getRightLeafColumns")),a.getCenterLeafColumns=q9(()=>[a.getAllLeafColumns(),a.getState().columnPinning.left,a.getState().columnPinning.right],(a,b,c)=>{let d=[...null!=b?b:[],...null!=c?c:[]];return a.filter(a=>!d.includes(a.id))},ra(a.options,"debugColumns","getCenterLeafColumns"))}},{createColumn:(a,b)=>{a._getFacetedRowModel=b.options.getFacetedRowModel&&b.options.getFacetedRowModel(b,a.id),a.getFacetedRowModel=()=>a._getFacetedRowModel?a._getFacetedRowModel():b.getPreFilteredRowModel(),a._getFacetedUniqueValues=b.options.getFacetedUniqueValues&&b.options.getFacetedUniqueValues(b,a.id),a.getFacetedUniqueValues=()=>a._getFacetedUniqueValues?a._getFacetedUniqueValues():new Map,a._getFacetedMinMaxValues=b.options.getFacetedMinMaxValues&&b.options.getFacetedMinMaxValues(b,a.id),a.getFacetedMinMaxValues=()=>{if(a._getFacetedMinMaxValues)return a._getFacetedMinMaxValues()}}},{getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:a=>({columnFilters:[],...a}),getDefaultOptions:a=>({onColumnFiltersChange:q7("columnFilters",a),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(a,b)=>{a.getAutoFilterFn=()=>{let c=b.getCoreRowModel().flatRows[0],d=null==c?void 0:c.getValue(a.id);return"string"==typeof d?ro.includesString:"number"==typeof d?ro.inNumberRange:"boolean"==typeof d||null!==d&&"object"==typeof d?ro.equals:Array.isArray(d)?ro.arrIncludes:ro.weakEquals},a.getFilterFn=()=>{var c,d;return q8(a.columnDef.filterFn)?a.columnDef.filterFn:"auto"===a.columnDef.filterFn?a.getAutoFilterFn():null!=(c=null==(d=b.options.filterFns)?void 0:d[a.columnDef.filterFn])?c:ro[a.columnDef.filterFn]},a.getCanFilter=()=>{var c,d,e;return(null==(c=a.columnDef.enableColumnFilter)||c)&&(null==(d=b.options.enableColumnFilters)||d)&&(null==(e=b.options.enableFilters)||e)&&!!a.accessorFn},a.getIsFiltered=()=>a.getFilterIndex()>-1,a.getFilterValue=()=>{var c;return null==(c=b.getState().columnFilters)||null==(c=c.find(b=>b.id===a.id))?void 0:c.value},a.getFilterIndex=()=>{var c,d;return null!=(c=null==(d=b.getState().columnFilters)?void 0:d.findIndex(b=>b.id===a.id))?c:-1},a.setFilterValue=c=>{b.setColumnFilters(b=>{var d,e;let f=a.getFilterFn(),g=null==b?void 0:b.find(b=>b.id===a.id),h=q6(c,g?g.value:void 0);if(rq(f,h,a))return null!=(d=null==b?void 0:b.filter(b=>b.id!==a.id))?d:[];let i={id:a.id,value:h};return g?null!=(e=null==b?void 0:b.map(b=>b.id===a.id?i:b))?e:[]:null!=b&&b.length?[...b,i]:[i]})}},createRow:(a,b)=>{a.columnFilters={},a.columnFiltersMeta={}},createTable:a=>{a.setColumnFilters=b=>{let c=a.getAllLeafColumns();null==a.options.onColumnFiltersChange||a.options.onColumnFiltersChange(a=>{var d;return null==(d=q6(b,a))?void 0:d.filter(a=>{let b=c.find(b=>b.id===a.id);return!(b&&rq(b.getFilterFn(),a.value,b))&&!0})})},a.resetColumnFilters=b=>{var c,d;a.setColumnFilters(b?[]:null!=(c=null==(d=a.initialState)?void 0:d.columnFilters)?c:[])},a.getPreFilteredRowModel=()=>a.getCoreRowModel(),a.getFilteredRowModel=()=>(!a._getFilteredRowModel&&a.options.getFilteredRowModel&&(a._getFilteredRowModel=a.options.getFilteredRowModel(a)),a.options.manualFiltering||!a._getFilteredRowModel)?a.getPreFilteredRowModel():a._getFilteredRowModel()}},{createTable:a=>{a._getGlobalFacetedRowModel=a.options.getFacetedRowModel&&a.options.getFacetedRowModel(a,"__global__"),a.getGlobalFacetedRowModel=()=>a.options.manualFiltering||!a._getGlobalFacetedRowModel?a.getPreFilteredRowModel():a._getGlobalFacetedRowModel(),a._getGlobalFacetedUniqueValues=a.options.getFacetedUniqueValues&&a.options.getFacetedUniqueValues(a,"__global__"),a.getGlobalFacetedUniqueValues=()=>a._getGlobalFacetedUniqueValues?a._getGlobalFacetedUniqueValues():new Map,a._getGlobalFacetedMinMaxValues=a.options.getFacetedMinMaxValues&&a.options.getFacetedMinMaxValues(a,"__global__"),a.getGlobalFacetedMinMaxValues=()=>{if(a._getGlobalFacetedMinMaxValues)return a._getGlobalFacetedMinMaxValues()}}},{getInitialState:a=>({globalFilter:void 0,...a}),getDefaultOptions:a=>({onGlobalFilterChange:q7("globalFilter",a),globalFilterFn:"auto",getColumnCanGlobalFilter:b=>{var c;let d=null==(c=a.getCoreRowModel().flatRows[0])||null==(c=c._getAllCellsByColumnId()[b.id])?void 0:c.getValue();return"string"==typeof d||"number"==typeof d}}),createColumn:(a,b)=>{a.getCanGlobalFilter=()=>{var c,d,e,f;return(null==(c=a.columnDef.enableGlobalFilter)||c)&&(null==(d=b.options.enableGlobalFilter)||d)&&(null==(e=b.options.enableFilters)||e)&&(null==(f=null==b.options.getColumnCanGlobalFilter?void 0:b.options.getColumnCanGlobalFilter(a))||f)&&!!a.accessorFn}},createTable:a=>{a.getGlobalAutoFilterFn=()=>ro.includesString,a.getGlobalFilterFn=()=>{var b,c;let{globalFilterFn:d}=a.options;return q8(d)?d:"auto"===d?a.getGlobalAutoFilterFn():null!=(b=null==(c=a.options.filterFns)?void 0:c[d])?b:ro[d]},a.setGlobalFilter=b=>{null==a.options.onGlobalFilterChange||a.options.onGlobalFilterChange(b)},a.resetGlobalFilter=b=>{a.setGlobalFilter(b?void 0:a.initialState.globalFilter)}}},{getInitialState:a=>({sorting:[],...a}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:a=>({onSortingChange:q7("sorting",a),isMultiSortEvent:a=>a.shiftKey}),createColumn:(a,b)=>{a.getAutoSortingFn=()=>{let c=b.getFilteredRowModel().flatRows.slice(10),d=!1;for(let b of c){let c=null==b?void 0:b.getValue(a.id);if("[object Date]"===Object.prototype.toString.call(c))return rI.datetime;if("string"==typeof c&&(d=!0,c.split(rE).length>1))return rI.alphanumeric}return d?rI.text:rI.basic},a.getAutoSortDir=()=>{let c=b.getFilteredRowModel().flatRows[0];return"string"==typeof(null==c?void 0:c.getValue(a.id))?"asc":"desc"},a.getSortingFn=()=>{var c,d;if(!a)throw Error();return q8(a.columnDef.sortingFn)?a.columnDef.sortingFn:"auto"===a.columnDef.sortingFn?a.getAutoSortingFn():null!=(c=null==(d=b.options.sortingFns)?void 0:d[a.columnDef.sortingFn])?c:rI[a.columnDef.sortingFn]},a.toggleSorting=(c,d)=>{let e=a.getNextSortingOrder(),f=null!=c;b.setSorting(g=>{let h,i=null==g?void 0:g.find(b=>b.id===a.id),j=null==g?void 0:g.findIndex(b=>b.id===a.id),k=[],l=f?c:"desc"===e;if("toggle"!=(h=null!=g&&g.length&&a.getCanMultiSort()&&d?i?"toggle":"add":null!=g&&g.length&&j!==g.length-1?"replace":i?"toggle":"replace")||f||e||(h="remove"),"add"===h){var m;(k=[...g,{id:a.id,desc:l}]).splice(0,k.length-(null!=(m=b.options.maxMultiSortColCount)?m:Number.MAX_SAFE_INTEGER))}else k="toggle"===h?g.map(b=>b.id===a.id?{...b,desc:l}:b):"remove"===h?g.filter(b=>b.id!==a.id):[{id:a.id,desc:l}];return k})},a.getFirstSortDir=()=>{var c,d;return(null!=(c=null!=(d=a.columnDef.sortDescFirst)?d:b.options.sortDescFirst)?c:"desc"===a.getAutoSortDir())?"desc":"asc"},a.getNextSortingOrder=c=>{var d,e;let f=a.getFirstSortDir(),g=a.getIsSorted();return g?(g===f||null!=(d=b.options.enableSortingRemoval)&&!d||!!c&&null!=(e=b.options.enableMultiRemove)&&!e)&&("desc"===g?"asc":"desc"):f},a.getCanSort=()=>{var c,d;return(null==(c=a.columnDef.enableSorting)||c)&&(null==(d=b.options.enableSorting)||d)&&!!a.accessorFn},a.getCanMultiSort=()=>{var c,d;return null!=(c=null!=(d=a.columnDef.enableMultiSort)?d:b.options.enableMultiSort)?c:!!a.accessorFn},a.getIsSorted=()=>{var c;let d=null==(c=b.getState().sorting)?void 0:c.find(b=>b.id===a.id);return!!d&&(d.desc?"desc":"asc")},a.getSortIndex=()=>{var c,d;return null!=(c=null==(d=b.getState().sorting)?void 0:d.findIndex(b=>b.id===a.id))?c:-1},a.clearSorting=()=>{b.setSorting(b=>null!=b&&b.length?b.filter(b=>b.id!==a.id):[])},a.getToggleSortingHandler=()=>{let c=a.getCanSort();return d=>{c&&(null==d.persist||d.persist(),null==a.toggleSorting||a.toggleSorting(void 0,!!a.getCanMultiSort()&&(null==b.options.isMultiSortEvent?void 0:b.options.isMultiSortEvent(d))))}}},createTable:a=>{a.setSorting=b=>null==a.options.onSortingChange?void 0:a.options.onSortingChange(b),a.resetSorting=b=>{var c,d;a.setSorting(b?[]:null!=(c=null==(d=a.initialState)?void 0:d.sorting)?c:[])},a.getPreSortedRowModel=()=>a.getGroupedRowModel(),a.getSortedRowModel=()=>(!a._getSortedRowModel&&a.options.getSortedRowModel&&(a._getSortedRowModel=a.options.getSortedRowModel(a)),a.options.manualSorting||!a._getSortedRowModel)?a.getPreSortedRowModel():a._getSortedRowModel()}},{getDefaultColumnDef:()=>({aggregatedCell:a=>{var b,c;return null!=(b=null==(c=a.getValue())||null==c.toString?void 0:c.toString())?b:null},aggregationFn:"auto"}),getInitialState:a=>({grouping:[],...a}),getDefaultOptions:a=>({onGroupingChange:q7("grouping",a),groupedColumnMode:"reorder"}),createColumn:(a,b)=>{a.toggleGrouping=()=>{b.setGrouping(b=>null!=b&&b.includes(a.id)?b.filter(b=>b!==a.id):[...null!=b?b:[],a.id])},a.getCanGroup=()=>{var c,d;return(null==(c=a.columnDef.enableGrouping)||c)&&(null==(d=b.options.enableGrouping)||d)&&(!!a.accessorFn||!!a.columnDef.getGroupingValue)},a.getIsGrouped=()=>{var c;return null==(c=b.getState().grouping)?void 0:c.includes(a.id)},a.getGroupedIndex=()=>{var c;return null==(c=b.getState().grouping)?void 0:c.indexOf(a.id)},a.getToggleGroupingHandler=()=>{let b=a.getCanGroup();return()=>{b&&a.toggleGrouping()}},a.getAutoAggregationFn=()=>{let c=b.getCoreRowModel().flatRows[0],d=null==c?void 0:c.getValue(a.id);return"number"==typeof d?rr.sum:"[object Date]"===Object.prototype.toString.call(d)?rr.extent:void 0},a.getAggregationFn=()=>{var c,d;if(!a)throw Error();return q8(a.columnDef.aggregationFn)?a.columnDef.aggregationFn:"auto"===a.columnDef.aggregationFn?a.getAutoAggregationFn():null!=(c=null==(d=b.options.aggregationFns)?void 0:d[a.columnDef.aggregationFn])?c:rr[a.columnDef.aggregationFn]}},createTable:a=>{a.setGrouping=b=>null==a.options.onGroupingChange?void 0:a.options.onGroupingChange(b),a.resetGrouping=b=>{var c,d;a.setGrouping(b?[]:null!=(c=null==(d=a.initialState)?void 0:d.grouping)?c:[])},a.getPreGroupedRowModel=()=>a.getFilteredRowModel(),a.getGroupedRowModel=()=>(!a._getGroupedRowModel&&a.options.getGroupedRowModel&&(a._getGroupedRowModel=a.options.getGroupedRowModel(a)),a.options.manualGrouping||!a._getGroupedRowModel)?a.getPreGroupedRowModel():a._getGroupedRowModel()},createRow:(a,b)=>{a.getIsGrouped=()=>!!a.groupingColumnId,a.getGroupingValue=c=>{if(a._groupingValuesCache.hasOwnProperty(c))return a._groupingValuesCache[c];let d=b.getColumn(c);return null!=d&&d.columnDef.getGroupingValue?(a._groupingValuesCache[c]=d.columnDef.getGroupingValue(a.original),a._groupingValuesCache[c]):a.getValue(c)},a._groupingValuesCache={}},createCell:(a,b,c,d)=>{a.getIsGrouped=()=>b.getIsGrouped()&&b.id===c.groupingColumnId,a.getIsPlaceholder=()=>!a.getIsGrouped()&&b.getIsGrouped(),a.getIsAggregated=()=>{var b;return!a.getIsGrouped()&&!a.getIsPlaceholder()&&!!(null!=(b=c.subRows)&&b.length)}}},{getInitialState:a=>({expanded:{},...a}),getDefaultOptions:a=>({onExpandedChange:q7("expanded",a),paginateExpandedRows:!0}),createTable:a=>{let b=!1,c=!1;a._autoResetExpanded=()=>{var d,e;if(!b)return void a._queue(()=>{b=!0});if(null!=(d=null!=(e=a.options.autoResetAll)?e:a.options.autoResetExpanded)?d:!a.options.manualExpanding){if(c)return;c=!0,a._queue(()=>{a.resetExpanded(),c=!1})}},a.setExpanded=b=>null==a.options.onExpandedChange?void 0:a.options.onExpandedChange(b),a.toggleAllRowsExpanded=b=>{(null!=b?b:!a.getIsAllRowsExpanded())?a.setExpanded(!0):a.setExpanded({})},a.resetExpanded=b=>{var c,d;a.setExpanded(b?{}:null!=(c=null==(d=a.initialState)?void 0:d.expanded)?c:{})},a.getCanSomeRowsExpand=()=>a.getPrePaginationRowModel().flatRows.some(a=>a.getCanExpand()),a.getToggleAllRowsExpandedHandler=()=>b=>{null==b.persist||b.persist(),a.toggleAllRowsExpanded()},a.getIsSomeRowsExpanded=()=>{let b=a.getState().expanded;return!0===b||Object.values(b).some(Boolean)},a.getIsAllRowsExpanded=()=>{let b=a.getState().expanded;return"boolean"==typeof b?!0===b:!(!Object.keys(b).length||a.getRowModel().flatRows.some(a=>!a.getIsExpanded()))},a.getExpandedDepth=()=>{let b=0;return(!0===a.getState().expanded?Object.keys(a.getRowModel().rowsById):Object.keys(a.getState().expanded)).forEach(a=>{let c=a.split(".");b=Math.max(b,c.length)}),b},a.getPreExpandedRowModel=()=>a.getSortedRowModel(),a.getExpandedRowModel=()=>(!a._getExpandedRowModel&&a.options.getExpandedRowModel&&(a._getExpandedRowModel=a.options.getExpandedRowModel(a)),a.options.manualExpanding||!a._getExpandedRowModel)?a.getPreExpandedRowModel():a._getExpandedRowModel()},createRow:(a,b)=>{a.toggleExpanded=c=>{b.setExpanded(d=>{var e;let f=!0===d||!!(null!=d&&d[a.id]),g={};if(!0===d?Object.keys(b.getRowModel().rowsById).forEach(a=>{g[a]=!0}):g=d,c=null!=(e=c)?e:!f,!f&&c)return{...g,[a.id]:!0};if(f&&!c){let{[a.id]:b,...c}=g;return c}return d})},a.getIsExpanded=()=>{var c;let d=b.getState().expanded;return!!(null!=(c=null==b.options.getIsRowExpanded?void 0:b.options.getIsRowExpanded(a))?c:!0===d||(null==d?void 0:d[a.id]))},a.getCanExpand=()=>{var c,d,e;return null!=(c=null==b.options.getRowCanExpand?void 0:b.options.getRowCanExpand(a))?c:(null==(d=b.options.enableExpanding)||d)&&!!(null!=(e=a.subRows)&&e.length)},a.getIsAllParentsExpanded=()=>{let c=!0,d=a;for(;c&&d.parentId;)c=(d=b.getRow(d.parentId,!0)).getIsExpanded();return c},a.getToggleExpandedHandler=()=>{let b=a.getCanExpand();return()=>{b&&a.toggleExpanded()}}}},{getInitialState:a=>({...a,pagination:{...ry(),...null==a?void 0:a.pagination}}),getDefaultOptions:a=>({onPaginationChange:q7("pagination",a)}),createTable:a=>{let b=!1,c=!1;a._autoResetPageIndex=()=>{var d,e;if(!b)return void a._queue(()=>{b=!0});if(null!=(d=null!=(e=a.options.autoResetAll)?e:a.options.autoResetPageIndex)?d:!a.options.manualPagination){if(c)return;c=!0,a._queue(()=>{a.resetPageIndex(),c=!1})}},a.setPagination=b=>null==a.options.onPaginationChange?void 0:a.options.onPaginationChange(a=>q6(b,a)),a.resetPagination=b=>{var c;a.setPagination(b?ry():null!=(c=a.initialState.pagination)?c:ry())},a.setPageIndex=b=>{a.setPagination(c=>{let d=q6(b,c.pageIndex);return d=Math.max(0,Math.min(d,void 0===a.options.pageCount||-1===a.options.pageCount?Number.MAX_SAFE_INTEGER:a.options.pageCount-1)),{...c,pageIndex:d}})},a.resetPageIndex=b=>{var c,d;a.setPageIndex(b?0:null!=(c=null==(d=a.initialState)||null==(d=d.pagination)?void 0:d.pageIndex)?c:0)},a.resetPageSize=b=>{var c,d;a.setPageSize(b?10:null!=(c=null==(d=a.initialState)||null==(d=d.pagination)?void 0:d.pageSize)?c:10)},a.setPageSize=b=>{a.setPagination(a=>{let c=Math.max(1,q6(b,a.pageSize)),d=Math.floor(a.pageSize*a.pageIndex/c);return{...a,pageIndex:d,pageSize:c}})},a.setPageCount=b=>a.setPagination(c=>{var d;let e=q6(b,null!=(d=a.options.pageCount)?d:-1);return"number"==typeof e&&(e=Math.max(-1,e)),{...c,pageCount:e}}),a.getPageOptions=q9(()=>[a.getPageCount()],a=>{let b=[];return a&&a>0&&(b=[...Array(a)].fill(null).map((a,b)=>b)),b},ra(a.options,"debugTable","getPageOptions")),a.getCanPreviousPage=()=>a.getState().pagination.pageIndex>0,a.getCanNextPage=()=>{let{pageIndex:b}=a.getState().pagination,c=a.getPageCount();return -1===c||0!==c&&b<c-1},a.previousPage=()=>a.setPageIndex(a=>a-1),a.nextPage=()=>a.setPageIndex(a=>a+1),a.firstPage=()=>a.setPageIndex(0),a.lastPage=()=>a.setPageIndex(a.getPageCount()-1),a.getPrePaginationRowModel=()=>a.getExpandedRowModel(),a.getPaginationRowModel=()=>(!a._getPaginationRowModel&&a.options.getPaginationRowModel&&(a._getPaginationRowModel=a.options.getPaginationRowModel(a)),a.options.manualPagination||!a._getPaginationRowModel)?a.getPrePaginationRowModel():a._getPaginationRowModel(),a.getPageCount=()=>{var b;return null!=(b=a.options.pageCount)?b:Math.ceil(a.getRowCount()/a.getState().pagination.pageSize)},a.getRowCount=()=>{var b;return null!=(b=a.options.rowCount)?b:a.getPrePaginationRowModel().rows.length}}},{getInitialState:a=>({rowPinning:rz(),...a}),getDefaultOptions:a=>({onRowPinningChange:q7("rowPinning",a)}),createRow:(a,b)=>{a.pin=(c,d,e)=>{let f=d?a.getLeafRows().map(a=>{let{id:b}=a;return b}):[],g=new Set([...e?a.getParentRows().map(a=>{let{id:b}=a;return b}):[],a.id,...f]);b.setRowPinning(a=>{var b,d,e,f,h,i;return"bottom"===c?{top:(null!=(e=null==a?void 0:a.top)?e:[]).filter(a=>!(null!=g&&g.has(a))),bottom:[...(null!=(f=null==a?void 0:a.bottom)?f:[]).filter(a=>!(null!=g&&g.has(a))),...Array.from(g)]}:"top"===c?{top:[...(null!=(h=null==a?void 0:a.top)?h:[]).filter(a=>!(null!=g&&g.has(a))),...Array.from(g)],bottom:(null!=(i=null==a?void 0:a.bottom)?i:[]).filter(a=>!(null!=g&&g.has(a)))}:{top:(null!=(b=null==a?void 0:a.top)?b:[]).filter(a=>!(null!=g&&g.has(a))),bottom:(null!=(d=null==a?void 0:a.bottom)?d:[]).filter(a=>!(null!=g&&g.has(a)))}})},a.getCanPin=()=>{var c;let{enableRowPinning:d,enablePinning:e}=b.options;return"function"==typeof d?d(a):null==(c=null!=d?d:e)||c},a.getIsPinned=()=>{let c=[a.id],{top:d,bottom:e}=b.getState().rowPinning,f=c.some(a=>null==d?void 0:d.includes(a)),g=c.some(a=>null==e?void 0:e.includes(a));return f?"top":!!g&&"bottom"},a.getPinnedIndex=()=>{var c,d;let e=a.getIsPinned();if(!e)return -1;let f=null==(c="top"===e?b.getTopRows():b.getBottomRows())?void 0:c.map(a=>{let{id:b}=a;return b});return null!=(d=null==f?void 0:f.indexOf(a.id))?d:-1}},createTable:a=>{a.setRowPinning=b=>null==a.options.onRowPinningChange?void 0:a.options.onRowPinningChange(b),a.resetRowPinning=b=>{var c,d;return a.setRowPinning(b?rz():null!=(c=null==(d=a.initialState)?void 0:d.rowPinning)?c:rz())},a.getIsSomeRowsPinned=b=>{var c,d,e;let f=a.getState().rowPinning;return b?!!(null==(c=f[b])?void 0:c.length):!!((null==(d=f.top)?void 0:d.length)||(null==(e=f.bottom)?void 0:e.length))},a._getPinnedRows=(b,c,d)=>{var e;return(null==(e=a.options.keepPinnedRows)||e?(null!=c?c:[]).map(b=>{let c=a.getRow(b,!0);return c.getIsAllParentsExpanded()?c:null}):(null!=c?c:[]).map(a=>b.find(b=>b.id===a))).filter(Boolean).map(a=>({...a,position:d}))},a.getTopRows=q9(()=>[a.getRowModel().rows,a.getState().rowPinning.top],(b,c)=>a._getPinnedRows(b,c,"top"),ra(a.options,"debugRows","getTopRows")),a.getBottomRows=q9(()=>[a.getRowModel().rows,a.getState().rowPinning.bottom],(b,c)=>a._getPinnedRows(b,c,"bottom"),ra(a.options,"debugRows","getBottomRows")),a.getCenterRows=q9(()=>[a.getRowModel().rows,a.getState().rowPinning.top,a.getState().rowPinning.bottom],(a,b,c)=>{let d=new Set([...null!=b?b:[],...null!=c?c:[]]);return a.filter(a=>!d.has(a.id))},ra(a.options,"debugRows","getCenterRows"))}},{getInitialState:a=>({rowSelection:{},...a}),getDefaultOptions:a=>({onRowSelectionChange:q7("rowSelection",a),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:a=>{a.setRowSelection=b=>null==a.options.onRowSelectionChange?void 0:a.options.onRowSelectionChange(b),a.resetRowSelection=b=>{var c;return a.setRowSelection(b?{}:null!=(c=a.initialState.rowSelection)?c:{})},a.toggleAllRowsSelected=b=>{a.setRowSelection(c=>{b=void 0!==b?b:!a.getIsAllRowsSelected();let d={...c},e=a.getPreGroupedRowModel().flatRows;return b?e.forEach(a=>{a.getCanSelect()&&(d[a.id]=!0)}):e.forEach(a=>{delete d[a.id]}),d})},a.toggleAllPageRowsSelected=b=>a.setRowSelection(c=>{let d=void 0!==b?b:!a.getIsAllPageRowsSelected(),e={...c};return a.getRowModel().rows.forEach(b=>{rA(e,b.id,d,!0,a)}),e}),a.getPreSelectedRowModel=()=>a.getCoreRowModel(),a.getSelectedRowModel=q9(()=>[a.getState().rowSelection,a.getCoreRowModel()],(b,c)=>Object.keys(b).length?rB(a,c):{rows:[],flatRows:[],rowsById:{}},ra(a.options,"debugTable","getSelectedRowModel")),a.getFilteredSelectedRowModel=q9(()=>[a.getState().rowSelection,a.getFilteredRowModel()],(b,c)=>Object.keys(b).length?rB(a,c):{rows:[],flatRows:[],rowsById:{}},ra(a.options,"debugTable","getFilteredSelectedRowModel")),a.getGroupedSelectedRowModel=q9(()=>[a.getState().rowSelection,a.getSortedRowModel()],(b,c)=>Object.keys(b).length?rB(a,c):{rows:[],flatRows:[],rowsById:{}},ra(a.options,"debugTable","getGroupedSelectedRowModel")),a.getIsAllRowsSelected=()=>{let b=a.getFilteredRowModel().flatRows,{rowSelection:c}=a.getState(),d=!!(b.length&&Object.keys(c).length);return d&&b.some(a=>a.getCanSelect()&&!c[a.id])&&(d=!1),d},a.getIsAllPageRowsSelected=()=>{let b=a.getPaginationRowModel().flatRows.filter(a=>a.getCanSelect()),{rowSelection:c}=a.getState(),d=!!b.length;return d&&b.some(a=>!c[a.id])&&(d=!1),d},a.getIsSomeRowsSelected=()=>{var b;let c=Object.keys(null!=(b=a.getState().rowSelection)?b:{}).length;return c>0&&c<a.getFilteredRowModel().flatRows.length},a.getIsSomePageRowsSelected=()=>{let b=a.getPaginationRowModel().flatRows;return!a.getIsAllPageRowsSelected()&&b.filter(a=>a.getCanSelect()).some(a=>a.getIsSelected()||a.getIsSomeSelected())},a.getToggleAllRowsSelectedHandler=()=>b=>{a.toggleAllRowsSelected(b.target.checked)},a.getToggleAllPageRowsSelectedHandler=()=>b=>{a.toggleAllPageRowsSelected(b.target.checked)}},createRow:(a,b)=>{a.toggleSelected=(c,d)=>{let e=a.getIsSelected();b.setRowSelection(f=>{var g;if(c=void 0!==c?c:!e,a.getCanSelect()&&e===c)return f;let h={...f};return rA(h,a.id,c,null==(g=null==d?void 0:d.selectChildren)||g,b),h})},a.getIsSelected=()=>{let{rowSelection:c}=b.getState();return rC(a,c)},a.getIsSomeSelected=()=>{let{rowSelection:c}=b.getState();return"some"===rD(a,c)},a.getIsAllSubRowsSelected=()=>{let{rowSelection:c}=b.getState();return"all"===rD(a,c)},a.getCanSelect=()=>{var c;return"function"==typeof b.options.enableRowSelection?b.options.enableRowSelection(a):null==(c=b.options.enableRowSelection)||c},a.getCanSelectSubRows=()=>{var c;return"function"==typeof b.options.enableSubRowSelection?b.options.enableSubRowSelection(a):null==(c=b.options.enableSubRowSelection)||c},a.getCanMultiSelect=()=>{var c;return"function"==typeof b.options.enableMultiRowSelection?b.options.enableMultiRowSelection(a):null==(c=b.options.enableMultiRowSelection)||c},a.getToggleSelectedHandler=()=>{let b=a.getCanSelect();return c=>{var d;b&&a.toggleSelected(null==(d=c.target)?void 0:d.checked)}}}},{getDefaultColumnDef:()=>rt,getInitialState:a=>({columnSizing:{},columnSizingInfo:ru(),...a}),getDefaultOptions:a=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:q7("columnSizing",a),onColumnSizingInfoChange:q7("columnSizingInfo",a)}),createColumn:(a,b)=>{a.getSize=()=>{var c,d,e;let f=b.getState().columnSizing[a.id];return Math.min(Math.max(null!=(c=a.columnDef.minSize)?c:rt.minSize,null!=(d=null!=f?f:a.columnDef.size)?d:rt.size),null!=(e=a.columnDef.maxSize)?e:rt.maxSize)},a.getStart=q9(a=>[a,rx(b,a),b.getState().columnSizing],(b,c)=>c.slice(0,a.getIndex(b)).reduce((a,b)=>a+b.getSize(),0),ra(b.options,"debugColumns","getStart")),a.getAfter=q9(a=>[a,rx(b,a),b.getState().columnSizing],(b,c)=>c.slice(a.getIndex(b)+1).reduce((a,b)=>a+b.getSize(),0),ra(b.options,"debugColumns","getAfter")),a.resetSize=()=>{b.setColumnSizing(b=>{let{[a.id]:c,...d}=b;return d})},a.getCanResize=()=>{var c,d;return(null==(c=a.columnDef.enableResizing)||c)&&(null==(d=b.options.enableColumnResizing)||d)},a.getIsResizing=()=>b.getState().columnSizingInfo.isResizingColumn===a.id},createHeader:(a,b)=>{a.getSize=()=>{let b=0,c=a=>{if(a.subHeaders.length)a.subHeaders.forEach(c);else{var d;b+=null!=(d=a.column.getSize())?d:0}};return c(a),b},a.getStart=()=>{if(a.index>0){let b=a.headerGroup.headers[a.index-1];return b.getStart()+b.getSize()}return 0},a.getResizeHandler=c=>{let d=b.getColumn(a.column.id),e=null==d?void 0:d.getCanResize();return f=>{if(!d||!e||(null==f.persist||f.persist(),rw(f)&&f.touches&&f.touches.length>1))return;let g=a.getSize(),h=a?a.getLeafHeaders().map(a=>[a.column.id,a.column.getSize()]):[[d.id,d.getSize()]],i=rw(f)?Math.round(f.touches[0].clientX):f.clientX,j={},k=(a,c)=>{"number"==typeof c&&(b.setColumnSizingInfo(a=>{var d,e;let f="rtl"===b.options.columnResizeDirection?-1:1,g=(c-(null!=(d=null==a?void 0:a.startOffset)?d:0))*f,h=Math.max(g/(null!=(e=null==a?void 0:a.startSize)?e:0),-.999999);return a.columnSizingStart.forEach(a=>{let[b,c]=a;j[b]=Math.round(100*Math.max(c+c*h,0))/100}),{...a,deltaOffset:g,deltaPercentage:h}}),("onChange"===b.options.columnResizeMode||"end"===a)&&b.setColumnSizing(a=>({...a,...j})))},l=a=>k("move",a),m=a=>{k("end",a),b.setColumnSizingInfo(a=>({...a,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},n=c||("undefined"!=typeof document?document:null),o={moveHandler:a=>l(a.clientX),upHandler:a=>{null==n||n.removeEventListener("mousemove",o.moveHandler),null==n||n.removeEventListener("mouseup",o.upHandler),m(a.clientX)}},p={moveHandler:a=>(a.cancelable&&(a.preventDefault(),a.stopPropagation()),l(a.touches[0].clientX),!1),upHandler:a=>{var b;null==n||n.removeEventListener("touchmove",p.moveHandler),null==n||n.removeEventListener("touchend",p.upHandler),a.cancelable&&(a.preventDefault(),a.stopPropagation()),m(null==(b=a.touches[0])?void 0:b.clientX)}},q=!!function(){if("boolean"==typeof rv)return rv;let a=!1;try{let b=()=>{};window.addEventListener("test",b,{get passive(){return a=!0,!1}}),window.removeEventListener("test",b)}catch(b){a=!1}return rv=a}()&&{passive:!1};rw(f)?(null==n||n.addEventListener("touchmove",p.moveHandler,q),null==n||n.addEventListener("touchend",p.upHandler,q)):(null==n||n.addEventListener("mousemove",o.moveHandler,q),null==n||n.addEventListener("mouseup",o.upHandler,q)),b.setColumnSizingInfo(a=>({...a,startOffset:i,startSize:g,deltaOffset:0,deltaPercentage:0,columnSizingStart:h,isResizingColumn:d.id}))}}},createTable:a=>{a.setColumnSizing=b=>null==a.options.onColumnSizingChange?void 0:a.options.onColumnSizingChange(b),a.setColumnSizingInfo=b=>null==a.options.onColumnSizingInfoChange?void 0:a.options.onColumnSizingInfoChange(b),a.resetColumnSizing=b=>{var c;a.setColumnSizing(b?{}:null!=(c=a.initialState.columnSizing)?c:{})},a.resetHeaderSizeInfo=b=>{var c;a.setColumnSizingInfo(b?ru():null!=(c=a.initialState.columnSizingInfo)?c:ru())},a.getTotalSize=()=>{var b,c;return null!=(b=null==(c=a.getHeaderGroups()[0])?void 0:c.headers.reduce((a,b)=>a+b.getSize(),0))?b:0},a.getLeftTotalSize=()=>{var b,c;return null!=(b=null==(c=a.getLeftHeaderGroups()[0])?void 0:c.headers.reduce((a,b)=>a+b.getSize(),0))?b:0},a.getCenterTotalSize=()=>{var b,c;return null!=(b=null==(c=a.getCenterHeaderGroups()[0])?void 0:c.headers.reduce((a,b)=>a+b.getSize(),0))?b:0},a.getRightTotalSize=()=>{var b,c;return null!=(b=null==(c=a.getRightHeaderGroups()[0])?void 0:c.headers.reduce((a,b)=>a+b.getSize(),0))?b:0}}}];function rK(a,b){var c,d,e;return a?"function"==typeof(d=c=a)&&(()=>{let a=Object.getPrototypeOf(d);return a.prototype&&a.prototype.isReactComponent})()||"function"==typeof c||"object"==typeof(e=c)&&"symbol"==typeof e.$$typeof&&["react.memo","react.forward_ref"].includes(e.$$typeof.description)?f.createElement(a,b):a:null}let rL=p("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),rM=p("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]),rN={accessor:(a,b)=>"function"==typeof a?{...b,accessorFn:a}:{...b,accessorKey:a}},rO=()=>{let{filteredJournals:a,selectedJournals:b,addSelectedJournal:c,removeSelectedJournal:d,filters:h}=k(),[i,j]=g().useState([{id:"impact_proxy",desc:!0}]),l=function(a){let b={state:{},onStateChange:()=>{},renderFallbackValue:null,...a},[c]=f.useState(()=>({current:function(a){var b,c;let d=[...rJ,...null!=(b=a._features)?b:[]],e={_features:d},f=e._features.reduce((a,b)=>Object.assign(a,null==b.getDefaultOptions?void 0:b.getDefaultOptions(e)),{}),g={...null!=(c=a.initialState)?c:{}};e._features.forEach(a=>{var b;g=null!=(b=null==a.getInitialState?void 0:a.getInitialState(g))?b:g});let h=[],i=!1,j={_features:d,options:{...f,...a},initialState:g,_queue:a=>{h.push(a),i||(i=!0,Promise.resolve().then(()=>{for(;h.length;)h.shift()();i=!1}).catch(a=>setTimeout(()=>{throw a})))},reset:()=>{e.setState(e.initialState)},setOptions:a=>{var b;b=q6(a,e.options),e.options=e.options.mergeOptions?e.options.mergeOptions(f,b):{...f,...b}},getState:()=>e.options.state,setState:a=>{null==e.options.onStateChange||e.options.onStateChange(a)},_getRowId:(a,b,c)=>{var d;return null!=(d=null==e.options.getRowId?void 0:e.options.getRowId(a,b,c))?d:`${c?[c.id,b].join("."):b}`},getCoreRowModel:()=>(e._getCoreRowModel||(e._getCoreRowModel=e.options.getCoreRowModel(e)),e._getCoreRowModel()),getRowModel:()=>e.getPaginationRowModel(),getRow:(a,b)=>{let c=(b?e.getPrePaginationRowModel():e.getRowModel()).rowsById[a];if(!c&&!(c=e.getCoreRowModel().rowsById[a]))throw Error();return c},_getDefaultColumnDef:q9(()=>[e.options.defaultColumn],a=>{var b;return a=null!=(b=a)?b:{},{header:a=>{let b=a.header.column.columnDef;return b.accessorKey?b.accessorKey:b.accessorFn?b.id:null},cell:a=>{var b,c;return null!=(b=null==(c=a.renderValue())||null==c.toString?void 0:c.toString())?b:null},...e._features.reduce((a,b)=>Object.assign(a,null==b.getDefaultColumnDef?void 0:b.getDefaultColumnDef()),{}),...a}},ra(a,"debugColumns","_getDefaultColumnDef")),_getColumnDefs:()=>e.options.columns,getAllColumns:q9(()=>[e._getColumnDefs()],a=>{let b=function(a,c,d){return void 0===d&&(d=0),a.map(a=>{let f=function(a,b,c,d){var e,f;let g,h={...a._getDefaultColumnDef(),...b},i=h.accessorKey,j=null!=(e=null!=(f=h.id)?f:i?"function"==typeof String.prototype.replaceAll?i.replaceAll(".","_"):i.replace(/\./g,"_"):void 0)?e:"string"==typeof h.header?h.header:void 0;if(h.accessorFn?g=h.accessorFn:i&&(g=i.includes(".")?a=>{let b=a;for(let a of i.split(".")){var c;b=null==(c=b)?void 0:c[a]}return b}:a=>a[h.accessorKey]),!j)throw Error();let k={id:`${String(j)}`,accessorFn:g,parent:d,depth:c,columnDef:h,columns:[],getFlatColumns:q9(()=>[!0],()=>{var a;return[k,...null==(a=k.columns)?void 0:a.flatMap(a=>a.getFlatColumns())]},ra(a.options,"debugColumns","column.getFlatColumns")),getLeafColumns:q9(()=>[a._getOrderColumnsFn()],a=>{var b;return null!=(b=k.columns)&&b.length?a(k.columns.flatMap(a=>a.getLeafColumns())):[k]},ra(a.options,"debugColumns","column.getLeafColumns"))};for(let b of a._features)null==b.createColumn||b.createColumn(k,a);return k}(e,a,d,c);return f.columns=a.columns?b(a.columns,f,d+1):[],f})};return b(a)},ra(a,"debugColumns","getAllColumns")),getAllFlatColumns:q9(()=>[e.getAllColumns()],a=>a.flatMap(a=>a.getFlatColumns()),ra(a,"debugColumns","getAllFlatColumns")),_getAllFlatColumnsById:q9(()=>[e.getAllFlatColumns()],a=>a.reduce((a,b)=>(a[b.id]=b,a),{}),ra(a,"debugColumns","getAllFlatColumnsById")),getAllLeafColumns:q9(()=>[e.getAllColumns(),e._getOrderColumnsFn()],(a,b)=>b(a.flatMap(a=>a.getLeafColumns())),ra(a,"debugColumns","getAllLeafColumns")),getColumn:a=>e._getAllFlatColumnsById()[a]};Object.assign(e,j);for(let a=0;a<e._features.length;a++){let b=e._features[a];null==b||null==b.createTable||b.createTable(e)}return e}(b)})),[d,e]=f.useState(()=>c.current.initialState);return c.current.setOptions(b=>({...b,...a,state:{...d,...a.state},onStateChange:b=>{e(b),null==a.onStateChange||a.onStateChange(b)}})),c.current}({data:a,columns:(0,f.useMemo)(()=>[rN.accessor("title",{header:"Journal Title",cell:a=>(0,e.jsxs)("div",{className:"flex items-center gap-2",children:[(0,e.jsx)("span",{className:"font-medium text-gray-900 truncate max-w-xs",children:((a,b)=>{if(!b)return a;let c=RegExp(`(${b})`,"gi");return a.split(c).map((a,b)=>c.test(a)?(0,e.jsx)("mark",{className:"bg-yellow-200 px-1 rounded",children:a},b):a)})(a.getValue(),h.searchTerm)}),a.row.original.journal_url&&(0,e.jsx)("a",{href:a.row.original.journal_url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800",children:(0,e.jsx)(rL,{className:"w-4 h-4"})})]}),size:300}),rN.accessor("subject",{header:"Subject",cell:a=>(0,e.jsx)("span",{className:"text-sm text-gray-700 truncate",children:a.getValue()}),size:150}),rN.accessor("impact_proxy",{header:({column:a})=>(0,e.jsxs)("button",{className:"flex items-center gap-1 hover:text-blue-600",onClick:()=>a.toggleSorting("asc"===a.getIsSorted()),children:["Impact Proxy",(0,e.jsx)(rM,{className:"w-4 h-4"})]}),cell:a=>(0,e.jsx)("span",{className:"font-mono text-sm",children:a.getValue().toFixed(2)}),size:100}),rN.accessor("apc_usd",{header:({column:a})=>(0,e.jsxs)("button",{className:"flex items-center gap-1 hover:text-blue-600",onClick:()=>a.toggleSorting("asc"===a.getIsSorted()),children:["APC (USD)",(0,e.jsx)(rM,{className:"w-4 h-4"})]}),cell:a=>{let b=a.getValue();return(0,e.jsx)("span",{className:"font-mono text-sm",children:null!=b?`$${b.toLocaleString()}`:"N/A"})},size:120}),rN.accessor("cost_efficiency",{header:({column:a})=>(0,e.jsxs)("button",{className:"flex items-center gap-1 hover:text-blue-600",onClick:()=>a.toggleSorting("asc"===a.getIsSorted()),children:["Cost Efficiency",(0,e.jsx)(rM,{className:"w-4 h-4"})]}),cell:a=>{let b=a.getValue();return(0,e.jsx)("span",{className:"font-mono text-sm",children:null==b?"N/A":b===1/0?"∞":b.toFixed(4)})},size:130}),rN.accessor("oa_type",{header:"OA Type",cell:a=>{let b=a.getValue(),c={"Fully OA":"bg-green-100 text-green-800",Hybrid:"bg-yellow-100 text-yellow-800",Diamond:"bg-purple-100 text-purple-800",Subscription:"bg-red-100 text-red-800",Unknown:"bg-gray-100 text-gray-800"}[b]||"bg-gray-100 text-gray-800";return(0,e.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${c}`,children:b})},size:120}),rN.accessor("publisher",{header:"Publisher",cell:a=>(0,e.jsx)("span",{className:"text-sm text-gray-700 truncate max-w-xs",children:a.getValue()}),size:200})],[]),state:{sorting:i},onSortingChange:j,getCoreRowModel:a=>q9(()=>[a.options.data],b=>{let c={rows:[],flatRows:[],rowsById:{}},d=function(b,e,f){void 0===e&&(e=0);let g=[];for(let i=0;i<b.length;i++){let j=re(a,a._getRowId(b[i],i,f),b[i],i,e,void 0,null==f?void 0:f.id);if(c.flatRows.push(j),c.rowsById[j.id]=j,g.push(j),a.options.getSubRows){var h;j.originalSubRows=a.options.getSubRows(b[i],i),null!=(h=j.originalSubRows)&&h.length&&(j.subRows=d(j.originalSubRows,e+1,j))}}return g};return c.rows=d(b),c},ra(a.options,"debugTable","getRowModel",()=>a._autoResetPageIndex())),getSortedRowModel:a=>q9(()=>[a.getState().sorting,a.getPreSortedRowModel()],(b,c)=>{if(!c.rows.length||!(null!=b&&b.length))return c;let d=a.getState().sorting,e=[],f=d.filter(b=>{var c;return null==(c=a.getColumn(b.id))?void 0:c.getCanSort()}),g={};f.forEach(b=>{let c=a.getColumn(b.id);c&&(g[b.id]={sortUndefined:c.columnDef.sortUndefined,invertSorting:c.columnDef.invertSorting,sortingFn:c.getSortingFn()})});let h=a=>{let b=a.map(a=>({...a}));return b.sort((a,b)=>{for(let d=0;d<f.length;d+=1){var c;let e=f[d],h=g[e.id],i=h.sortUndefined,j=null!=(c=null==e?void 0:e.desc)&&c,k=0;if(i){let c=a.getValue(e.id),d=b.getValue(e.id),f=void 0===c,g=void 0===d;if(f||g){if("first"===i)return f?-1:1;if("last"===i)return f?1:-1;k=f&&g?0:f?i:-i}}if(0===k&&(k=h.sortingFn(a,b,e.id)),0!==k)return j&&(k*=-1),h.invertSorting&&(k*=-1),k}return a.index-b.index}),b.forEach(a=>{var b;e.push(a),null!=(b=a.subRows)&&b.length&&(a.subRows=h(a.subRows))}),b};return{rows:h(c.rows),flatRows:e,rowsById:c.rowsById}},ra(a.options,"debugTable","getSortedRowModel",()=>a._autoResetPageIndex()))});return(0,e.jsxs)("div",{className:"h-full flex flex-col",children:[(0,e.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,e.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Journal Data"}),(0,e.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,e.jsxs)("span",{children:[a.length," journals"]}),b.size>0&&(0,e.jsxs)("span",{className:"text-blue-600 font-medium",children:[b.size," selected"]})]})]}),(0,e.jsx)("div",{className:"flex-1 overflow-auto border border-gray-200 rounded-lg",children:(0,e.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,e.jsx)("thead",{className:"bg-gray-50 sticky top-0",children:l.getHeaderGroups().map(a=>(0,e.jsx)("tr",{children:a.headers.map(a=>(0,e.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",style:{width:a.getSize()},children:a.isPlaceholder?null:rK(a.column.columnDef.header,a.getContext())},a.id))},a.id))}),(0,e.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:l.getRowModel().rows.map(a=>{let f=b.has(a.original.issn_l);return(0,e.jsx)("tr",{onClick:()=>(a=>{b.has(a.issn_l)?d(a.issn_l):c(a.issn_l)})(a.original),className:`cursor-pointer hover:bg-gray-50 ${f?"bg-blue-50 border-l-4 border-blue-500":""}`,children:a.getVisibleCells().map(a=>(0,e.jsx)("td",{className:"px-4 py-3 whitespace-nowrap text-sm",children:rK(a.column.columnDef.cell,a.getContext())},a.id))},a.id)})})]})}),(0,e.jsx)("div",{className:"mt-4 text-xs text-gray-500",children:"Click rows to select journals. Selected journals will be highlighted in the scatter plot above."})]})},rP=()=>{let[a,b]=(0,f.useState)(!1),[c,d]=(0,f.useState)(""),[g,h]=(0,f.useState)(!1);return(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,e.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,e.jsxs)("div",{className:"flex items-center justify-between",children:[(0,e.jsxs)("div",{children:[(0,e.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Journal Watch"}),(0,e.jsx)("p",{className:"text-gray-600 mt-2",children:"Find the right journal for your research based on SJR impact, cost, and openness"})]}),(0,e.jsx)("div",{className:"flex items-center space-x-4",children:(0,e.jsx)("button",{onClick:()=>b(!0),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium",children:"Subscribe for Updates"})})]})})}),a&&(0,e.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,e.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,e.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Subscribe for Updates"}),g?(0,e.jsxs)("div",{className:"text-center",children:[(0,e.jsx)("div",{className:"text-green-600 text-2xl mb-2",children:"✓"}),(0,e.jsx)("p",{className:"text-gray-600",children:"Thank you for subscribing!"})]}):(0,e.jsxs)("form",{onSubmit:a=>{a.preventDefault(),c&&(console.log("Subscribing email:",c),h(!0),setTimeout(()=>{b(!1),h(!1),d("")},2e3))},children:[(0,e.jsx)("p",{className:"text-gray-600 mb-4",children:"Get notified when we add new data sources or features."}),(0,e.jsx)("input",{type:"email",value:c,onChange:a=>d(a.target.value),placeholder:"Enter your email",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mb-4",required:!0}),(0,e.jsxs)("div",{className:"flex gap-2",children:[(0,e.jsx)("button",{type:"submit",className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium",children:"Subscribe"}),(0,e.jsx)("button",{type:"button",onClick:()=>b(!1),className:"flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md font-medium",children:"Cancel"})]})]})]})})]})},rQ=()=>(0,e.jsx)("footer",{className:"bg-white border-t mt-8",children:(0,e.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,e.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,e.jsxs)("div",{className:"text-sm text-gray-600 mb-4 md:mb-0",children:[(0,e.jsx)("p",{children:"Data sources: SciMago Journal Rankings, MDPI, OpenAPC, DOAJ"}),(0,e.jsx)("p",{className:"mt-1",children:"Last updated: July 19, 2025"})]}),(0,e.jsx)("div",{className:"text-sm text-gray-600",children:"\xa9 2025 Journal Watch"})]})})}),rR=p("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]),rS=p("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),rT=p("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),rU=()=>{let{filteredJournals:a,addSelectedJournal:b}=k(),c=(0,f.useMemo)(()=>{if(0===a.length)return{highImpact:[],bestValue:[],costEfficiency:[]};let b=[...a].sort((a,b)=>b.impact_proxy-a.impact_proxy).slice(0,5);return{highImpact:b,bestValue:[...a.filter(a=>null!=a.apc_usd&&a.apc_usd>0&&a.impact_proxy>.5)].sort((a,b)=>{let c=a.impact_proxy/a.apc_usd;return b.impact_proxy/b.apc_usd-c}).slice(0,5),costEfficiency:[...a.filter(a=>null!=a.cost_efficiency&&a.cost_efficiency>0)].sort((a,b)=>{let c=999999===a.cost_efficiency?1/0:a.cost_efficiency;return(999999===b.cost_efficiency?1/0:b.cost_efficiency)-c}).slice(0,5)}},[a]),d=({journal:a,reason:c,icon:d,iconColor:f})=>(0,e.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,e.jsxs)("div",{className:"flex items-start gap-3",children:[(0,e.jsx)(d,{className:`w-5 h-5 mt-1 ${f}`}),(0,e.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,e.jsx)("h4",{className:"font-medium text-gray-900 truncate",children:a.title}),c&&(0,e.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:c}),(0,e.jsxs)("div",{className:"flex items-center gap-4 mt-2 text-xs text-gray-500",children:[(0,e.jsxs)("span",{children:["Impact: ",a.impact_proxy.toFixed(2)]}),(0,e.jsxs)("span",{children:["APC: ",null!=a.apc_usd?`$${a.apc_usd.toLocaleString()}`:"N/A"]}),(0,e.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${"Fully OA"===a.oa_type?"bg-green-100 text-green-800":"Hybrid"===a.oa_type?"bg-yellow-100 text-yellow-800":"Diamond"===a.oa_type?"bg-purple-100 text-purple-800":"bg-gray-100 text-gray-800"}`,children:a.oa_type})]}),(0,e.jsx)("button",{onClick:()=>b(a.issn_l),className:"mt-2 text-xs text-blue-600 hover:text-blue-800 font-medium",children:"Add to selection"})]})]})});return 0===a.length?null:(0,e.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 mt-8",children:[(0,e.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Recommendations Based on Your Filters"}),(0,e.jsxs)("p",{className:"text-sm text-gray-600 mb-4",children:[(0,e.jsx)("strong",{children:"Note:"}),' "N/A APC" means pricing information is unavailable - this is NOT the same as free journals.']}),(0,e.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[c.highImpact.length>0&&(0,e.jsxs)("div",{children:[(0,e.jsxs)("h4",{className:"font-medium text-gray-900 mb-3 flex items-center gap-2",children:[(0,e.jsx)(rR,{className:"w-4 h-4 text-green-600"}),"High Impact"]}),(0,e.jsx)("div",{className:"space-y-3",children:c.highImpact.map(a=>(0,e.jsx)(d,{journal:a,reason:"",icon:rR,iconColor:"text-green-600"},a.issn_l))})]}),c.bestValue.length>0&&(0,e.jsxs)("div",{children:[(0,e.jsxs)("h4",{className:"font-medium text-gray-900 mb-3 flex items-center gap-2",children:[(0,e.jsx)(rS,{className:"w-4 h-4 text-blue-600"}),"Best Value"]}),(0,e.jsx)("div",{className:"space-y-3",children:c.bestValue.map(a=>(0,e.jsx)(d,{journal:a,reason:"",icon:rS,iconColor:"text-blue-600"},a.issn_l))})]}),c.costEfficiency.length>0&&(0,e.jsxs)("div",{children:[(0,e.jsxs)("h4",{className:"font-medium text-gray-900 mb-3 flex items-center gap-2",children:[(0,e.jsx)(rT,{className:"w-4 h-4 text-purple-600"}),"Highest cost efficiency"]}),(0,e.jsx)("div",{className:"space-y-3",children:c.costEfficiency.map(a=>(0,e.jsx)(d,{journal:a,reason:"",icon:rT,iconColor:"text-purple-600"},a.issn_l))})]})]})]})},rV=()=>(0,e.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,e.jsx)("div",{className:"bg-white shadow-sm border-b",children:(0,e.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,e.jsxs)("div",{className:"flex items-center justify-between",children:[(0,e.jsxs)("div",{children:[(0,e.jsx)("div",{className:"h-8 bg-gray-200 rounded w-80 mb-2 animate-pulse"}),(0,e.jsx)("div",{className:"h-4 bg-gray-200 rounded w-96 animate-pulse"})]}),(0,e.jsxs)("div",{className:"flex space-x-4",children:[(0,e.jsx)("div",{className:"h-6 bg-gray-200 rounded w-16 animate-pulse"}),(0,e.jsx)("div",{className:"h-6 bg-gray-200 rounded w-24 animate-pulse"})]})]})})}),(0,e.jsxs)("main",{className:"container mx-auto px-4 py-6",children:[(0,e.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[void 0,void 0,void 0,void 0].map((a,b)=>(0,e.jsx)("div",{className:"bg-white rounded-lg p-4 border border-gray-200",children:(0,e.jsxs)("div",{className:"flex items-center justify-between",children:[(0,e.jsxs)("div",{children:[(0,e.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-2 animate-pulse"}),(0,e.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16 animate-pulse"})]}),(0,e.jsx)("div",{className:"h-8 w-8 bg-gray-200 rounded animate-pulse"})]})},b))}),(0,e.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 mb-6",children:[(0,e.jsx)("div",{className:"h-6 bg-gray-200 rounded w-64 mb-4 animate-pulse"}),(0,e.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[void 0,void 0,void 0].map((a,b)=>(0,e.jsxs)("div",{children:[(0,e.jsx)("div",{className:"h-5 bg-gray-200 rounded w-32 mb-3 animate-pulse"}),(0,e.jsx)("div",{className:"space-y-3",children:[void 0,void 0].map((a,b)=>(0,e.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,e.jsxs)("div",{className:"flex items-start gap-3",children:[(0,e.jsx)("div",{className:"h-5 w-5 bg-gray-200 rounded animate-pulse"}),(0,e.jsxs)("div",{className:"flex-1",children:[(0,e.jsx)("div",{className:"h-4 bg-gray-200 rounded w-full mb-2 animate-pulse"}),(0,e.jsx)("div",{className:"h-3 bg-gray-200 rounded w-3/4 mb-2 animate-pulse"}),(0,e.jsxs)("div",{className:"flex gap-2",children:[(0,e.jsx)("div",{className:"h-3 bg-gray-200 rounded w-16 animate-pulse"}),(0,e.jsx)("div",{className:"h-3 bg-gray-200 rounded w-16 animate-pulse"}),(0,e.jsx)("div",{className:"h-3 bg-gray-200 rounded w-16 animate-pulse"})]})]})]})},b))})]},b))})]}),(0,e.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-400px)]",children:[(0,e.jsx)("div",{className:"lg:col-span-1",children:(0,e.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-4 h-full",children:[(0,e.jsx)("div",{className:"h-6 bg-gray-200 rounded w-24 mb-4 animate-pulse"}),(0,e.jsxs)("div",{className:"space-y-4",children:[(0,e.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"}),(0,e.jsx)("div",{className:"space-y-2",children:[void 0,void 0,void 0,void 0,void 0].map((a,b)=>(0,e.jsxs)("div",{className:"flex items-center gap-2",children:[(0,e.jsx)("div",{className:"h-4 w-4 bg-gray-200 rounded animate-pulse"}),(0,e.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 animate-pulse"})]},b))})]})]})}),(0,e.jsxs)("div",{className:"lg:col-span-3 flex flex-col gap-6",children:[(0,e.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-4 h-1/2",children:[(0,e.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 mb-4 animate-pulse"}),(0,e.jsx)("div",{className:"h-full bg-gray-100 rounded animate-pulse"})]}),(0,e.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-4 h-1/2",children:[(0,e.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,e.jsx)("div",{className:"h-6 bg-gray-200 rounded w-32 animate-pulse"}),(0,e.jsx)("div",{className:"h-8 bg-gray-200 rounded w-40 animate-pulse"})]}),(0,e.jsx)("div",{className:"space-y-2",children:[...Array(8)].map((a,b)=>(0,e.jsx)("div",{className:"h-12 bg-gray-100 rounded animate-pulse"},b))})]})]})]})]}),(0,e.jsx)("div",{className:"bg-white border-t mt-8",children:(0,e.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,e.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,e.jsxs)("div",{className:"mb-4 md:mb-0",children:[(0,e.jsx)("div",{className:"h-4 bg-gray-200 rounded w-80 mb-1 animate-pulse"}),(0,e.jsx)("div",{className:"h-4 bg-gray-200 rounded w-40 animate-pulse"})]}),(0,e.jsx)("div",{className:"flex space-x-6",children:[void 0,void 0,void 0].map((a,b)=>(0,e.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 animate-pulse"},b))})]})})})]}),rW=p("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),rX=p("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),rY=p("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),rZ=p("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),r$=p("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),r_=()=>{let{filteredJournals:a,selectedJournals:b,removeSelectedJournal:c,clearSelectedJournals:d,setFilters:h,filters:i}=k(),{selected:j,deselected:l}=(0,f.useMemo)(()=>({selected:a.filter(a=>b.has(a.issn_l)),deselected:a.filter(a=>!b.has(a.issn_l))}),[a,b]),[m,n]=g().useState(!1);return 0===a.length?null:(0,e.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-4",children:[(0,e.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,e.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Journal Selection Status"}),(0,e.jsx)("div",{className:"flex gap-2",children:j.length>0&&(0,e.jsxs)(e.Fragment,{children:[(0,e.jsxs)("button",{onClick:()=>{if(0===j.length)return;let b=j.map(a=>a.impact_proxy),c=Math.min(...b),d=Math.max(...b),e=j.map(a=>a.apc_usd).filter(a=>null!=a),f=e.length>0?Math.min(...e):0,g=e.length>0?Math.max(...e):15e3,k=new Set(j.map(a=>a.oa_type).filter(Boolean)),l=(b,c,d)=>a.filter(a=>!(a.impact_proxy<b[0])&&!(a.impact_proxy>b[1])&&(null==a.apc_usd||!(a.apc_usd<c[0])&&!(a.apc_usd>c[1]))&&(!(d.size>0)||!!d.has(a.oa_type))).length,m=0;for(;m<10&&!(l([c,d],[f,g],k)>=10);){let a=d-c,b=g-f;c=Math.max(0,c-.2*a),d=Math.min(150,d+.2*a),f=Math.max(0,f-.2*b),g=Math.min(15e3,g+.2*b),m>3&&k.size>0&&(k=new Set),m++}h({...i,impactRange:[Math.floor(c),Math.ceil(d)],apcRange:[Math.floor(f),Math.ceil(g)],oaTypes:k})},className:"flex items-center gap-1 px-3 py-1 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors",title:"Create filter based on selected journals",children:[(0,e.jsx)(rW,{className:"w-4 h-4"}),"Filter Similar"]}),(0,e.jsxs)("button",{onClick:d,className:"flex items-center gap-1 px-3 py-1 text-sm text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors",children:[(0,e.jsx)(rX,{className:"w-4 h-4"}),"Clear All"]})]})})]}),(0,e.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,e.jsx)("div",{className:"bg-green-50 rounded-lg p-3",children:(0,e.jsxs)("div",{className:"flex items-center gap-2",children:[(0,e.jsx)(rY,{className:"w-5 h-5 text-green-600"}),(0,e.jsxs)("div",{children:[(0,e.jsx)("p",{className:"text-sm font-medium text-green-800",children:"Selected"}),(0,e.jsx)("p",{className:"text-lg font-bold text-green-900",children:j.length})]})]})}),(0,e.jsx)("div",{className:"bg-gray-50 rounded-lg p-3",children:(0,e.jsxs)("div",{className:"flex items-center gap-2",children:[(0,e.jsx)(rZ,{className:"w-5 h-5 text-gray-600"}),(0,e.jsxs)("div",{children:[(0,e.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Available"}),(0,e.jsx)("p",{className:"text-lg font-bold text-gray-900",children:l.length})]})]})})]}),j.length>0&&(0,e.jsxs)("div",{className:"mb-4",children:[(0,e.jsxs)("h4",{className:"text-md font-medium text-gray-900 mb-2 flex items-center gap-2",children:[(0,e.jsx)(rY,{className:"w-4 h-4 text-green-600"}),"Selected Journals (",j.length,")"]}),(0,e.jsx)("div",{className:"max-h-48 overflow-y-auto space-y-2",children:j.sort((a,b)=>b.impact_proxy-a.impact_proxy).map(a=>(0,e.jsxs)("div",{className:"flex items-center justify-between p-2 bg-green-50 rounded-md border border-green-200",children:[(0,e.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,e.jsx)("p",{className:"text-sm font-medium text-green-900 truncate",children:a.title}),(0,e.jsxs)("p",{className:"text-xs text-green-700",children:["Impact: ",a.impact_proxy.toFixed(2)," | APC: $",a.apc_usd?.toLocaleString()||"N/A"]})]}),(0,e.jsx)("button",{onClick:()=>c(a.issn_l),className:"ml-2 p-1 text-green-600 hover:text-red-600 hover:bg-red-50 rounded transition-colors",title:"Remove from selection",children:(0,e.jsx)(rZ,{className:"w-4 h-4"})})]},a.issn_l))})]}),(0,e.jsxs)("div",{className:"border-t pt-4",children:[(0,e.jsxs)("button",{onClick:()=>n(!m),className:"flex items-center gap-2 text-sm text-gray-600 hover:text-gray-800 mb-2",children:[m?(0,e.jsx)(r$,{className:"w-4 h-4"}):(0,e.jsx)(rW,{className:"w-4 h-4"}),m?"Hide":"Show"," Available Journals (",l.length,")"]}),m&&(0,e.jsxs)("div",{className:"max-h-48 overflow-y-auto space-y-1",children:[l.sort((a,b)=>b.impact_proxy-a.impact_proxy).slice(0,50).map(a=>(0,e.jsxs)("div",{className:"p-2 bg-gray-50 rounded-md border border-gray-200",children:[(0,e.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:a.title}),(0,e.jsxs)("p",{className:"text-xs text-gray-600",children:["Impact: ",a.impact_proxy.toFixed(2)," | APC: $",a.apc_usd?.toLocaleString()||"N/A"]})]},a.issn_l)),l.length>50&&(0,e.jsxs)("p",{className:"text-xs text-gray-500 text-center py-2",children:["... and ",l.length-50," more journals"]})]})]})]})};function r0(){let{setJournals:a,setLoading:b,setError:c,isLoading:d,error:f}=k();return d?(0,e.jsx)(rV,{}):f?(0,e.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,e.jsxs)("div",{className:"text-center text-red-600",children:[(0,e.jsx)("p",{className:"text-xl font-semibold mb-2",children:"Error loading data"}),(0,e.jsx)("p",{children:f})]})}):(0,e.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,e.jsx)(rP,{}),(0,e.jsxs)("main",{className:"container mx-auto px-4 py-6",children:[(0,e.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,e.jsx)("div",{className:"lg:col-span-1 order-2 lg:order-1",children:(0,e.jsxs)("div",{className:"lg:sticky lg:top-6 space-y-4",children:[(0,e.jsx)(s,{}),(0,e.jsx)(r_,{})]})}),(0,e.jsxs)("div",{className:"lg:col-span-3 order-1 lg:order-2 flex flex-col gap-6",children:[(0,e.jsx)("div",{className:"bg-white rounded-lg shadow-sm border p-4 h-[400px] lg:h-[500px]",children:(0,e.jsx)(q5,{})}),(0,e.jsx)("div",{className:"bg-white rounded-lg shadow-sm border p-4 h-[600px] lg:h-[700px]",children:(0,e.jsx)(rO,{})})]})]}),(0,e.jsx)(rU,{})]}),(0,e.jsx)(rQ,{})]})}},7841:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(6431),e=c(8150),f=c(9243),g=c(3574);b.has=function(a,b){let c;if(0===(c=Array.isArray(b)?b:"string"==typeof b&&d.isDeepKey(b)&&a?.[b]==null?g.toPath(b):[b]).length)return!1;let h=a;for(let a=0;a<c.length;a++){let b=c[a];if((null==h||!Object.hasOwn(h,b))&&!((Array.isArray(h)||f.isArguments(h))&&e.isIndex(b)&&b<h.length))return!1;h=h[b]}return!0}},8130:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isLength=function(a){return Number.isSafeInteger(a)&&a>=0}},8150:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let c=/^(?:0|[1-9]\d*)$/;b.isIndex=function(a,b=Number.MAX_SAFE_INTEGER){switch(typeof a){case"number":return Number.isInteger(a)&&a>=0&&a<b;case"symbol":return!1;case"string":return c.test(a)}}},8354:a=>{"use strict";a.exports=require("util")},8382:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.flatten=function(a,b=1){let c=[],d=Math.floor(b),e=(a,b)=>{for(let f=0;f<a.length;f++){let g=a[f];Array.isArray(g)&&b<d?e(g,b+1):c.push(g)}};return e(a,0),c}},8531:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,6133,23)),Promise.resolve().then(c.t.bind(c,6444,23)),Promise.resolve().then(c.t.bind(c,6042,23)),Promise.resolve().then(c.t.bind(c,9477,23)),Promise.resolve().then(c.t.bind(c,9345,23)),Promise.resolve().then(c.t.bind(c,2089,23)),Promise.resolve().then(c.t.bind(c,6577,23)),Promise.resolve().then(c.t.bind(c,1307,23)),Promise.resolve().then(c.t.bind(c,4817,23))},8730:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(8130);b.isArrayLike=function(a){return null!=a&&"function"!=typeof a&&d.isLength(a.length)}},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9138:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(9862);b.cloneDeep=function(a){return d.cloneDeepWith(a)}},9243:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(1428);b.isArguments=function(a){return null!==a&&"object"==typeof a&&"[object Arguments]"===d.getTag(a)}},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9404:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(175),e=c(1653),f=c(1428),g=c(7469),h=c(1706);b.isEqualWith=function(a,b,c){return function a(b,c,i,j,k,l,m){let n=m(b,c,i,j,k,l);if(void 0!==n)return n;if(typeof b==typeof c)switch(typeof b){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return b===c;case"number":return b===c||Object.is(b,c)}return function b(c,i,j,k){if(Object.is(c,i))return!0;let l=f.getTag(c),m=f.getTag(i);if(l===g.argumentsTag&&(l=g.objectTag),m===g.argumentsTag&&(m=g.objectTag),l!==m)return!1;switch(l){case g.stringTag:return c.toString()===i.toString();case g.numberTag:{let a=c.valueOf(),b=i.valueOf();return h.eq(a,b)}case g.booleanTag:case g.dateTag:case g.symbolTag:return Object.is(c.valueOf(),i.valueOf());case g.regexpTag:return c.source===i.source&&c.flags===i.flags;case g.functionTag:return c===i}let n=(j=j??new Map).get(c),o=j.get(i);if(null!=n&&null!=o)return n===i;j.set(c,i),j.set(i,c);try{switch(l){case g.mapTag:if(c.size!==i.size)return!1;for(let[b,d]of c.entries())if(!i.has(b)||!a(d,i.get(b),b,c,i,j,k))return!1;return!0;case g.setTag:{if(c.size!==i.size)return!1;let b=Array.from(c.values()),d=Array.from(i.values());for(let e=0;e<b.length;e++){let f=b[e],g=d.findIndex(b=>a(f,b,void 0,c,i,j,k));if(-1===g)return!1;d.splice(g,1)}return!0}case g.arrayTag:case g.uint8ArrayTag:case g.uint8ClampedArrayTag:case g.uint16ArrayTag:case g.uint32ArrayTag:case g.bigUint64ArrayTag:case g.int8ArrayTag:case g.int16ArrayTag:case g.int32ArrayTag:case g.bigInt64ArrayTag:case g.float32ArrayTag:case g.float64ArrayTag:if("undefined"!=typeof Buffer&&Buffer.isBuffer(c)!==Buffer.isBuffer(i)||c.length!==i.length)return!1;for(let b=0;b<c.length;b++)if(!a(c[b],i[b],b,c,i,j,k))return!1;return!0;case g.arrayBufferTag:if(c.byteLength!==i.byteLength)return!1;return b(new Uint8Array(c),new Uint8Array(i),j,k);case g.dataViewTag:if(c.byteLength!==i.byteLength||c.byteOffset!==i.byteOffset)return!1;return b(new Uint8Array(c),new Uint8Array(i),j,k);case g.errorTag:return c.name===i.name&&c.message===i.message;case g.objectTag:{if(!(b(c.constructor,i.constructor,j,k)||d.isPlainObject(c)&&d.isPlainObject(i)))return!1;let f=[...Object.keys(c),...e.getSymbols(c)],g=[...Object.keys(i),...e.getSymbols(i)];if(f.length!==g.length)return!1;for(let b=0;b<f.length;b++){let d=f[b],e=c[d];if(!Object.hasOwn(i,d))return!1;let g=i[d];if(!a(e,g,d,c,i,j,k))return!1}return!0}default:return!1}}finally{j.delete(c),j.delete(i)}}(b,c,l,m)}(a,b,void 0,void 0,void 0,void 0,c)}},9474:(a,b,c)=>{a.exports=c(3731).last},9618:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.uniqBy=function(a,b){let c=new Map;for(let d=0;d<a.length;d++){let e=a[d],f=b(e);c.has(f)||c.set(f,e)}return Array.from(c.values())}},9632:(a,b,c)=>{"use strict";a.exports=c(7668)},9733:(a,b,c)=>{"use strict";a.exports=c(907)},9760:(a,b,c)=>{"use strict";a.exports=c(3332)},9862:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(2923),e=c(7469);b.cloneDeepWith=function(a,b){return d.cloneDeepWith(a,(c,f,g,h)=>{let i=b?.(c,f,g,h);if(null!=i)return i;if("object"==typeof a)switch(Object.prototype.toString.call(a)){case e.numberTag:case e.stringTag:case e.booleanTag:{let b=new a.constructor(a?.valueOf());return d.copyProperties(b,a),b}case e.argumentsTag:{let b={};return d.copyProperties(b,a),b.length=a.length,b[Symbol.iterator]=a[Symbol.iterator],b}default:return}})}},9899:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isObjectLike=function(a){return"object"==typeof a&&null!==a}},9911:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(9404),e=c(15);b.isEqual=function(a,b){return d.isEqualWith(a,b,e.noop)}}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[985,400],()=>b(b.s=7156));module.exports=c})();