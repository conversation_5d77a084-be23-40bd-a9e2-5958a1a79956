{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/OpenPub/journal-transparency/src/app/store.ts"], "sourcesContent": ["/**\n * Zustand store for Journal Choice Transparency application.\n */\n\nimport { create } from 'zustand';\nimport { Journal, FilterState, AppState } from './types';\n\ninterface StoreActions {\n  setJournals: (journals: Journal[]) => void;\n  setFilters: (filters: Partial<FilterState>) => void;\n  setSelectedJournals: (selected: Set<string>) => void;\n  addSelectedJournal: (issn: string) => void;\n  removeSelectedJournal: (issn: string) => void;\n  clearSelectedJournals: () => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  applyFilters: () => void;\n}\n\ntype Store = AppState & StoreActions;\n\nconst initialFilters: FilterState = {\n  subjects: new Set(),\n  oaTypes: new Set(),\n  apcRange: [0, 10000],\n  impactRange: [0, 200],\n  searchTerm: '',\n};\n\nexport const useStore = create<Store>((set, get) => ({\n  // State\n  journals: [],\n  filteredJournals: [],\n  selectedJournals: new Set(),\n  filters: initialFilters,\n  isLoading: false,\n  error: null,\n\n  // Actions\n  setJournals: (journals) => {\n    set({ journals });\n    get().applyFilters();\n  },\n\n  setFilters: (newFilters) => {\n    const currentFilters = get().filters;\n    const updatedFilters = { ...currentFilters, ...newFilters };\n    set({ filters: updatedFilters });\n    get().applyFilters();\n  },\n\n  setSelectedJournals: (selected) => set({ selectedJournals: selected }),\n\n  addSelectedJournal: (issn) => {\n    const current = get().selectedJournals;\n    const updated = new Set(current);\n    updated.add(issn);\n    set({ selectedJournals: updated });\n  },\n\n  removeSelectedJournal: (issn) => {\n    const current = get().selectedJournals;\n    const updated = new Set(current);\n    updated.delete(issn);\n    set({ selectedJournals: updated });\n  },\n\n  clearSelectedJournals: () => set({ selectedJournals: new Set() }),\n\n  setLoading: (isLoading) => set({ isLoading }),\n\n  setError: (error) => set({ error }),\n\n  applyFilters: () => {\n    const { journals, filters } = get();\n    \n    const filtered = journals.filter((journal) => {\n      // Subject filter\n      if (filters.subjects.size > 0 && !filters.subjects.has(journal.subject)) {\n        return false;\n      }\n\n      // OA Type filter\n      if (filters.oaTypes.size > 0 && !filters.oaTypes.has(journal.oa_type)) {\n        return false;\n      }\n\n      // APC range filter\n      if (journal.apc_usd < filters.apcRange[0] || journal.apc_usd > filters.apcRange[1]) {\n        return false;\n      }\n\n      // Impact range filter\n      if (journal.impact_proxy < filters.impactRange[0] || journal.impact_proxy > filters.impactRange[1]) {\n        return false;\n      }\n\n      // Search term filter\n      if (filters.searchTerm && !journal.title.toLowerCase().includes(filters.searchTerm.toLowerCase())) {\n        return false;\n      }\n\n      return true;\n    });\n\n    set({ filteredJournals: filtered });\n  },\n}));\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;;AAiBA,MAAM,iBAA8B;IAClC,UAAU,IAAI;IACd,SAAS,IAAI;IACb,UAAU;QAAC;QAAG;KAAM;IACpB,aAAa;QAAC;QAAG;KAAI;IACrB,YAAY;AACd;AAEO,MAAM,WAAW,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAS,CAAC,KAAK,MAAQ,CAAC;QACnD,QAAQ;QACR,UAAU,EAAE;QACZ,kBAAkB,EAAE;QACpB,kBAAkB,IAAI;QACtB,SAAS;QACT,WAAW;QACX,OAAO;QAEP,UAAU;QACV,aAAa,CAAC;YACZ,IAAI;gBAAE;YAAS;YACf,MAAM,YAAY;QACpB;QAEA,YAAY,CAAC;YACX,MAAM,iBAAiB,MAAM,OAAO;YACpC,MAAM,iBAAiB;gBAAE,GAAG,cAAc;gBAAE,GAAG,UAAU;YAAC;YAC1D,IAAI;gBAAE,SAAS;YAAe;YAC9B,MAAM,YAAY;QACpB;QAEA,qBAAqB,CAAC,WAAa,IAAI;gBAAE,kBAAkB;YAAS;QAEpE,oBAAoB,CAAC;YACnB,MAAM,UAAU,MAAM,gBAAgB;YACtC,MAAM,UAAU,IAAI,IAAI;YACxB,QAAQ,GAAG,CAAC;YACZ,IAAI;gBAAE,kBAAkB;YAAQ;QAClC;QAEA,uBAAuB,CAAC;YACtB,MAAM,UAAU,MAAM,gBAAgB;YACtC,MAAM,UAAU,IAAI,IAAI;YACxB,QAAQ,MAAM,CAAC;YACf,IAAI;gBAAE,kBAAkB;YAAQ;QAClC;QAEA,uBAAuB,IAAM,IAAI;gBAAE,kBAAkB,IAAI;YAAM;QAE/D,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAE3C,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QAEjC,cAAc;YACZ,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;YAE9B,MAAM,WAAW,SAAS,MAAM,CAAC,CAAC;gBAChC,iBAAiB;gBACjB,IAAI,QAAQ,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC,QAAQ,QAAQ,CAAC,GAAG,CAAC,QAAQ,OAAO,GAAG;oBACvE,OAAO;gBACT;gBAEA,iBAAiB;gBACjB,IAAI,QAAQ,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,QAAQ,OAAO,CAAC,GAAG,CAAC,QAAQ,OAAO,GAAG;oBACrE,OAAO;gBACT;gBAEA,mBAAmB;gBACnB,IAAI,QAAQ,OAAO,GAAG,QAAQ,QAAQ,CAAC,EAAE,IAAI,QAAQ,OAAO,GAAG,QAAQ,QAAQ,CAAC,EAAE,EAAE;oBAClF,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,IAAI,QAAQ,YAAY,GAAG,QAAQ,WAAW,CAAC,EAAE,IAAI,QAAQ,YAAY,GAAG,QAAQ,WAAW,CAAC,EAAE,EAAE;oBAClG,OAAO;gBACT;gBAEA,qBAAqB;gBACrB,IAAI,QAAQ,UAAU,IAAI,CAAC,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,UAAU,CAAC,WAAW,KAAK;oBACjG,OAAO;gBACT;gBAEA,OAAO;YACT;YAEA,IAAI;gBAAE,kBAAkB;YAAS;QACnC;IACF,CAAC", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/FilterPanel.tsx"], "sourcesContent": ["/**\n * Filter panel component for Journal Choice Transparency application.\n */\n\n'use client';\n\nimport React, { useMemo } from 'react';\nimport { useStore } from '../store';\nimport { Search, Filter } from 'lucide-react';\n\nconst FilterPanel: React.FC = () => {\n  const { \n    journals, \n    filters, \n    setFilters, \n    clearSelectedJournals \n  } = useStore();\n\n  // Get unique values for filter options\n  const filterOptions = useMemo(() => {\n    const subjects = new Set(journals.map(j => j.subject).filter(Boolean));\n    const oaTypes = new Set(journals.map(j => j.oa_type).filter(Boolean));\n    \n    const maxApc = Math.max(...journals.map(j => j.apc_usd));\n    const maxImpact = Math.max(...journals.map(j => j.impact_proxy));\n    \n    return {\n      subjects: Array.from(subjects).sort(),\n      oaTypes: Array.from(oaTypes).sort(),\n      maxApc: Math.ceil(maxApc / 1000) * 1000, // Round up to nearest 1000\n      maxImpact: Math.ceil(maxImpact / 10) * 10, // Round up to nearest 10\n    };\n  }, [journals]);\n\n  const handleSubjectChange = (subject: string, checked: boolean) => {\n    const newSubjects = new Set(filters.subjects);\n    if (checked) {\n      newSubjects.add(subject);\n    } else {\n      newSubjects.delete(subject);\n    }\n    setFilters({ subjects: newSubjects });\n    clearSelectedJournals();\n  };\n\n  const handleOATypeChange = (oaType: string, checked: boolean) => {\n    const newOATypes = new Set(filters.oaTypes);\n    if (checked) {\n      newOATypes.add(oaType);\n    } else {\n      newOATypes.delete(oaType);\n    }\n    setFilters({ oaTypes: newOATypes });\n    clearSelectedJournals();\n  };\n\n  const handleSearchChange = (searchTerm: string) => {\n    setFilters({ searchTerm });\n    clearSelectedJournals();\n  };\n\n  const handleAPCRangeChange = (value: number[]) => {\n    setFilters({ apcRange: [value[0], value[1]] as [number, number] });\n    clearSelectedJournals();\n  };\n\n  const handleImpactRangeChange = (value: number[]) => {\n    setFilters({ impactRange: [value[0], value[1]] as [number, number] });\n    clearSelectedJournals();\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border p-4 h-full overflow-y-auto\">\n      <div className=\"flex items-center gap-2 mb-4\">\n        <Filter className=\"w-5 h-5 text-gray-600\" />\n        <h2 className=\"text-lg font-semibold text-gray-900\">Filters</h2>\n      </div>\n\n      {/* Search */}\n      <div className=\"mb-6\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Search Journals\n        </label>\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n          <input\n            type=\"text\"\n            placeholder=\"Search by journal title...\"\n            value={filters.searchTerm}\n            onChange={(e) => handleSearchChange(e.target.value)}\n            className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n        </div>\n      </div>\n\n      {/* Subject Filter */}\n      <div className=\"mb-6\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Subject Area\n        </label>\n        <div className=\"max-h-40 overflow-y-auto space-y-2\">\n          {filterOptions.subjects.slice(0, 20).map((subject) => (\n            <label key={subject} className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={filters.subjects.has(subject)}\n                onChange={(e) => handleSubjectChange(subject, e.target.checked)}\n                className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n              />\n              <span className=\"ml-2 text-sm text-gray-700 truncate\">{subject}</span>\n            </label>\n          ))}\n        </div>\n      </div>\n\n      {/* Open Access Type Filter */}\n      <div className=\"mb-6\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Open Access Type\n        </label>\n        <div className=\"space-y-2\">\n          {filterOptions.oaTypes.map((oaType) => (\n            <label key={oaType} className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={filters.oaTypes.has(oaType)}\n                onChange={(e) => handleOATypeChange(oaType, e.target.checked)}\n                className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n              />\n              <span className=\"ml-2 text-sm text-gray-700\">{oaType}</span>\n            </label>\n          ))}\n        </div>\n      </div>\n\n      {/* APC Range */}\n      <div className=\"mb-6\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          APC Range (USD): ${filters.apcRange[0]} - ${filters.apcRange[1]}\n        </label>\n        <input\n          type=\"range\"\n          min=\"0\"\n          max={filterOptions.maxApc}\n          step=\"100\"\n          value={filters.apcRange[1]}\n          onChange={(e) => handleAPCRangeChange([filters.apcRange[0], parseInt(e.target.value)])}\n          className=\"w-full\"\n        />\n      </div>\n\n      {/* Impact Range */}\n      <div className=\"mb-6\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Min Impact: {filters.impactRange[0].toFixed(1)}\n        </label>\n        <input\n          type=\"range\"\n          min=\"0\"\n          max={filterOptions.maxImpact}\n          step=\"0.1\"\n          value={filters.impactRange[0]}\n          onChange={(e) => handleImpactRangeChange([parseFloat(e.target.value), filters.impactRange[1]])}\n          className=\"w-full\"\n        />\n      </div>\n\n      {/* Clear Filters */}\n      <button\n        onClick={() => {\n          setFilters({\n            subjects: new Set(),\n            oaTypes: new Set(),\n            apcRange: [0, filterOptions.maxApc],\n            impactRange: [0, filterOptions.maxImpact],\n            searchTerm: '',\n          });\n          clearSelectedJournals();\n        }}\n        className=\"w-full px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n      >\n        Clear All Filters\n      </button>\n    </div>\n  );\n};\n\nexport default FilterPanel;\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AACA;AACA;AAAA;AAJA;;;;;AAMA,MAAM,cAAwB;IAC5B,MAAM,EACJ,QAAQ,EACR,OAAO,EACP,UAAU,EACV,qBAAqB,EACtB,GAAG,CAAA,GAAA,mHAAA,CAAA,WAAQ,AAAD;IAEX,uCAAuC;IACvC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,MAAM,WAAW,IAAI,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,MAAM,CAAC;QAC7D,MAAM,UAAU,IAAI,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,MAAM,CAAC;QAE5D,MAAM,SAAS,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;QACtD,MAAM,YAAY,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,YAAY;QAE9D,OAAO;YACL,UAAU,MAAM,IAAI,CAAC,UAAU,IAAI;YACnC,SAAS,MAAM,IAAI,CAAC,SAAS,IAAI;YACjC,QAAQ,KAAK,IAAI,CAAC,SAAS,QAAQ;YACnC,WAAW,KAAK,IAAI,CAAC,YAAY,MAAM;QACzC;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,sBAAsB,CAAC,SAAiB;QAC5C,MAAM,cAAc,IAAI,IAAI,QAAQ,QAAQ;QAC5C,IAAI,SAAS;YACX,YAAY,GAAG,CAAC;QAClB,OAAO;YACL,YAAY,MAAM,CAAC;QACrB;QACA,WAAW;YAAE,UAAU;QAAY;QACnC;IACF;IAEA,MAAM,qBAAqB,CAAC,QAAgB;QAC1C,MAAM,aAAa,IAAI,IAAI,QAAQ,OAAO;QAC1C,IAAI,SAAS;YACX,WAAW,GAAG,CAAC;QACjB,OAAO;YACL,WAAW,MAAM,CAAC;QACpB;QACA,WAAW;YAAE,SAAS;QAAW;QACjC;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,WAAW;YAAE;QAAW;QACxB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,WAAW;YAAE,UAAU;gBAAC,KAAK,CAAC,EAAE;gBAAE,KAAK,CAAC,EAAE;aAAC;QAAqB;QAChE;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,WAAW;YAAE,aAAa;gBAAC,KAAK,CAAC,EAAE;gBAAE,KAAK,CAAC,EAAE;aAAC;QAAqB;QACnE;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;;;;;;;0BAItD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO,QAAQ,UAAU;gCACzB,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gCAClD,WAAU;;;;;;;;;;;;;;;;;;0BAMhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBAAI,WAAU;kCACZ,cAAc,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,wBACxC,8OAAC;gCAAoB,WAAU;;kDAC7B,8OAAC;wCACC,MAAK;wCACL,SAAS,QAAQ,QAAQ,CAAC,GAAG,CAAC;wCAC9B,UAAU,CAAC,IAAM,oBAAoB,SAAS,EAAE,MAAM,CAAC,OAAO;wCAC9D,WAAU;;;;;;kDAEZ,8OAAC;wCAAK,WAAU;kDAAuC;;;;;;;+BAP7C;;;;;;;;;;;;;;;;0BAclB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBAAI,WAAU;kCACZ,cAAc,OAAO,CAAC,GAAG,CAAC,CAAC,uBAC1B,8OAAC;gCAAmB,WAAU;;kDAC5B,8OAAC;wCACC,MAAK;wCACL,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;wCAC7B,UAAU,CAAC,IAAM,mBAAmB,QAAQ,EAAE,MAAM,CAAC,OAAO;wCAC5D,WAAU;;;;;;kDAEZ,8OAAC;wCAAK,WAAU;kDAA8B;;;;;;;+BAPpC;;;;;;;;;;;;;;;;0BAclB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;;4BAA+C;4BAC3C,QAAQ,QAAQ,CAAC,EAAE;4BAAC;4BAAK,QAAQ,QAAQ,CAAC,EAAE;;;;;;;kCAEjE,8OAAC;wBACC,MAAK;wBACL,KAAI;wBACJ,KAAK,cAAc,MAAM;wBACzB,MAAK;wBACL,OAAO,QAAQ,QAAQ,CAAC,EAAE;wBAC1B,UAAU,CAAC,IAAM,qBAAqB;gCAAC,QAAQ,QAAQ,CAAC,EAAE;gCAAE,SAAS,EAAE,MAAM,CAAC,KAAK;6BAAE;wBACrF,WAAU;;;;;;;;;;;;0BAKd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;;4BAA+C;4BACjD,QAAQ,WAAW,CAAC,EAAE,CAAC,OAAO,CAAC;;;;;;;kCAE9C,8OAAC;wBACC,MAAK;wBACL,KAAI;wBACJ,KAAK,cAAc,SAAS;wBAC5B,MAAK;wBACL,OAAO,QAAQ,WAAW,CAAC,EAAE;wBAC7B,UAAU,CAAC,IAAM,wBAAwB;gCAAC,WAAW,EAAE,MAAM,CAAC,KAAK;gCAAG,QAAQ,WAAW,CAAC,EAAE;6BAAC;wBAC7F,WAAU;;;;;;;;;;;;0BAKd,8OAAC;gBACC,SAAS;oBACP,WAAW;wBACT,UAAU,IAAI;wBACd,SAAS,IAAI;wBACb,UAAU;4BAAC;4BAAG,cAAc,MAAM;yBAAC;wBACnC,aAAa;4BAAC;4BAAG,cAAc,SAAS;yBAAC;wBACzC,YAAY;oBACd;oBACA;gBACF;gBACA,WAAU;0BACX;;;;;;;;;;;;AAKP;uCAEe", "debugId": null}}, {"offset": {"line": 484, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/ScatterPlot.tsx"], "sourcesContent": ["/**\n * Scatter plot component for Journal Choice Transparency application.\n */\n\n'use client';\n\nimport React, { useMemo } from 'react';\nimport {\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  ResponsiveContainer,\n  Legend,\n} from 'recharts';\nimport { useStore } from '../store';\n\nconst OA_TYPE_COLORS = {\n  'Fully OA': '#10B981', // Green\n  'Hybrid': '#F59E0B',   // Amber\n  'Diamond': '#8B5CF6',  // Purple\n  'Subscription': '#EF4444', // Red\n  'Unknown': '#6B7280',  // Gray\n};\n\nconst ScatterPlot: React.FC = () => {\n  const { filteredJournals, selectedJournals, addSelectedJournal, removeSelectedJournal } = useStore();\n\n  // Prepare data for scatter plot - sample for performance if too many points\n  const scatterData = useMemo(() => {\n    let journals = filteredJournals;\n\n    // If we have too many journals, sample them for better performance\n    if (journals.length > 5000) {\n      // Sort by impact and take top journals plus random sample\n      const sorted = [...journals].sort((a, b) => b.impact_proxy - a.impact_proxy);\n      const topJournals = sorted.slice(0, 2000);\n      const remaining = sorted.slice(2000);\n      const randomSample = remaining\n        .sort(() => 0.5 - Math.random())\n        .slice(0, 3000);\n      journals = [...topJournals, ...randomSample];\n    }\n\n    return journals.map((journal) => ({\n      x: journal.impact_proxy,\n      y: journal.apc_usd,\n      title: journal.title,\n      oa_type: journal.oa_type,\n      cost_efficiency: journal.cost_efficiency === 999999 ? Infinity : journal.cost_efficiency,\n      issn_l: journal.issn_l,\n      publisher: journal.publisher,\n      selected: selectedJournals.has(journal.issn_l),\n    }));\n  }, [filteredJournals, selectedJournals]);\n\n  // Group data by OA type for multiple scatter series\n  const dataByOAType = useMemo(() => {\n    const grouped: Record<string, typeof scatterData> = {};\n    scatterData.forEach((point) => {\n      if (!grouped[point.oa_type]) {\n        grouped[point.oa_type] = [];\n      }\n      grouped[point.oa_type].push(point);\n    });\n    return grouped;\n  }, [scatterData]);\n\n  const CustomTooltip = ({ active, payload }: any) => {\n    if (active && payload && payload.length) {\n      const data = payload[0].payload;\n      return (\n        <div className=\"bg-white p-3 border border-gray-300 rounded-lg shadow-lg\">\n          <p className=\"font-semibold text-gray-900 mb-1\">{data.title}</p>\n          <p className=\"text-sm text-gray-600\">Impact: {data.x.toFixed(2)}</p>\n          <p className=\"text-sm text-gray-600\">APC: ${data.y.toLocaleString()}</p>\n          <p className=\"text-sm text-gray-600\">OA Type: {data.oa_type}</p>\n          <p className=\"text-sm text-gray-600\">Publisher: {data.publisher}</p>\n          <p className=\"text-sm text-gray-600\">\n            Cost Efficiency: {\n              data.cost_efficiency === Infinity \n                ? '∞' \n                : data.cost_efficiency.toFixed(4)\n            }\n          </p>\n        </div>\n      );\n    }\n    return null;\n  };\n\n  const handlePointClick = (data: any) => {\n    if (selectedJournals.has(data.issn_l)) {\n      removeSelectedJournal(data.issn_l);\n    } else {\n      addSelectedJournal(data.issn_l);\n    }\n  };\n\n  return (\n    <div className=\"h-full\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">\n          Impact vs APC Cost\n        </h3>\n        <div className=\"text-sm text-gray-600\">\n          {filteredJournals.length} journals\n        </div>\n      </div>\n\n      <ResponsiveContainer width=\"100%\" height=\"90%\">\n        <ScatterChart\n          margin={{\n            top: 20,\n            right: 20,\n            bottom: 60,\n            left: 60,\n          }}\n        >\n          <CartesianGrid strokeDasharray=\"3 3\" />\n          <XAxis\n            type=\"number\"\n            dataKey=\"x\"\n            name=\"Impact Proxy\"\n            domain={['dataMin', 'dataMax']}\n            label={{ value: 'Impact Proxy (Citations per Doc)', position: 'insideBottom', offset: -10 }}\n          />\n          <YAxis\n            type=\"number\"\n            dataKey=\"y\"\n            name=\"APC (USD)\"\n            domain={['dataMin', 'dataMax']}\n            label={{ value: 'APC (USD)', angle: -90, position: 'insideLeft' }}\n          />\n          <Tooltip content={<CustomTooltip />} />\n          <Legend />\n          \n          {Object.entries(dataByOAType).map(([oaType, data]) => (\n            <Scatter\n              key={oaType}\n              name={oaType}\n              data={data}\n              fill={OA_TYPE_COLORS[oaType as keyof typeof OA_TYPE_COLORS] || '#6B7280'}\n              onClick={handlePointClick}\n              style={{ cursor: 'pointer' }}\n            />\n          ))}\n        </ScatterChart>\n      </ResponsiveContainer>\n\n      <div className=\"mt-4 text-xs text-gray-500\">\n        Click points to select journals. Selected journals will be highlighted in the table below.\n      </div>\n    </div>\n  );\n};\n\nexport default ScatterPlot;\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAbA;;;;;AAeA,MAAM,iBAAiB;IACrB,YAAY;IACZ,UAAU;IACV,WAAW;IACX,gBAAgB;IAChB,WAAW;AACb;AAEA,MAAM,cAAwB;IAC5B,MAAM,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,qBAAqB,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,WAAQ,AAAD;IAEjG,4EAA4E;IAC5E,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,IAAI,WAAW;QAEf,mEAAmE;QACnE,IAAI,SAAS,MAAM,GAAG,MAAM;YAC1B,0DAA0D;YAC1D,MAAM,SAAS;mBAAI;aAAS,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,YAAY,GAAG,EAAE,YAAY;YAC3E,MAAM,cAAc,OAAO,KAAK,CAAC,GAAG;YACpC,MAAM,YAAY,OAAO,KAAK,CAAC;YAC/B,MAAM,eAAe,UAClB,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM,IAC5B,KAAK,CAAC,GAAG;YACZ,WAAW;mBAAI;mBAAgB;aAAa;QAC9C;QAEA,OAAO,SAAS,GAAG,CAAC,CAAC,UAAY,CAAC;gBAChC,GAAG,QAAQ,YAAY;gBACvB,GAAG,QAAQ,OAAO;gBAClB,OAAO,QAAQ,KAAK;gBACpB,SAAS,QAAQ,OAAO;gBACxB,iBAAiB,QAAQ,eAAe,KAAK,SAAS,WAAW,QAAQ,eAAe;gBACxF,QAAQ,QAAQ,MAAM;gBACtB,WAAW,QAAQ,SAAS;gBAC5B,UAAU,iBAAiB,GAAG,CAAC,QAAQ,MAAM;YAC/C,CAAC;IACH,GAAG;QAAC;QAAkB;KAAiB;IAEvC,oDAAoD;IACpD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,MAAM,UAA8C,CAAC;QACrD,YAAY,OAAO,CAAC,CAAC;YACnB,IAAI,CAAC,OAAO,CAAC,MAAM,OAAO,CAAC,EAAE;gBAC3B,OAAO,CAAC,MAAM,OAAO,CAAC,GAAG,EAAE;YAC7B;YACA,OAAO,CAAC,MAAM,OAAO,CAAC,CAAC,IAAI,CAAC;QAC9B;QACA,OAAO;IACT,GAAG;QAAC;KAAY;IAEhB,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAO;QAC7C,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;YACvC,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,OAAO;YAC/B,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAoC,KAAK,KAAK;;;;;;kCAC3D,8OAAC;wBAAE,WAAU;;4BAAwB;4BAAS,KAAK,CAAC,CAAC,OAAO,CAAC;;;;;;;kCAC7D,8OAAC;wBAAE,WAAU;;4BAAwB;4BAAO,KAAK,CAAC,CAAC,cAAc;;;;;;;kCACjE,8OAAC;wBAAE,WAAU;;4BAAwB;4BAAU,KAAK,OAAO;;;;;;;kCAC3D,8OAAC;wBAAE,WAAU;;4BAAwB;4BAAY,KAAK,SAAS;;;;;;;kCAC/D,8OAAC;wBAAE,WAAU;;4BAAwB;4BAEjC,KAAK,eAAe,KAAK,WACrB,MACA,KAAK,eAAe,CAAC,OAAO,CAAC;;;;;;;;;;;;;QAK3C;QACA,OAAO;IACT;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,iBAAiB,GAAG,CAAC,KAAK,MAAM,GAAG;YACrC,sBAAsB,KAAK,MAAM;QACnC,OAAO;YACL,mBAAmB,KAAK,MAAM;QAChC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAGpD,8OAAC;wBAAI,WAAU;;4BACZ,iBAAiB,MAAM;4BAAC;;;;;;;;;;;;;0BAI7B,8OAAC,mKAAA,CAAA,sBAAmB;gBAAC,OAAM;gBAAO,QAAO;0BACvC,cAAA,8OAAC,wJAAA,CAAA,eAAY;oBACX,QAAQ;wBACN,KAAK;wBACL,OAAO;wBACP,QAAQ;wBACR,MAAM;oBACR;;sCAEA,8OAAC,6JAAA,CAAA,gBAAa;4BAAC,iBAAgB;;;;;;sCAC/B,8OAAC,qJAAA,CAAA,QAAK;4BACJ,MAAK;4BACL,SAAQ;4BACR,MAAK;4BACL,QAAQ;gCAAC;gCAAW;6BAAU;4BAC9B,OAAO;gCAAE,OAAO;gCAAoC,UAAU;gCAAgB,QAAQ,CAAC;4BAAG;;;;;;sCAE5F,8OAAC,qJAAA,CAAA,QAAK;4BACJ,MAAK;4BACL,SAAQ;4BACR,MAAK;4BACL,QAAQ;gCAAC;gCAAW;6BAAU;4BAC9B,OAAO;gCAAE,OAAO;gCAAa,OAAO,CAAC;gCAAI,UAAU;4BAAa;;;;;;sCAElE,8OAAC,uJAAA,CAAA,UAAO;4BAAC,uBAAS,8OAAC;;;;;;;;;;sCACnB,8OAAC,sJAAA,CAAA,SAAM;;;;;wBAEN,OAAO,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,QAAQ,KAAK,iBAC/C,8OAAC,uJAAA,CAAA,UAAO;gCAEN,MAAM;gCACN,MAAM;gCACN,MAAM,cAAc,CAAC,OAAsC,IAAI;gCAC/D,SAAS;gCACT,OAAO;oCAAE,QAAQ;gCAAU;+BALtB;;;;;;;;;;;;;;;;0BAWb,8OAAC;gBAAI,WAAU;0BAA6B;;;;;;;;;;;;AAKlD;uCAEe", "debugId": null}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/ExportButton.tsx"], "sourcesContent": ["/**\n * Export button component for Journal Choice Transparency application.\n */\n\n'use client';\n\nimport React from 'react';\nimport { useStore } from '../store';\nimport { Download } from 'lucide-react';\n\nconst ExportButton: React.FC = () => {\n  const { filteredJournals, selectedJournals } = useStore();\n\n  const exportToCSV = (journals: any[], filename: string) => {\n    if (journals.length === 0) return;\n\n    // Define CSV headers\n    const headers = [\n      'Title',\n      'ISSN-L',\n      'Subject',\n      'Impact Proxy',\n      'APC (USD)',\n      'OA Type',\n      'License',\n      'Cost Efficiency',\n      'Publisher',\n      'Last Verified',\n    ];\n\n    // Convert journals to CSV rows\n    const csvRows = [\n      headers.join(','),\n      ...journals.map(journal => [\n        `\"${journal.title.replace(/\"/g, '\"\"')}\"`,\n        journal.issn_l,\n        `\"${journal.subject.replace(/\"/g, '\"\"')}\"`,\n        journal.impact_proxy.toFixed(2),\n        journal.apc_usd,\n        `\"${journal.oa_type}\"`,\n        `\"${journal.license}\"`,\n        journal.cost_efficiency === Infinity ? 'Infinity' : journal.cost_efficiency.toFixed(4),\n        `\"${journal.publisher.replace(/\"/g, '\"\"')}\"`,\n        journal.last_verified,\n      ].join(','))\n    ];\n\n    // Create and download CSV file\n    const csvContent = csvRows.join('\\n');\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\n    const link = document.createElement('a');\n    \n    if (link.download !== undefined) {\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', filename);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n  };\n\n  const handleExportSelected = () => {\n    const selectedJournalsList = filteredJournals.filter(journal => \n      selectedJournals.has(journal.issn_l)\n    );\n    exportToCSV(selectedJournalsList, 'selected_journals.csv');\n  };\n\n  const handleExportFiltered = () => {\n    exportToCSV(filteredJournals, 'filtered_journals.csv');\n  };\n\n  return (\n    <div className=\"flex items-center gap-2\">\n      <button\n        onClick={handleExportFiltered}\n        disabled={filteredJournals.length === 0}\n        className=\"flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n      >\n        <Download className=\"w-4 h-4\" />\n        Export Filtered ({filteredJournals.length})\n      </button>\n      \n      {selectedJournals.size > 0 && (\n        <button\n          onClick={handleExportSelected}\n          className=\"flex items-center gap-2 px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n        >\n          <Download className=\"w-4 h-4\" />\n          Export Selected ({selectedJournals.size})\n        </button>\n      )}\n    </div>\n  );\n};\n\nexport default ExportButton;\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAKD;AACA;AAJA;;;;AAMA,MAAM,eAAyB;IAC7B,MAAM,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,WAAQ,AAAD;IAEtD,MAAM,cAAc,CAAC,UAAiB;QACpC,IAAI,SAAS,MAAM,KAAK,GAAG;QAE3B,qBAAqB;QACrB,MAAM,UAAU;YACd;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,+BAA+B;QAC/B,MAAM,UAAU;YACd,QAAQ,IAAI,CAAC;eACV,SAAS,GAAG,CAAC,CAAA,UAAW;oBACzB,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAC,OAAO,CAAC,MAAM,MAAM,CAAC,CAAC;oBACxC,QAAQ,MAAM;oBACd,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,OAAO,CAAC,MAAM,MAAM,CAAC,CAAC;oBAC1C,QAAQ,YAAY,CAAC,OAAO,CAAC;oBAC7B,QAAQ,OAAO;oBACf,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,CAAC,CAAC;oBACtB,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,CAAC,CAAC;oBACtB,QAAQ,eAAe,KAAK,WAAW,aAAa,QAAQ,eAAe,CAAC,OAAO,CAAC;oBACpF,CAAC,CAAC,EAAE,QAAQ,SAAS,CAAC,OAAO,CAAC,MAAM,MAAM,CAAC,CAAC;oBAC5C,QAAQ,aAAa;iBACtB,CAAC,IAAI,CAAC;SACR;QAED,+BAA+B;QAC/B,MAAM,aAAa,QAAQ,IAAI,CAAC;QAChC,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAA0B;QACtE,MAAM,OAAO,SAAS,aAAa,CAAC;QAEpC,IAAI,KAAK,QAAQ,KAAK,WAAW;YAC/B,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,KAAK,YAAY,CAAC,QAAQ;YAC1B,KAAK,YAAY,CAAC,YAAY;YAC9B,KAAK,KAAK,CAAC,UAAU,GAAG;YACxB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;IACF;IAEA,MAAM,uBAAuB;QAC3B,MAAM,uBAAuB,iBAAiB,MAAM,CAAC,CAAA,UACnD,iBAAiB,GAAG,CAAC,QAAQ,MAAM;QAErC,YAAY,sBAAsB;IACpC;IAEA,MAAM,uBAAuB;QAC3B,YAAY,kBAAkB;IAChC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,SAAS;gBACT,UAAU,iBAAiB,MAAM,KAAK;gBACtC,WAAU;;kCAEV,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAAY;oBACd,iBAAiB,MAAM;oBAAC;;;;;;;YAG3C,iBAAiB,IAAI,GAAG,mBACvB,8OAAC;gBACC,SAAS;gBACT,WAAU;;kCAEV,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAAY;oBACd,iBAAiB,IAAI;oBAAC;;;;;;;;;;;;;AAKlD;uCAEe", "debugId": null}}, {"offset": {"line": 914, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/DataTable.tsx"], "sourcesContent": ["/**\n * Data table component for Journal Choice Transparency application.\n */\n\n'use client';\n\nimport React, { useMemo } from 'react';\nimport {\n  useReactTable,\n  getCoreRowModel,\n  getSortedRowModel,\n  flexRender,\n  createColumnHelper,\n  SortingState,\n} from '@tanstack/react-table';\nimport { useStore } from '../store';\nimport { Journal } from '../types';\nimport { ExternalLink, ArrowUpDown } from 'lucide-react';\nimport ExportButton from './ExportButton';\n\nconst columnHelper = createColumnHelper<Journal>();\n\nconst DataTable: React.FC = () => {\n  const { \n    filteredJournals, \n    selectedJournals, \n    addSelectedJournal, \n    removeSelectedJournal \n  } = useStore();\n\n  const [sorting, setSorting] = React.useState<SortingState>([\n    { id: 'impact_proxy', desc: true }\n  ]);\n\n  const columns = useMemo(\n    () => [\n      columnHelper.accessor('title', {\n        header: 'Journal Title',\n        cell: (info) => (\n          <div className=\"flex items-center gap-2\">\n            <span className=\"font-medium text-gray-900 truncate max-w-xs\">\n              {info.getValue()}\n            </span>\n            {info.row.original.journal_url && (\n              <a\n                href={info.row.original.journal_url}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-blue-600 hover:text-blue-800\"\n              >\n                <ExternalLink className=\"w-4 h-4\" />\n              </a>\n            )}\n          </div>\n        ),\n        size: 300,\n      }),\n      columnHelper.accessor('subject', {\n        header: 'Subject',\n        cell: (info) => (\n          <span className=\"text-sm text-gray-700 truncate\">\n            {info.getValue()}\n          </span>\n        ),\n        size: 150,\n      }),\n      columnHelper.accessor('impact_proxy', {\n        header: ({ column }) => (\n          <button\n            className=\"flex items-center gap-1 hover:text-blue-600\"\n            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\n          >\n            Impact\n            <ArrowUpDown className=\"w-4 h-4\" />\n          </button>\n        ),\n        cell: (info) => (\n          <span className=\"font-mono text-sm\">\n            {info.getValue().toFixed(2)}\n          </span>\n        ),\n        size: 100,\n      }),\n      columnHelper.accessor('apc_usd', {\n        header: ({ column }) => (\n          <button\n            className=\"flex items-center gap-1 hover:text-blue-600\"\n            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\n          >\n            APC (USD)\n            <ArrowUpDown className=\"w-4 h-4\" />\n          </button>\n        ),\n        cell: (info) => (\n          <span className=\"font-mono text-sm\">\n            ${info.getValue().toLocaleString()}\n          </span>\n        ),\n        size: 120,\n      }),\n      columnHelper.accessor('cost_efficiency', {\n        header: ({ column }) => (\n          <button\n            className=\"flex items-center gap-1 hover:text-blue-600\"\n            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\n          >\n            Cost Efficiency\n            <ArrowUpDown className=\"w-4 h-4\" />\n          </button>\n        ),\n        cell: (info) => (\n          <span className=\"font-mono text-sm\">\n            {info.getValue() === Infinity ? '∞' : info.getValue().toFixed(4)}\n          </span>\n        ),\n        size: 130,\n      }),\n      columnHelper.accessor('oa_type', {\n        header: 'OA Type',\n        cell: (info) => {\n          const oaType = info.getValue();\n          const colorClass = {\n            'Fully OA': 'bg-green-100 text-green-800',\n            'Hybrid': 'bg-yellow-100 text-yellow-800',\n            'Diamond': 'bg-purple-100 text-purple-800',\n            'Subscription': 'bg-red-100 text-red-800',\n            'Unknown': 'bg-gray-100 text-gray-800',\n          }[oaType] || 'bg-gray-100 text-gray-800';\n          \n          return (\n            <span className={`px-2 py-1 rounded-full text-xs font-medium ${colorClass}`}>\n              {oaType}\n            </span>\n          );\n        },\n        size: 120,\n      }),\n      columnHelper.accessor('publisher', {\n        header: 'Publisher',\n        cell: (info) => (\n          <span className=\"text-sm text-gray-700 truncate max-w-xs\">\n            {info.getValue()}\n          </span>\n        ),\n        size: 200,\n      }),\n    ],\n    []\n  );\n\n  const table = useReactTable({\n    data: filteredJournals,\n    columns,\n    state: {\n      sorting,\n    },\n    onSortingChange: setSorting,\n    getCoreRowModel: getCoreRowModel(),\n    getSortedRowModel: getSortedRowModel(),\n  });\n\n  const handleRowClick = (journal: Journal) => {\n    if (selectedJournals.has(journal.issn_l)) {\n      removeSelectedJournal(journal.issn_l);\n    } else {\n      addSelectedJournal(journal.issn_l);\n    }\n  };\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">\n          Journal Data\n        </h3>\n        <div className=\"flex items-center gap-4\">\n          <div className=\"flex items-center gap-4 text-sm text-gray-600\">\n            <span>{filteredJournals.length} journals</span>\n            {selectedJournals.size > 0 && (\n              <span className=\"text-blue-600 font-medium\">\n                {selectedJournals.size} selected\n              </span>\n            )}\n          </div>\n          <ExportButton />\n        </div>\n      </div>\n\n      <div className=\"flex-1 overflow-auto border border-gray-200 rounded-lg\">\n        <table className=\"min-w-full divide-y divide-gray-200\">\n          <thead className=\"bg-gray-50 sticky top-0\">\n            {table.getHeaderGroups().map((headerGroup) => (\n              <tr key={headerGroup.id}>\n                {headerGroup.headers.map((header) => (\n                  <th\n                    key={header.id}\n                    className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n                    style={{ width: header.getSize() }}\n                  >\n                    {header.isPlaceholder\n                      ? null\n                      : flexRender(\n                          header.column.columnDef.header,\n                          header.getContext()\n                        )}\n                  </th>\n                ))}\n              </tr>\n            ))}\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {table.getRowModel().rows.map((row) => {\n              const isSelected = selectedJournals.has(row.original.issn_l);\n              return (\n                <tr\n                  key={row.id}\n                  onClick={() => handleRowClick(row.original)}\n                  className={`cursor-pointer hover:bg-gray-50 ${\n                    isSelected ? 'bg-blue-50 border-l-4 border-blue-500' : ''\n                  }`}\n                >\n                  {row.getVisibleCells().map((cell) => (\n                    <td\n                      key={cell.id}\n                      className=\"px-4 py-3 whitespace-nowrap text-sm\"\n                    >\n                      {flexRender(\n                        cell.column.columnDef.cell,\n                        cell.getContext()\n                      )}\n                    </td>\n                  ))}\n                </tr>\n              );\n            })}\n          </tbody>\n        </table>\n      </div>\n\n      <div className=\"mt-4 text-xs text-gray-500\">\n        Click rows to select journals. Selected journals will be highlighted in the scatter plot above.\n      </div>\n    </div>\n  );\n};\n\nexport default DataTable;\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AACA;AAAA;AAQA;AAEA;AAAA;AACA;AAdA;;;;;;;AAgBA,MAAM,eAAe,CAAA,GAAA,qKAAA,CAAA,qBAAkB,AAAD;AAEtC,MAAM,YAAsB;IAC1B,MAAM,EACJ,gBAAgB,EAChB,gBAAgB,EAChB,kBAAkB,EAClB,qBAAqB,EACtB,GAAG,CAAA,GAAA,mHAAA,CAAA,WAAQ,AAAD;IAEX,MAAM,CAAC,SAAS,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAe;QACzD;YAAE,IAAI;YAAgB,MAAM;QAAK;KAClC;IAED,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACpB,IAAM;YACJ,aAAa,QAAQ,CAAC,SAAS;gBAC7B,QAAQ;gBACR,MAAM,CAAC,qBACL,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CACb,KAAK,QAAQ;;;;;;4BAEf,KAAK,GAAG,CAAC,QAAQ,CAAC,WAAW,kBAC5B,8OAAC;gCACC,MAAM,KAAK,GAAG,CAAC,QAAQ,CAAC,WAAW;gCACnC,QAAO;gCACP,KAAI;gCACJ,WAAU;0CAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;gBAKhC,MAAM;YACR;YACA,aAAa,QAAQ,CAAC,WAAW;gBAC/B,QAAQ;gBACR,MAAM,CAAC,qBACL,8OAAC;wBAAK,WAAU;kCACb,KAAK,QAAQ;;;;;;gBAGlB,MAAM;YACR;YACA,aAAa,QAAQ,CAAC,gBAAgB;gBACpC,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;;4BAC9D;0CAEC,8OAAC,wNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;;gBAG3B,MAAM,CAAC,qBACL,8OAAC;wBAAK,WAAU;kCACb,KAAK,QAAQ,GAAG,OAAO,CAAC;;;;;;gBAG7B,MAAM;YACR;YACA,aAAa,QAAQ,CAAC,WAAW;gBAC/B,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;;4BAC9D;0CAEC,8OAAC,wNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;;gBAG3B,MAAM,CAAC,qBACL,8OAAC;wBAAK,WAAU;;4BAAoB;4BAChC,KAAK,QAAQ,GAAG,cAAc;;;;;;;gBAGpC,MAAM;YACR;YACA,aAAa,QAAQ,CAAC,mBAAmB;gBACvC,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;;4BAC9D;0CAEC,8OAAC,wNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;;gBAG3B,MAAM,CAAC,qBACL,8OAAC;wBAAK,WAAU;kCACb,KAAK,QAAQ,OAAO,WAAW,MAAM,KAAK,QAAQ,GAAG,OAAO,CAAC;;;;;;gBAGlE,MAAM;YACR;YACA,aAAa,QAAQ,CAAC,WAAW;gBAC/B,QAAQ;gBACR,MAAM,CAAC;oBACL,MAAM,SAAS,KAAK,QAAQ;oBAC5B,MAAM,aAAa;wBACjB,YAAY;wBACZ,UAAU;wBACV,WAAW;wBACX,gBAAgB;wBAChB,WAAW;oBACb,CAAC,CAAC,OAAO,IAAI;oBAEb,qBACE,8OAAC;wBAAK,WAAW,CAAC,2CAA2C,EAAE,YAAY;kCACxE;;;;;;gBAGP;gBACA,MAAM;YACR;YACA,aAAa,QAAQ,CAAC,aAAa;gBACjC,QAAQ;gBACR,MAAM,CAAC,qBACL,8OAAC;wBAAK,WAAU;kCACb,KAAK,QAAQ;;;;;;gBAGlB,MAAM;YACR;SACD,EACD,EAAE;IAGJ,MAAM,QAAQ,CAAA,GAAA,sLAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B,MAAM;QACN;QACA,OAAO;YACL;QACF;QACA,iBAAiB;QACjB,iBAAiB,CAAA,GAAA,qKAAA,CAAA,kBAAe,AAAD;QAC/B,mBAAmB,CAAA,GAAA,qKAAA,CAAA,oBAAiB,AAAD;IACrC;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,iBAAiB,GAAG,CAAC,QAAQ,MAAM,GAAG;YACxC,sBAAsB,QAAQ,MAAM;QACtC,OAAO;YACL,mBAAmB,QAAQ,MAAM;QACnC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAGpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAM,iBAAiB,MAAM;4CAAC;;;;;;;oCAC9B,iBAAiB,IAAI,GAAG,mBACvB,8OAAC;wCAAK,WAAU;;4CACb,iBAAiB,IAAI;4CAAC;;;;;;;;;;;;;0CAI7B,8OAAC,yIAAA,CAAA,UAAY;;;;;;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;4BAAM,WAAU;sCACd,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAC5B,8OAAC;8CACE,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,uBACxB,8OAAC;4CAEC,WAAU;4CACV,OAAO;gDAAE,OAAO,OAAO,OAAO;4CAAG;sDAEhC,OAAO,aAAa,GACjB,OACA,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EACP,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;2CARlB,OAAO,EAAE;;;;;mCAHX,YAAY,EAAE;;;;;;;;;;sCAkB3B,8OAAC;4BAAM,WAAU;sCACd,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;gCAC7B,MAAM,aAAa,iBAAiB,GAAG,CAAC,IAAI,QAAQ,CAAC,MAAM;gCAC3D,qBACE,8OAAC;oCAEC,SAAS,IAAM,eAAe,IAAI,QAAQ;oCAC1C,WAAW,CAAC,gCAAgC,EAC1C,aAAa,0CAA0C,IACvD;8CAED,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,qBAC1B,8OAAC;4CAEC,WAAU;sDAET,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EACR,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;2CALZ,KAAK,EAAE;;;;;mCARX,IAAI,EAAE;;;;;4BAmBjB;;;;;;;;;;;;;;;;;0BAKN,8OAAC;gBAAI,WAAU;0BAA6B;;;;;;;;;;;;AAKlD;uCAEe", "debugId": null}}, {"offset": {"line": 1286, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/Header.tsx"], "sourcesContent": ["/**\n * Header component for Journal Choice Transparency application.\n */\n\nimport React from 'react';\n\nconst Header: React.FC = () => {\n  return (\n    <header className=\"bg-white shadow-sm border-b\">\n      <div className=\"container mx-auto px-4 py-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">\n              Journal Choice Transparency\n            </h1>\n            <p className=\"text-gray-600 mt-2\">\n              Find the right journal for your research based on impact, cost, and openness\n            </p>\n          </div>\n          \n          <div className=\"flex items-center space-x-4\">\n            <a\n              href=\"#about\"\n              className=\"text-blue-600 hover:text-blue-800 font-medium\"\n            >\n              About\n            </a>\n            <a\n              href=\"#methodology\"\n              className=\"text-blue-600 hover:text-blue-800 font-medium\"\n            >\n              Data Sources\n            </a>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAID,MAAM,SAAmB;IACvB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAKpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 1374, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/Footer.tsx"], "sourcesContent": ["/**\n * Footer component for Journal Choice Transparency application.\n */\n\nimport React from 'react';\n\nconst Footer: React.FC = () => {\n  return (\n    <footer className=\"bg-white border-t mt-8\">\n      <div className=\"container mx-auto px-4 py-6\">\n        <div className=\"flex flex-col md:flex-row justify-between items-center\">\n          <div className=\"text-sm text-gray-600 mb-4 md:mb-0\">\n            <p>\n              Data sources: SciMago Journal Rankings, MDPI, OpenAPC, DOAJ\n            </p>\n            <p className=\"mt-1\">\n              Last updated: July 19, 2025\n            </p>\n          </div>\n          \n          <div className=\"flex items-center space-x-6 text-sm\">\n            <a\n              href=\"https://github.com\"\n              className=\"text-gray-600 hover:text-gray-900\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n            >\n              GitHub\n            </a>\n            <a\n              href=\"#methodology\"\n              className=\"text-gray-600 hover:text-gray-900\"\n            >\n              Methodology\n            </a>\n            <a\n              href=\"#contact\"\n              className=\"text-gray-600 hover:text-gray-900\"\n            >\n              Contact\n            </a>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAID,MAAM,SAAmB;IACvB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAE;;;;;;0CAGH,8OAAC;gCAAE,WAAU;0CAAO;;;;;;;;;;;;kCAKtB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,WAAU;gCACV,QAAO;gCACP,KAAI;0CACL;;;;;;0CAGD,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 1473, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/SummaryStats.tsx"], "sourcesContent": ["/**\n * Summary statistics component for Journal Choice Transparency application.\n */\n\n'use client';\n\nimport React, { useMemo } from 'react';\nimport { useStore } from '../store';\nimport { BarChart3, DollarSign, Target, Zap } from 'lucide-react';\n\nconst SummaryStats: React.FC = () => {\n  const { filteredJournals } = useStore();\n\n  const stats = useMemo(() => {\n    if (filteredJournals.length === 0) {\n      return {\n        totalJournals: 0,\n        avgImpact: 0,\n        avgAPC: 0,\n        fullyOACount: 0,\n        diamondCount: 0,\n      };\n    }\n\n    const totalJournals = filteredJournals.length;\n    const avgImpact = filteredJournals.reduce((sum, j) => sum + j.impact_proxy, 0) / totalJournals;\n    const avgAPC = filteredJournals.reduce((sum, j) => sum + j.apc_usd, 0) / totalJournals;\n    const fullyOACount = filteredJournals.filter(j => j.oa_type === 'Fully OA').length;\n    const diamondCount = filteredJournals.filter(j => j.oa_type === 'Diamond').length;\n\n    return {\n      totalJournals,\n      avgImpact,\n      avgAPC,\n      fullyOACount,\n      diamondCount,\n    };\n  }, [filteredJournals]);\n\n  const statCards = [\n    {\n      title: 'Total Journals',\n      value: stats.totalJournals.toLocaleString(),\n      icon: BarChart3,\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-50',\n    },\n    {\n      title: 'Avg Impact',\n      value: stats.avgImpact.toFixed(2),\n      icon: Target,\n      color: 'text-green-600',\n      bgColor: 'bg-green-50',\n    },\n    {\n      title: 'Avg APC',\n      value: `$${Math.round(stats.avgAPC).toLocaleString()}`,\n      icon: DollarSign,\n      color: 'text-yellow-600',\n      bgColor: 'bg-yellow-50',\n    },\n    {\n      title: 'Fully Open Access',\n      value: `${stats.fullyOACount} (${((stats.fullyOACount / stats.totalJournals) * 100).toFixed(1)}%)`,\n      icon: Zap,\n      color: 'text-purple-600',\n      bgColor: 'bg-purple-50',\n    },\n  ];\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\">\n      {statCards.map((stat, index) => {\n        const Icon = stat.icon;\n        return (\n          <div\n            key={index}\n            className={`${stat.bgColor} rounded-lg p-4 border border-gray-200`}\n          >\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">{stat.title}</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{stat.value}</p>\n              </div>\n              <Icon className={`w-8 h-8 ${stat.color}`} />\n            </div>\n          </div>\n        );\n      })}\n    </div>\n  );\n};\n\nexport default SummaryStats;\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,eAAyB;IAC7B,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,WAAQ,AAAD;IAEpC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACpB,IAAI,iBAAiB,MAAM,KAAK,GAAG;YACjC,OAAO;gBACL,eAAe;gBACf,WAAW;gBACX,QAAQ;gBACR,cAAc;gBACd,cAAc;YAChB;QACF;QAEA,MAAM,gBAAgB,iBAAiB,MAAM;QAC7C,MAAM,YAAY,iBAAiB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,YAAY,EAAE,KAAK;QACjF,MAAM,SAAS,iBAAiB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,OAAO,EAAE,KAAK;QACzE,MAAM,eAAe,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,YAAY,MAAM;QAClF,MAAM,eAAe,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,WAAW,MAAM;QAEjF,OAAO;YACL;YACA;YACA;YACA;YACA;QACF;IACF,GAAG;QAAC;KAAiB;IAErB,MAAM,YAAY;QAChB;YACE,OAAO;YACP,OAAO,MAAM,aAAa,CAAC,cAAc;YACzC,MAAM,kNAAA,CAAA,YAAS;YACf,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,MAAM,SAAS,CAAC,OAAO,CAAC;YAC/B,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,MAAM,MAAM,EAAE,cAAc,IAAI;YACtD,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,GAAG,MAAM,YAAY,CAAC,EAAE,EAAE,CAAC,AAAC,MAAM,YAAY,GAAG,MAAM,aAAa,GAAI,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;YAClG,MAAM,gMAAA,CAAA,MAAG;YACT,OAAO;YACP,SAAS;QACX;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACZ,UAAU,GAAG,CAAC,CAAC,MAAM;YACpB,MAAM,OAAO,KAAK,IAAI;YACtB,qBACE,8OAAC;gBAEC,WAAW,GAAG,KAAK,OAAO,CAAC,sCAAsC,CAAC;0BAElE,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAAqC,KAAK,KAAK;;;;;;8CAC5D,8OAAC;oCAAE,WAAU;8CAAoC,KAAK,KAAK;;;;;;;;;;;;sCAE7D,8OAAC;4BAAK,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;;eARrC;;;;;QAYX;;;;;;AAGN;uCAEe", "debugId": null}}, {"offset": {"line": 1610, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/Recommendations.tsx"], "sourcesContent": ["/**\n * Recommendations component for Journal Choice Transparency application.\n */\n\n'use client';\n\nimport React, { useMemo } from 'react';\nimport { useStore } from '../store';\nimport { Journal } from '../types';\nimport { Star, TrendingUp, DollarSign } from 'lucide-react';\n\nconst Recommendations: React.FC = () => {\n  const { filteredJournals, addSelectedJournal } = useStore();\n\n  const recommendations = useMemo(() => {\n    if (filteredJournals.length === 0) return { highImpact: [], bestValue: [], diamond: [] };\n\n    // High impact journals (top 10% by impact)\n    const sortedByImpact = [...filteredJournals]\n      .sort((a, b) => b.impact_proxy - a.impact_proxy)\n      .slice(0, Math.max(5, Math.floor(filteredJournals.length * 0.1)));\n\n    // Best value journals (high cost efficiency, excluding free journals)\n    const paidJournals = filteredJournals.filter(j => j.apc_usd > 0);\n    const bestValue = [...paidJournals]\n      .sort((a, b) => {\n        const aEff = a.cost_efficiency === 999999 ? Infinity : a.cost_efficiency;\n        const bEff = b.cost_efficiency === 999999 ? Infinity : b.cost_efficiency;\n        return bEff - aEff;\n      })\n      .slice(0, 5);\n\n    // Diamond/Free OA journals with good impact\n    const diamond = filteredJournals\n      .filter(j => j.apc_usd === 0 && j.impact_proxy > 1)\n      .sort((a, b) => b.impact_proxy - a.impact_proxy)\n      .slice(0, 5);\n\n    return {\n      highImpact: sortedByImpact.slice(0, 5),\n      bestValue,\n      diamond,\n    };\n  }, [filteredJournals]);\n\n  const RecommendationCard: React.FC<{\n    journal: Journal;\n    reason: string;\n    icon: React.ComponentType<any>;\n    iconColor: string;\n  }> = ({ journal, reason, icon: Icon, iconColor }) => (\n    <div className=\"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow\">\n      <div className=\"flex items-start gap-3\">\n        <Icon className={`w-5 h-5 mt-1 ${iconColor}`} />\n        <div className=\"flex-1 min-w-0\">\n          <h4 className=\"font-medium text-gray-900 truncate\">{journal.title}</h4>\n          <p className=\"text-sm text-gray-600 mt-1\">{reason}</p>\n          <div className=\"flex items-center gap-4 mt-2 text-xs text-gray-500\">\n            <span>Impact: {journal.impact_proxy.toFixed(2)}</span>\n            <span>APC: ${journal.apc_usd.toLocaleString()}</span>\n            <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n              journal.oa_type === 'Fully OA' ? 'bg-green-100 text-green-800' :\n              journal.oa_type === 'Hybrid' ? 'bg-yellow-100 text-yellow-800' :\n              journal.oa_type === 'Diamond' ? 'bg-purple-100 text-purple-800' :\n              'bg-gray-100 text-gray-800'\n            }`}>\n              {journal.oa_type}\n            </span>\n          </div>\n          <button\n            onClick={() => addSelectedJournal(journal.issn_l)}\n            className=\"mt-2 text-xs text-blue-600 hover:text-blue-800 font-medium\"\n          >\n            Add to selection\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n\n  if (filteredJournals.length === 0) {\n    return null;\n  }\n\n  return (\n    <div className=\"bg-gray-50 rounded-lg p-4 mb-6\">\n      <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n        Recommendations Based on Your Filters\n      </h3>\n      \n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* High Impact */}\n        {recommendations.highImpact.length > 0 && (\n          <div>\n            <h4 className=\"font-medium text-gray-900 mb-3 flex items-center gap-2\">\n              <TrendingUp className=\"w-4 h-4 text-green-600\" />\n              High Impact\n            </h4>\n            <div className=\"space-y-3\">\n              {recommendations.highImpact.map((journal) => (\n                <RecommendationCard\n                  key={journal.issn_l}\n                  journal={journal}\n                  reason={`Top ${Math.round((journal.impact_proxy / Math.max(...filteredJournals.map(j => j.impact_proxy))) * 100)}% impact in your selection`}\n                  icon={TrendingUp}\n                  iconColor=\"text-green-600\"\n                />\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Best Value */}\n        {recommendations.bestValue.length > 0 && (\n          <div>\n            <h4 className=\"font-medium text-gray-900 mb-3 flex items-center gap-2\">\n              <Star className=\"w-4 h-4 text-blue-600\" />\n              Best Value\n            </h4>\n            <div className=\"space-y-3\">\n              {recommendations.bestValue.map((journal) => (\n                <RecommendationCard\n                  key={journal.issn_l}\n                  journal={journal}\n                  reason=\"High impact per dollar spent\"\n                  icon={Star}\n                  iconColor=\"text-blue-600\"\n                />\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Diamond/Free OA */}\n        {recommendations.diamond.length > 0 && (\n          <div>\n            <h4 className=\"font-medium text-gray-900 mb-3 flex items-center gap-2\">\n              <DollarSign className=\"w-4 h-4 text-purple-600\" />\n              Free & High Quality\n            </h4>\n            <div className=\"space-y-3\">\n              {recommendations.diamond.map((journal) => (\n                <RecommendationCard\n                  key={journal.issn_l}\n                  journal={journal}\n                  reason=\"No APC with good impact\"\n                  icon={DollarSign}\n                  iconColor=\"text-purple-600\"\n                />\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Recommendations;\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AACA;AAEA;AAAA;AAAA;AALA;;;;;AAOA,MAAM,kBAA4B;IAChC,MAAM,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,WAAQ,AAAD;IAExD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,iBAAiB,MAAM,KAAK,GAAG,OAAO;YAAE,YAAY,EAAE;YAAE,WAAW,EAAE;YAAE,SAAS,EAAE;QAAC;QAEvF,2CAA2C;QAC3C,MAAM,iBAAiB;eAAI;SAAiB,CACzC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,YAAY,GAAG,EAAE,YAAY,EAC9C,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,iBAAiB,MAAM,GAAG;QAE7D,sEAAsE;QACtE,MAAM,eAAe,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,GAAG;QAC9D,MAAM,YAAY;eAAI;SAAa,CAChC,IAAI,CAAC,CAAC,GAAG;YACR,MAAM,OAAO,EAAE,eAAe,KAAK,SAAS,WAAW,EAAE,eAAe;YACxE,MAAM,OAAO,EAAE,eAAe,KAAK,SAAS,WAAW,EAAE,eAAe;YACxE,OAAO,OAAO;QAChB,GACC,KAAK,CAAC,GAAG;QAEZ,4CAA4C;QAC5C,MAAM,UAAU,iBACb,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,KAAK,EAAE,YAAY,GAAG,GAChD,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,YAAY,GAAG,EAAE,YAAY,EAC9C,KAAK,CAAC,GAAG;QAEZ,OAAO;YACL,YAAY,eAAe,KAAK,CAAC,GAAG;YACpC;YACA;QACF;IACF,GAAG;QAAC;KAAiB;IAErB,MAAM,qBAKD,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,IAAI,EAAE,SAAS,EAAE,iBAC9C,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAW,CAAC,aAAa,EAAE,WAAW;;;;;;kCAC5C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC,QAAQ,KAAK;;;;;;0CACjE,8OAAC;gCAAE,WAAU;0CAA8B;;;;;;0CAC3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAK;4CAAS,QAAQ,YAAY,CAAC,OAAO,CAAC;;;;;;;kDAC5C,8OAAC;;4CAAK;4CAAO,QAAQ,OAAO,CAAC,cAAc;;;;;;;kDAC3C,8OAAC;wCAAK,WAAW,CAAC,2CAA2C,EAC3D,QAAQ,OAAO,KAAK,aAAa,gCACjC,QAAQ,OAAO,KAAK,WAAW,kCAC/B,QAAQ,OAAO,KAAK,YAAY,kCAChC,6BACA;kDACC,QAAQ,OAAO;;;;;;;;;;;;0CAGpB,8OAAC;gCACC,SAAS,IAAM,mBAAmB,QAAQ,MAAM;gCAChD,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAQT,IAAI,iBAAiB,MAAM,KAAK,GAAG;QACjC,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA2C;;;;;;0BAIzD,8OAAC;gBAAI,WAAU;;oBAEZ,gBAAgB,UAAU,CAAC,MAAM,GAAG,mBACnC,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAA2B;;;;;;;0CAGnD,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,UAAU,CAAC,GAAG,CAAC,CAAC,wBAC/B,8OAAC;wCAEC,SAAS;wCACT,QAAQ,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,AAAC,QAAQ,YAAY,GAAG,KAAK,GAAG,IAAI,iBAAiB,GAAG,CAAC,CAAA,IAAK,EAAE,YAAY,KAAM,KAAK,0BAA0B,CAAC;wCAC5I,MAAM,kNAAA,CAAA,aAAU;wCAChB,WAAU;uCAJL,QAAQ,MAAM;;;;;;;;;;;;;;;;oBAY5B,gBAAgB,SAAS,CAAC,MAAM,GAAG,mBAClC,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;0CAG5C,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,SAAS,CAAC,GAAG,CAAC,CAAC,wBAC9B,8OAAC;wCAEC,SAAS;wCACT,QAAO;wCACP,MAAM,kMAAA,CAAA,OAAI;wCACV,WAAU;uCAJL,QAAQ,MAAM;;;;;;;;;;;;;;;;oBAY5B,gBAAgB,OAAO,CAAC,MAAM,GAAG,mBAChC,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAA4B;;;;;;;0CAGpD,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,OAAO,CAAC,GAAG,CAAC,CAAC,wBAC5B,8OAAC;wCAEC,SAAS;wCACT,QAAO;wCACP,MAAM,kNAAA,CAAA,aAAU;wCAChB,WAAU;uCAJL,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAarC;uCAEe", "debugId": null}}, {"offset": {"line": 1912, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/OpenPub/journal-transparency/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useStore } from './store';\nimport { Journal } from './types';\nimport FilterPanel from './components/FilterPanel';\nimport ScatterPlot from './components/ScatterPlot';\nimport DataTable from './components/DataTable';\nimport Header from './components/Header';\nimport Footer from './components/Footer';\nimport SummaryStats from './components/SummaryStats';\nimport Recommendations from './components/Recommendations';\nimport LoadingSkeleton from './components/LoadingSkeleton';\n\nexport default function Home() {\n  const { setJournals, setLoading, setError, isLoading, error } = useStore();\n\n  useEffect(() => {\n    const loadData = async () => {\n      setLoading(true);\n      try {\n        const response = await fetch('/journals_mvp.json');\n        if (!response.ok) {\n          throw new Error('Failed to load journal data');\n        }\n        const data: Journal[] = await response.json();\n        setJournals(data);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'Failed to load data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadData();\n  }, [setJournals, setLoading, setError]);\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading journal data...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center text-red-600\">\n          <p className=\"text-xl font-semibold mb-2\">Error loading data</p>\n          <p>{error}</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n\n      <main className=\"container mx-auto px-4 py-6\">\n        <SummaryStats />\n        <Recommendations />\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-400px)]\">\n          {/* Filter Panel */}\n          <div className=\"lg:col-span-1\">\n            <FilterPanel />\n          </div>\n\n          {/* Visualization Area */}\n          <div className=\"lg:col-span-3 flex flex-col gap-6\">\n            {/* Scatter Plot */}\n            <div className=\"bg-white rounded-lg shadow-sm border p-4 h-1/2\">\n              <ScatterPlot />\n            </div>\n\n            {/* Data Table */}\n            <div className=\"bg-white rounded-lg shadow-sm border p-4 h-1/2\">\n              <DataTable />\n            </div>\n          </div>\n        </div>\n      </main>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;AAce,SAAS;IACtB,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,WAAQ,AAAD;IAEvE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,WAAW;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBACA,MAAM,OAAkB,MAAM,SAAS,IAAI;gBAC3C,YAAY;YACd,EAAE,OAAO,KAAK;gBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAChD,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;QAAa;QAAY;KAAS;IAEtC,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAC1C,8OAAC;kCAAG;;;;;;;;;;;;;;;;;IAIZ;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,mIAAA,CAAA,UAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;;kCACd,8OAAC,yIAAA,CAAA,UAAY;;;;;kCACb,8OAAC,4IAAA,CAAA,UAAe;;;;;kCAEhB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,wIAAA,CAAA,UAAW;;;;;;;;;;0CAId,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wIAAA,CAAA,UAAW;;;;;;;;;;kDAId,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,sIAAA,CAAA,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMlB,8OAAC,mIAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}