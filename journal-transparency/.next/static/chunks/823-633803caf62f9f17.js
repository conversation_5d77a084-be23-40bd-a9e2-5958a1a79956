(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[823],{52:(e,t,r)=>{"use strict";function n(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}r.d(t,{HY:()=>s,Qd:()=>l,Tw:()=>f,Zz:()=>c,ve:()=>d,y$:()=>u});var i="function"==typeof Symbol&&Symbol.observable||"@@observable",o=()=>Math.random().toString(36).substring(7).split("").join("."),a={INIT:`@@redux/INIT${o()}`,REPLACE:`@@redux/REPLACE${o()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${o()}`};function l(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function u(e,t,r){if("function"!=typeof e)throw Error(n(2));if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw Error(n(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw Error(n(1));return r(u)(e,t)}let o=e,s=t,c=new Map,f=c,d=0,h=!1;function p(){f===c&&(f=new Map,c.forEach((e,t)=>{f.set(t,e)}))}function g(){if(h)throw Error(n(3));return s}function y(e){if("function"!=typeof e)throw Error(n(4));if(h)throw Error(n(5));let t=!0;p();let r=d++;return f.set(r,e),function(){if(t){if(h)throw Error(n(6));t=!1,p(),f.delete(r),c=null}}}function v(e){if(!l(e))throw Error(n(7));if(void 0===e.type)throw Error(n(8));if("string"!=typeof e.type)throw Error(n(17));if(h)throw Error(n(9));try{h=!0,s=o(s,e)}finally{h=!1}return(c=f).forEach(e=>{e()}),e}return v({type:a.INIT}),{dispatch:v,subscribe:y,getState:g,replaceReducer:function(e){if("function"!=typeof e)throw Error(n(10));o=e,v({type:a.REPLACE})},[i]:function(){return{subscribe(e){if("object"!=typeof e||null===e)throw Error(n(11));function t(){e.next&&e.next(g())}return t(),{unsubscribe:y(t)}},[i](){return this}}}}}function s(e){let t,r=Object.keys(e),i={};for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof e[n]&&(i[n]=e[n])}let o=Object.keys(i);try{Object.keys(i).forEach(e=>{let t=i[e];if(void 0===t(void 0,{type:a.INIT}))throw Error(n(12));if(void 0===t(void 0,{type:a.PROBE_UNKNOWN_ACTION()}))throw Error(n(13))})}catch(e){t=e}return function(e={},r){if(t)throw t;let a=!1,l={};for(let t=0;t<o.length;t++){let u=o[t],s=i[u],c=e[u],f=s(c,r);if(void 0===f)throw r&&r.type,Error(n(14));l[u]=f,a=a||f!==c}return(a=a||o.length!==Object.keys(e).length)?l:e}}function c(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce((e,t)=>(...r)=>e(t(...r)))}function f(...e){return t=>(r,i)=>{let o=t(r,i),a=()=>{throw Error(n(15))},l={getState:o.getState,dispatch:(e,...t)=>a(e,...t)};return a=c(...e.map(e=>e(l)))(o.dispatch),{...o,dispatch:a}}}function d(e){return l(e)&&"type"in e&&"string"==typeof e.type}},177:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(5160);t.isArguments=function(e){return null!==e&&"object"==typeof e&&"[object Arguments]"===n.getTag(e)}},215:(e,t,r)=>{"use strict";r.d(t,{BZ:()=>eo,eE:()=>es,Xb:()=>ea,A2:()=>ei,yn:()=>ec,Dn:()=>P,gL:()=>W,fl:()=>Y,R4:()=>Q,Re:()=>O,n4:()=>_});var n=r(8924),i=r(2183),o=r(7238),a=r(9827),l=r(356),u=r(8478),s=r(6377),c=r(8190),f=r(6523),d=r(530),h=r(1928),p=r(841),g=r(4968),y=r(2589),v=r(6124),m=r(5146),b=r(6670),w=r(5714),x=r(4013),O=e=>{var t=(0,o.fz)(e);return"horizontal"===t?"xAxis":"vertical"===t?"yAxis":"centric"===t?"angleAxis":"radiusAxis"},S=e=>e.tooltip.settings.axisId,P=e=>{var t=O(e),r=S(e);return(0,i.Hd)(e,t,r)},M=(0,n.Mz)([P,o.fz,i.um,u.iO,O],i.sr),C=(0,n.Mz)([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],(e,t)=>[...e,...t]),E=(0,n.Mz)([O,S],i.eo),A=(0,n.Mz)([C,P,E],i.ec),j=(0,n.Mz)([A],i.rj),_=(0,n.Mz)([j,l.LF],i.Nk),k=(0,n.Mz)([_,P,A],i.fb),T=(0,n.Mz)([P],i.S5),D=(0,n.Mz)([_,A,u.eC],i.MK),I=(0,n.Mz)([D,l.LF,O],i.pM),R=(0,n.Mz)([A],i.IO),N=(0,n.Mz)([_,P,R,O],i.kz),z=(0,n.Mz)([i.Kr,O,S],i.P9),F=(0,n.Mz)([z,O],i.Oz),L=(0,n.Mz)([i.gT,O,S],i.P9),B=(0,n.Mz)([L,O],i.q),V=(0,n.Mz)([i.$X,O,S],i.P9),U=(0,n.Mz)([V,O],i.bb),H=(0,n.Mz)([F,U,B],i.yi),G=(0,n.Mz)([P,T,I,N,H],i.wL),$=(0,n.Mz)([P,o.fz,_,k,u.eC,O,G],i.tP),K=(0,n.Mz)([$,P,M],i.xp),q=(0,n.Mz)([P,$,K,O],i.g1),Z=e=>{var t=O(e),r=S(e);return(0,i.D5)(e,t,r,!1)},W=(0,n.Mz)([P,Z],c.I),Y=(0,n.Mz)([P,M,q,W],i.Qn),X=(0,n.Mz)([o.fz,k,P,O],i.tF),J=(0,n.Mz)([o.fz,k,P,O],i.iv),Q=(0,n.Mz)([o.fz,P,M,Y,Z,X,J,O],(e,t,r,n,i,o,l,u)=>{if(t){var{type:c}=t,f=(0,a._L)(e,u);if(n){var d="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,h="category"===c&&n.bandwidth?n.bandwidth()/d:0;return(h="angleAxis"===u&&null!=i&&(null==i?void 0:i.length)>=2?2*(0,s.sA)(i[0]-i[1])*h:h,f&&l)?l.map((e,t)=>({coordinate:n(e)+h,value:e,index:t,offset:h})):n.domain().map((e,t)=>({coordinate:n(e)+h,value:o?o[e]:e,index:t,offset:h}))}}}),ee=(0,n.Mz)([f.xH,f.Hw,e=>e.tooltip.settings],(e,t,r)=>(0,f.$g)(r.shared,e,t)),et=e=>e.tooltip.settings.trigger,er=e=>e.tooltip.settings.defaultIndex,en=(0,n.Mz)([w.J,ee,et,er],h.i),ei=(0,n.Mz)([en,_],p.P),eo=(0,n.Mz)([Q,ei],d.E),ea=(0,n.Mz)([en],e=>{if(e)return e.dataKey}),el=(0,n.Mz)([w.J,ee,et,er],m.q),eu=(0,n.Mz)([y.Lp,y.A$,o.fz,v.HZ,Q,er,el,b.x],g.o),es=(0,n.Mz)([en,eu],(e,t)=>null!=e&&e.coordinate?e.coordinate:t),ec=(0,n.Mz)([en],e=>e.active),ef=(0,n.Mz)([el,ei,l.LF,P,eo,b.x,ee],x.N);(0,n.Mz)([ef],e=>{if(null!=e)return Array.from(new Set(e.map(e=>e.payload).filter(e=>null!=e)))})},220:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.last=function(e){return e[e.length-1]}},241:(e,t,r)=>{e.exports=r(2434).sortBy},294:(e,t)=>{"use strict";var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,i=r?Symbol.for("react.portal"):60106,o=r?Symbol.for("react.fragment"):60107,a=r?Symbol.for("react.strict_mode"):60108,l=r?Symbol.for("react.profiler"):60114,u=r?Symbol.for("react.provider"):60109,s=r?Symbol.for("react.context"):60110,c=r?Symbol.for("react.async_mode"):60111,f=r?Symbol.for("react.concurrent_mode"):60111,d=r?Symbol.for("react.forward_ref"):60112,h=r?Symbol.for("react.suspense"):60113,p=(r&&Symbol.for("react.suspense_list"),r?Symbol.for("react.memo"):60115),g=r?Symbol.for("react.lazy"):60116;function y(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case c:case f:case o:case l:case a:case h:return e;default:switch(e=e&&e.$$typeof){case s:case d:case g:case p:case u:return e;default:return t}}case i:return t}}}r&&Symbol.for("react.block"),r&&Symbol.for("react.fundamental"),r&&Symbol.for("react.responder"),r&&Symbol.for("react.scope");t.isFragment=function(e){return y(e)===o}},330:(e,t,r)=>{"use strict";e.exports=r(294)},356:(e,t,r)=>{"use strict";r.d(t,{HS:()=>a,LF:()=>i});var n=r(8924),i=e=>e.chartData,o=(0,n.Mz)([i],e=>{var t=null!=e.chartData?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}}),a=(e,t,r,n)=>n?o(e):i(e)},379:(e,t,r)=>{"use strict";r.d(t,{J:()=>v,Z:()=>y});var n=r(2115),i=r(2596),o=r(9095),a=r(788),l=r(6377),u=r(5641),s=r(7238),c=["offset"],f=["labelRef"];function d(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function g(){return(g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var y=e=>null!=e&&"function"==typeof e;function v(e){var t,{offset:r=5}=e,h=p({offset:r},d(e,c)),{viewBox:y,position:v,value:m,children:b,content:w,className:x="",textBreakAll:O,labelRef:S}=h,P=(0,s.sk)(),M=y||P;if(!M||(0,l.uy)(m)&&(0,l.uy)(b)&&!(0,n.isValidElement)(w)&&"function"!=typeof w)return null;if((0,n.isValidElement)(w)){var{labelRef:C}=h,E=d(h,f);return(0,n.cloneElement)(w,E)}if("function"==typeof w){if(t=(0,n.createElement)(w,h),(0,n.isValidElement)(t))return t}else t=(e=>{var{value:t,formatter:r}=e,n=(0,l.uy)(e.children)?t:e.children;return"function"==typeof r?r(n):n})(h);var A="cx"in M&&(0,l.Et)(M.cx),j=(0,a.J9)(h,!0);if(A&&("insideStart"===v||"insideEnd"===v||"end"===v))return((e,t,r)=>{let o,a;var s,c,{position:f,viewBox:d,offset:h,className:p}=e,{cx:y,cy:v,innerRadius:m,outerRadius:b,startAngle:w,endAngle:x,clockWise:O}=d,S=(m+b)/2,P=(o=w,a=x,(0,l.sA)(a-o)*Math.min(Math.abs(a-o),360)),M=P>=0?1:-1;"insideStart"===f?(s=w+M*h,c=O):"insideEnd"===f?(s=x-M*h,c=!O):"end"===f&&(s=x+M*h,c=O),c=P<=0?c:!c;var C=(0,u.IZ)(y,v,S,s),E=(0,u.IZ)(y,v,S,s+(c?1:-1)*359),A="M".concat(C.x,",").concat(C.y,"\n    A").concat(S,",").concat(S,",0,1,").concat(+!c,",\n    ").concat(E.x,",").concat(E.y),j=(0,l.uy)(e.id)?(0,l.NF)("recharts-radial-line-"):e.id;return n.createElement("text",g({},r,{dominantBaseline:"central",className:(0,i.$)("recharts-radial-bar-label",p)}),n.createElement("defs",null,n.createElement("path",{id:j,d:A})),n.createElement("textPath",{xlinkHref:"#".concat(j)},t))})(h,t,j);var _=A?(e=>{var{viewBox:t,offset:r,position:n}=e,{cx:i,cy:o,innerRadius:a,outerRadius:l,startAngle:s,endAngle:c}=t,f=(s+c)/2;if("outside"===n){var{x:d,y:h}=(0,u.IZ)(i,o,l+r,f);return{x:d,y:h,textAnchor:d>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"end"};var{x:p,y:g}=(0,u.IZ)(i,o,(a+l)/2,f);return{x:p,y:g,textAnchor:"middle",verticalAnchor:"middle"}})(h):((e,t)=>{var{parentViewBox:r,offset:n,position:i}=e,{x:o,y:a,width:u,height:s}=t,c=s>=0?1:-1,f=c*n,d=c>0?"end":"start",h=c>0?"start":"end",g=u>=0?1:-1,y=g*n,v=g>0?"end":"start",m=g>0?"start":"end";if("top"===i)return p(p({},{x:o+u/2,y:a-c*n,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(a-r.y,0),width:u}:{});if("bottom"===i)return p(p({},{x:o+u/2,y:a+s+f,textAnchor:"middle",verticalAnchor:h}),r?{height:Math.max(r.y+r.height-(a+s),0),width:u}:{});if("left"===i){var b={x:o-y,y:a+s/2,textAnchor:v,verticalAnchor:"middle"};return p(p({},b),r?{width:Math.max(b.x-r.x,0),height:s}:{})}if("right"===i){var w={x:o+u+y,y:a+s/2,textAnchor:m,verticalAnchor:"middle"};return p(p({},w),r?{width:Math.max(r.x+r.width-w.x,0),height:s}:{})}var x=r?{width:u,height:s}:{};return"insideLeft"===i?p({x:o+y,y:a+s/2,textAnchor:m,verticalAnchor:"middle"},x):"insideRight"===i?p({x:o+u-y,y:a+s/2,textAnchor:v,verticalAnchor:"middle"},x):"insideTop"===i?p({x:o+u/2,y:a+f,textAnchor:"middle",verticalAnchor:h},x):"insideBottom"===i?p({x:o+u/2,y:a+s-f,textAnchor:"middle",verticalAnchor:d},x):"insideTopLeft"===i?p({x:o+y,y:a+f,textAnchor:m,verticalAnchor:h},x):"insideTopRight"===i?p({x:o+u-y,y:a+f,textAnchor:v,verticalAnchor:h},x):"insideBottomLeft"===i?p({x:o+y,y:a+s-f,textAnchor:m,verticalAnchor:d},x):"insideBottomRight"===i?p({x:o+u-y,y:a+s-f,textAnchor:v,verticalAnchor:d},x):i&&"object"==typeof i&&((0,l.Et)(i.x)||(0,l._3)(i.x))&&((0,l.Et)(i.y)||(0,l._3)(i.y))?p({x:o+(0,l.F4)(i.x,u),y:a+(0,l.F4)(i.y,s),textAnchor:"end",verticalAnchor:"end"},x):p({x:o+u/2,y:a+s/2,textAnchor:"middle",verticalAnchor:"middle"},x)})(h,M);return n.createElement(o.E,g({ref:S,className:(0,i.$)("recharts-label",x)},j,_,{breakAll:O}),t)}v.displayName="Label";var m=e=>{var{cx:t,cy:r,angle:n,startAngle:i,endAngle:o,r:a,radius:u,innerRadius:s,outerRadius:c,x:f,y:d,top:h,left:p,width:g,height:y,clockWise:v,labelViewBox:m}=e;if(m)return m;if((0,l.Et)(g)&&(0,l.Et)(y)){if((0,l.Et)(f)&&(0,l.Et)(d))return{x:f,y:d,width:g,height:y};if((0,l.Et)(h)&&(0,l.Et)(p))return{x:h,y:p,width:g,height:y}}return(0,l.Et)(f)&&(0,l.Et)(d)?{x:f,y:d,width:0,height:0}:(0,l.Et)(t)&&(0,l.Et)(r)?{cx:t,cy:r,startAngle:i||n||0,endAngle:o||n||0,innerRadius:s||0,outerRadius:c||u||a||0,clockWise:v}:e.viewBox?e.viewBox:void 0};v.parseViewBox=m,v.renderCallByParent=function(e,t){var r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&r&&!e.label)return null;var{children:i,labelRef:o}=e,u=m(e),s=(0,a.aS)(i,v).map((e,r)=>(0,n.cloneElement)(e,{viewBox:t||u,key:"label-".concat(r)}));return r?[((e,t,r)=>{if(!e)return null;var i={viewBox:t,labelRef:r};return!0===e?n.createElement(v,g({key:"label-implicit"},i)):(0,l.vh)(e)?n.createElement(v,g({key:"label-implicit",value:e},i)):(0,n.isValidElement)(e)?e.type===v?(0,n.cloneElement)(e,p({key:"label-implicit"},i)):n.createElement(v,g({key:"label-implicit",content:e},i)):y(e)?n.createElement(v,g({key:"label-implicit",content:e},i)):e&&"object"==typeof e?n.createElement(v,g({},e,{key:"label-implicit"},i)):null})(e.label,t||u,o),...s]:s}},400:(e,t,r)=>{e.exports=r(2962).throttle},512:(e,t,r)=>{e.exports=r(7547).uniqBy},530:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var n=r(6377),i=(e,t)=>{var r,i=Number(t);if(!(0,n.M8)(i)&&null!=t)return i>=0?null==e||null==(r=e[i])?void 0:r.value:void 0}},646:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},656:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8179),i=r(9279);t.isArrayLikeObject=function(e){return i.isObjectLike(e)&&n.isArrayLike(e)}},668:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isSymbol=function(e){return"symbol"==typeof e||e instanceof Symbol}},675:(e,t,r)=>{"use strict";r.d(t,{R:()=>n});var n=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]}},688:(e,t,r)=>{"use strict";r.d(t,{I:()=>V});var n=r(2115);function i(){}function o(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function a(e){this._context=e}function l(e){this._context=e}function u(e){this._context=e}a.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:o(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:o(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},l.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:o(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},u.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:o(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class s{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function c(e){this._context=e}function f(e){this._context=e}function d(e){return new f(e)}c.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e*=1,t*=1,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function h(e,t,r){var n=e._x1-e._x0,i=t-e._x1,o=(e._y1-e._y0)/(n||i<0&&-0),a=(r-e._y1)/(i||n<0&&-0);return((o<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(o),Math.abs(a),.5*Math.abs((o*i+a*n)/(n+i)))||0}function p(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function g(e,t,r){var n=e._x0,i=e._y0,o=e._x1,a=e._y1,l=(o-n)/3;e._context.bezierCurveTo(n+l,i+l*t,o-l,a-l*r,o,a)}function y(e){this._context=e}function v(e){this._context=new m(e)}function m(e){this._context=e}function b(e){this._context=e}function w(e){var t,r,n=e.length-1,i=Array(n),o=Array(n),a=Array(n);for(i[0]=0,o[0]=2,a[0]=e[0]+2*e[1],t=1;t<n-1;++t)i[t]=1,o[t]=4,a[t]=4*e[t]+2*e[t+1];for(i[n-1]=2,o[n-1]=7,a[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=i[t]/o[t-1],o[t]-=r,a[t]-=r*a[t-1];for(i[n-1]=a[n-1]/o[n-1],t=n-2;t>=0;--t)i[t]=(a[t]-i[t+1])/o[t];for(t=0,o[n-1]=(e[n]+i[n-1])/2;t<n-1;++t)o[t]=2*e[t+1]-i[t+1];return[i,o]}function x(e,t){this._context=e,this._t=t}f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},y.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:g(this,this._t0,p(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t*=1,(e*=1)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,g(this,p(this,r=h(this,e,t)),r);break;default:g(this,this._t0,r=h(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(v.prototype=Object.create(y.prototype)).point=function(e,t){y.prototype.point.call(this,t,e)},m.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,o){this._context.bezierCurveTo(t,e,n,r,o,i)}},b.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=w(e),i=w(t),o=0,a=1;a<r;++o,++a)this._context.bezierCurveTo(n[0][o],i[0][o],n[1][o],i[1][o],e[a],t[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},x.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var O=r(9819),S=r(5654),P=r(1847);function M(e){return e[0]}function C(e){return e[1]}function E(e,t){var r=(0,S.A)(!0),n=null,i=d,o=null,a=(0,P.i)(l);function l(l){var u,s,c,f=(l=(0,O.A)(l)).length,d=!1;for(null==n&&(o=i(c=a())),u=0;u<=f;++u)!(u<f&&r(s=l[u],u,l))===d&&((d=!d)?o.lineStart():o.lineEnd()),d&&o.point(+e(s,u,l),+t(s,u,l));if(c)return o=null,c+""||null}return e="function"==typeof e?e:void 0===e?M:(0,S.A)(e),t="function"==typeof t?t:void 0===t?C:(0,S.A)(t),l.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,S.A)(+t),l):e},l.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,S.A)(+e),l):t},l.defined=function(e){return arguments.length?(r="function"==typeof e?e:(0,S.A)(!!e),l):r},l.curve=function(e){return arguments.length?(i=e,null!=n&&(o=i(n)),l):i},l.context=function(e){return arguments.length?(null==e?n=o=null:o=i(n=e),l):n},l}function A(e,t,r){var n=null,i=(0,S.A)(!0),o=null,a=d,l=null,u=(0,P.i)(s);function s(s){var c,f,d,h,p,g=(s=(0,O.A)(s)).length,y=!1,v=Array(g),m=Array(g);for(null==o&&(l=a(p=u())),c=0;c<=g;++c){if(!(c<g&&i(h=s[c],c,s))===y)if(y=!y)f=c,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),d=c-1;d>=f;--d)l.point(v[d],m[d]);l.lineEnd(),l.areaEnd()}y&&(v[c]=+e(h,c,s),m[c]=+t(h,c,s),l.point(n?+n(h,c,s):v[c],r?+r(h,c,s):m[c]))}if(p)return l=null,p+""||null}function c(){return E().defined(i).curve(a).context(o)}return e="function"==typeof e?e:void 0===e?M:(0,S.A)(+e),t="function"==typeof t?t:void 0===t?(0,S.A)(0):(0,S.A)(+t),r="function"==typeof r?r:void 0===r?C:(0,S.A)(+r),s.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,S.A)(+t),n=null,s):e},s.x0=function(t){return arguments.length?(e="function"==typeof t?t:(0,S.A)(+t),s):e},s.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:(0,S.A)(+e),s):n},s.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,S.A)(+e),r=null,s):t},s.y0=function(e){return arguments.length?(t="function"==typeof e?e:(0,S.A)(+e),s):t},s.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:(0,S.A)(+e),s):r},s.lineX0=s.lineY0=function(){return c().x(e).y(t)},s.lineY1=function(){return c().x(e).y(r)},s.lineX1=function(){return c().x(n).y(t)},s.defined=function(e){return arguments.length?(i="function"==typeof e?e:(0,S.A)(!!e),s):i},s.curve=function(e){return arguments.length?(a=e,null!=o&&(l=a(o)),s):a},s.context=function(e){return arguments.length?(null==e?o=l=null:l=a(o=e),s):o},s}var j=r(2596),_=r(3597),k=r(788),T=r(6377),D=r(8892);function I(){return(I=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function R(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function N(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?R(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):R(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var z={curveBasisClosed:function(e){return new l(e)},curveBasisOpen:function(e){return new u(e)},curveBasis:function(e){return new a(e)},curveBumpX:function(e){return new s(e,!0)},curveBumpY:function(e){return new s(e,!1)},curveLinearClosed:function(e){return new c(e)},curveLinear:d,curveMonotoneX:function(e){return new y(e)},curveMonotoneY:function(e){return new v(e)},curveNatural:function(e){return new b(e)},curveStep:function(e){return new x(e,.5)},curveStepAfter:function(e){return new x(e,1)},curveStepBefore:function(e){return new x(e,0)}},F=e=>(0,D.H)(e.x)&&(0,D.H)(e.y),L=e=>e.x,B=e=>e.y,V=e=>{var{className:t,points:r,path:i,pathRef:o}=e;if((!r||!r.length)&&!i)return null;var a=r&&r.length?(e=>{var t,{type:r="linear",points:n=[],baseLine:i,layout:o,connectNulls:a=!1}=e,l=((e,t)=>{if("function"==typeof e)return e;var r="curve".concat((0,T.Zb)(e));return("curveMonotone"===r||"curveBump"===r)&&t?z["".concat(r).concat("vertical"===t?"Y":"X")]:z[r]||d})(r,o),u=a?n.filter(F):n;if(Array.isArray(i)){var s=a?i.filter(e=>F(e)):i,c=u.map((e,t)=>N(N({},e),{},{base:s[t]}));return(t="vertical"===o?A().y(B).x1(L).x0(e=>e.base.x):A().x(L).y1(B).y0(e=>e.base.y)).defined(F).curve(l),t(c)}return(t="vertical"===o&&(0,T.Et)(i)?A().y(B).x1(L).x0(i):(0,T.Et)(i)?A().x(L).y1(B).y0(i):E().x(L).y(B)).defined(F).curve(l),t(u)})(e):i;return n.createElement("path",I({},(0,k.J9)(e,!1),(0,_._U)(e),{className:(0,j.$)("recharts-curve",t),d:null===a?void 0:a,ref:o}))}},788:(e,t,r)=>{"use strict";r.d(t,{J9:()=>p,aS:()=>h});var n=r(5672),i=r.n(n),o=r(2115),a=r(330),l=r(6377),u=r(3597),s=e=>"string"==typeof e?e:e?e.displayName||e.name||"Component":"",c=null,f=null,d=e=>{if(e===c&&Array.isArray(f))return f;var t=[];return o.Children.forEach(e,e=>{(0,l.uy)(e)||((0,a.isFragment)(e)?t=t.concat(d(e.props.children)):t.push(e))}),f=t,c=e,t};function h(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(e=>s(e)):[s(t)],d(e).forEach(e=>{var t=i()(e,"type.displayName")||i()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}var p=(e,t,r)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,o.isValidElement)(e)&&(n=e.props),"object"!=typeof n&&"function"!=typeof n)return null;var i={};return Object.keys(n).forEach(e=>{var o;((e,t,r,n)=>{var i,o=null!=(i=n&&(null===u.VU||void 0===u.VU?void 0:u.VU[n]))?i:[];return t.startsWith("data-")||"function"!=typeof e&&(n&&o.includes(t)||u.QQ.includes(t))||r&&u.j2.includes(t)})(null==(o=n)?void 0:o[e],e,t,r)&&(i[e]=n[e])}),i}},841:(e,t,r)=>{"use strict";r.d(t,{P:()=>i});var n=r(8892),i=(e,t)=>{var r=null==e?void 0:e.index;if(null==r)return null;var i=Number(r);if(!(0,n.H)(i))return r;var o=Infinity;return t.length>0&&(o=t.length-1),String(Math.max(0,Math.min(i,o)))}},885:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isTypedArray=function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}},921:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(7040),i=r(4545),o=r(6200),a=r(4072);t.get=function e(t,r,l){if(null==t)return l;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return l;let o=t[r];if(void 0===o)if(i.isDeepKey(r))return e(t,a.toPath(r),l);else return l;return o}case"number":case"symbol":{"number"==typeof r&&(r=o.toKey(r));let e=t[r];if(void 0===e)return l;return e}default:{if(Array.isArray(r)){var u=t,s=r,c=l;if(0===s.length)return c;let e=u;for(let t=0;t<s.length;t++){if(null==e||n.isUnsafeProperty(s[t]))return c;e=e[s[t]]}return void 0===e?c:e}if(r=Object.is(r?.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return l;let e=t[r];if(void 0===e)return l;return e}}}},931:(e,t,r)=>{e.exports=r(6006).isPlainObject},972:(e,t,r)=>{"use strict";r.d(t,{C:()=>l,U:()=>u});var n=r(8924),i=r(6124),o=r(2589),a=r(6377),l=e=>e.brush,u=(0,n.Mz)([l,i.HZ,o.HK],(e,t,r)=>({height:e.height,x:(0,a.Et)(e.x)?e.x:t.left,y:(0,a.Et)(e.y)?e.y:t.top+t.height+t.brushBottom-((null==r?void 0:r.bottom)||0),width:(0,a.Et)(e.width)?e.width:t.width}))},1032:(e,t,r)=>{"use strict";function n(){return{accessor:(e,t)=>"function"==typeof e?{...t,accessorFn:e}:{...t,accessorKey:e},display:e=>e,group:e=>e}}function i(e,t){return"function"==typeof e?e(t):e}function o(e,t){return r=>{t.setState(t=>({...t,[e]:i(r,t[e])}))}}function a(e){return e instanceof Function}function l(e,t,r){let n,i=[];return o=>{let a,l;r.key&&r.debug&&(a=Date.now());let u=e(o);if(!(u.length!==i.length||u.some((e,t)=>i[t]!==e)))return n;if(i=u,r.key&&r.debug&&(l=Date.now()),n=t(...u),null==r||null==r.onChange||r.onChange(n),r.key&&r.debug&&null!=r&&r.debug()){let e=Math.round((Date.now()-a)*100)/100,t=Math.round((Date.now()-l)*100)/100,n=t/16,i=(e,t)=>{for(e=String(e);e.length<t;)e=" "+e;return e};console.info(`%c⏱ ${i(t,5)} /${i(e,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*n,120))}deg 100% 31%);`,null==r?void 0:r.key)}return n}}function u(e,t,r,n){return{debug:()=>{var r;return null!=(r=null==e?void 0:e.debugAll)?r:e[t]},key:!1,onChange:n}}r.d(t,{FB:()=>n,HT:()=>$,ZR:()=>G,h5:()=>K});let s="debugHeaders";function c(e,t,r){var n;let i={id:null!=(n=r.id)?n:t.id,column:t,index:r.index,isPlaceholder:!!r.isPlaceholder,placeholderId:r.placeholderId,depth:r.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{let e=[],t=r=>{r.subHeaders&&r.subHeaders.length&&r.subHeaders.map(t),e.push(r)};return t(i),e},getContext:()=>({table:e,header:i,column:t})};return e._features.forEach(t=>{null==t.createHeader||t.createHeader(i,e)}),i}function f(e,t,r,n){var i,o;let a=0,l=function(e,t){void 0===t&&(t=1),a=Math.max(a,t),e.filter(e=>e.getIsVisible()).forEach(e=>{var r;null!=(r=e.columns)&&r.length&&l(e.columns,t+1)},0)};l(e);let u=[],s=(e,t)=>{let i={depth:t,id:[n,`${t}`].filter(Boolean).join("_"),headers:[]},o=[];e.forEach(e=>{let a,l=[...o].reverse()[0],u=e.column.depth===i.depth,s=!1;if(u&&e.column.parent?a=e.column.parent:(a=e.column,s=!0),l&&(null==l?void 0:l.column)===a)l.subHeaders.push(e);else{let i=c(r,a,{id:[n,t,a.id,null==e?void 0:e.id].filter(Boolean).join("_"),isPlaceholder:s,placeholderId:s?`${o.filter(e=>e.column===a).length}`:void 0,depth:t,index:o.length});i.subHeaders.push(e),o.push(i)}i.headers.push(e),e.headerGroup=i}),u.push(i),t>0&&s(o,t-1)};s(t.map((e,t)=>c(r,e,{depth:a,index:t})),a-1),u.reverse();let f=e=>e.filter(e=>e.column.getIsVisible()).map(e=>{let t=0,r=0,n=[0];return e.subHeaders&&e.subHeaders.length?(n=[],f(e.subHeaders).forEach(e=>{let{colSpan:r,rowSpan:i}=e;t+=r,n.push(i)})):t=1,r+=Math.min(...n),e.colSpan=t,e.rowSpan=r,{colSpan:t,rowSpan:r}});return f(null!=(i=null==(o=u[0])?void 0:o.headers)?i:[]),u}let d=(e,t,r,n,i,o,a)=>{let s={id:t,index:n,original:r,depth:i,parentId:a,_valuesCache:{},_uniqueValuesCache:{},getValue:t=>{if(s._valuesCache.hasOwnProperty(t))return s._valuesCache[t];let r=e.getColumn(t);if(null!=r&&r.accessorFn)return s._valuesCache[t]=r.accessorFn(s.original,n),s._valuesCache[t]},getUniqueValues:t=>{if(s._uniqueValuesCache.hasOwnProperty(t))return s._uniqueValuesCache[t];let r=e.getColumn(t);if(null!=r&&r.accessorFn)return r.columnDef.getUniqueValues?s._uniqueValuesCache[t]=r.columnDef.getUniqueValues(s.original,n):s._uniqueValuesCache[t]=[s.getValue(t)],s._uniqueValuesCache[t]},renderValue:t=>{var r;return null!=(r=s.getValue(t))?r:e.options.renderFallbackValue},subRows:null!=o?o:[],getLeafRows:()=>(function(e,t){let r=[],n=e=>{e.forEach(e=>{r.push(e);let i=t(e);null!=i&&i.length&&n(i)})};return n(e),r})(s.subRows,e=>e.subRows),getParentRow:()=>s.parentId?e.getRow(s.parentId,!0):void 0,getParentRows:()=>{let e=[],t=s;for(;;){let r=t.getParentRow();if(!r)break;e.push(r),t=r}return e.reverse()},getAllCells:l(()=>[e.getAllLeafColumns()],t=>t.map(t=>(function(e,t,r,n){let i={id:`${t.id}_${r.id}`,row:t,column:r,getValue:()=>t.getValue(n),renderValue:()=>{var t;return null!=(t=i.getValue())?t:e.options.renderFallbackValue},getContext:l(()=>[e,r,t,i],(e,t,r,n)=>({table:e,column:t,row:r,cell:n,getValue:n.getValue,renderValue:n.renderValue}),u(e.options,"debugCells","cell.getContext"))};return e._features.forEach(n=>{null==n.createCell||n.createCell(i,r,t,e)},{}),i})(e,s,t,t.id)),u(e.options,"debugRows","getAllCells")),_getAllCellsByColumnId:l(()=>[s.getAllCells()],e=>e.reduce((e,t)=>(e[t.column.id]=t,e),{}),u(e.options,"debugRows","getAllCellsByColumnId"))};for(let t=0;t<e._features.length;t++){let r=e._features[t];null==r||null==r.createRow||r.createRow(s,e)}return s},h=(e,t,r)=>{var n,i;let o=null==r||null==(n=r.toString())?void 0:n.toLowerCase();return!!(null==(i=e.getValue(t))||null==(i=i.toString())||null==(i=i.toLowerCase())?void 0:i.includes(o))};h.autoRemove=e=>S(e);let p=(e,t,r)=>{var n;return!!(null==(n=e.getValue(t))||null==(n=n.toString())?void 0:n.includes(r))};p.autoRemove=e=>S(e);let g=(e,t,r)=>{var n;return(null==(n=e.getValue(t))||null==(n=n.toString())?void 0:n.toLowerCase())===(null==r?void 0:r.toLowerCase())};g.autoRemove=e=>S(e);let y=(e,t,r)=>{var n;return null==(n=e.getValue(t))?void 0:n.includes(r)};y.autoRemove=e=>S(e);let v=(e,t,r)=>!r.some(r=>{var n;return!(null!=(n=e.getValue(t))&&n.includes(r))});v.autoRemove=e=>S(e)||!(null!=e&&e.length);let m=(e,t,r)=>r.some(r=>{var n;return null==(n=e.getValue(t))?void 0:n.includes(r)});m.autoRemove=e=>S(e)||!(null!=e&&e.length);let b=(e,t,r)=>e.getValue(t)===r;b.autoRemove=e=>S(e);let w=(e,t,r)=>e.getValue(t)==r;w.autoRemove=e=>S(e);let x=(e,t,r)=>{let[n,i]=r,o=e.getValue(t);return o>=n&&o<=i};x.resolveFilterValue=e=>{let[t,r]=e,n="number"!=typeof t?parseFloat(t):t,i="number"!=typeof r?parseFloat(r):r,o=null===t||Number.isNaN(n)?-1/0:n,a=null===r||Number.isNaN(i)?1/0:i;if(o>a){let e=o;o=a,a=e}return[o,a]},x.autoRemove=e=>S(e)||S(e[0])&&S(e[1]);let O={includesString:h,includesStringSensitive:p,equalsString:g,arrIncludes:y,arrIncludesAll:v,arrIncludesSome:m,equals:b,weakEquals:w,inNumberRange:x};function S(e){return null==e||""===e}function P(e,t,r){return!!e&&!!e.autoRemove&&e.autoRemove(t,r)||void 0===t||"string"==typeof t&&!t}let M={sum:(e,t,r)=>r.reduce((t,r)=>{let n=r.getValue(e);return t+("number"==typeof n?n:0)},0),min:(e,t,r)=>{let n;return r.forEach(t=>{let r=t.getValue(e);null!=r&&(n>r||void 0===n&&r>=r)&&(n=r)}),n},max:(e,t,r)=>{let n;return r.forEach(t=>{let r=t.getValue(e);null!=r&&(n<r||void 0===n&&r>=r)&&(n=r)}),n},extent:(e,t,r)=>{let n,i;return r.forEach(t=>{let r=t.getValue(e);null!=r&&(void 0===n?r>=r&&(n=i=r):(n>r&&(n=r),i<r&&(i=r)))}),[n,i]},mean:(e,t)=>{let r=0,n=0;if(t.forEach(t=>{let i=t.getValue(e);null!=i&&(i*=1)>=i&&(++r,n+=i)}),r)return n/r},median:(e,t)=>{if(!t.length)return;let r=t.map(t=>t.getValue(e));if(!function(e){return Array.isArray(e)&&e.every(e=>"number"==typeof e)}(r))return;if(1===r.length)return r[0];let n=Math.floor(r.length/2),i=r.sort((e,t)=>e-t);return r.length%2!=0?i[n]:(i[n-1]+i[n])/2},unique:(e,t)=>Array.from(new Set(t.map(t=>t.getValue(e))).values()),uniqueCount:(e,t)=>new Set(t.map(t=>t.getValue(e))).size,count:(e,t)=>t.length},C=()=>({left:[],right:[]}),E={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},A=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),j=null;function _(e){return"touchstart"===e.type}function k(e,t){return t?"center"===t?e.getCenterVisibleLeafColumns():"left"===t?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}let T=()=>({pageIndex:0,pageSize:10}),D=()=>({top:[],bottom:[]}),I=(e,t,r,n,i)=>{var o;let a=i.getRow(t,!0);r?(a.getCanMultiSelect()||Object.keys(e).forEach(t=>delete e[t]),a.getCanSelect()&&(e[t]=!0)):delete e[t],n&&null!=(o=a.subRows)&&o.length&&a.getCanSelectSubRows()&&a.subRows.forEach(t=>I(e,t.id,r,n,i))};function R(e,t){let r=e.getState().rowSelection,n=[],i={},o=function(e,t){return e.map(e=>{var t;let a=N(e,r);if(a&&(n.push(e),i[e.id]=e),null!=(t=e.subRows)&&t.length&&(e={...e,subRows:o(e.subRows)}),a)return e}).filter(Boolean)};return{rows:o(t.rows),flatRows:n,rowsById:i}}function N(e,t){var r;return null!=(r=t[e.id])&&r}function z(e,t,r){var n;if(!(null!=(n=e.subRows)&&n.length))return!1;let i=!0,o=!1;return e.subRows.forEach(e=>{if((!o||i)&&(e.getCanSelect()&&(N(e,t)?o=!0:i=!1),e.subRows&&e.subRows.length)){let r=z(e,t);"all"===r?o=!0:("some"===r&&(o=!0),i=!1)}}),i?"all":!!o&&"some"}let F=/([0-9]+)/gm;function L(e,t){return e===t?0:e>t?1:-1}function B(e){return"number"==typeof e?isNaN(e)||e===1/0||e===-1/0?"":String(e):"string"==typeof e?e:""}function V(e,t){let r=e.split(F).filter(Boolean),n=t.split(F).filter(Boolean);for(;r.length&&n.length;){let e=r.shift(),t=n.shift(),i=parseInt(e,10),o=parseInt(t,10),a=[i,o].sort();if(isNaN(a[0])){if(e>t)return 1;if(t>e)return -1;continue}if(isNaN(a[1]))return isNaN(i)?-1:1;if(i>o)return 1;if(o>i)return -1}return r.length-n.length}let U={alphanumeric:(e,t,r)=>V(B(e.getValue(r)).toLowerCase(),B(t.getValue(r)).toLowerCase()),alphanumericCaseSensitive:(e,t,r)=>V(B(e.getValue(r)),B(t.getValue(r))),text:(e,t,r)=>L(B(e.getValue(r)).toLowerCase(),B(t.getValue(r)).toLowerCase()),textCaseSensitive:(e,t,r)=>L(B(e.getValue(r)),B(t.getValue(r))),datetime:(e,t,r)=>{let n=e.getValue(r),i=t.getValue(r);return n>i?1:n<i?-1:0},basic:(e,t,r)=>L(e.getValue(r),t.getValue(r))},H=[{createTable:e=>{e.getHeaderGroups=l(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,r,n,i)=>{var o,a;let l=null!=(o=null==n?void 0:n.map(e=>r.find(t=>t.id===e)).filter(Boolean))?o:[],u=null!=(a=null==i?void 0:i.map(e=>r.find(t=>t.id===e)).filter(Boolean))?a:[];return f(t,[...l,...r.filter(e=>!(null!=n&&n.includes(e.id))&&!(null!=i&&i.includes(e.id))),...u],e)},u(e.options,s,"getHeaderGroups")),e.getCenterHeaderGroups=l(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,r,n,i)=>f(t,r=r.filter(e=>!(null!=n&&n.includes(e.id))&&!(null!=i&&i.includes(e.id))),e,"center"),u(e.options,s,"getCenterHeaderGroups")),e.getLeftHeaderGroups=l(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(t,r,n)=>{var i;return f(t,null!=(i=null==n?void 0:n.map(e=>r.find(t=>t.id===e)).filter(Boolean))?i:[],e,"left")},u(e.options,s,"getLeftHeaderGroups")),e.getRightHeaderGroups=l(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(t,r,n)=>{var i;return f(t,null!=(i=null==n?void 0:n.map(e=>r.find(t=>t.id===e)).filter(Boolean))?i:[],e,"right")},u(e.options,s,"getRightHeaderGroups")),e.getFooterGroups=l(()=>[e.getHeaderGroups()],e=>[...e].reverse(),u(e.options,s,"getFooterGroups")),e.getLeftFooterGroups=l(()=>[e.getLeftHeaderGroups()],e=>[...e].reverse(),u(e.options,s,"getLeftFooterGroups")),e.getCenterFooterGroups=l(()=>[e.getCenterHeaderGroups()],e=>[...e].reverse(),u(e.options,s,"getCenterFooterGroups")),e.getRightFooterGroups=l(()=>[e.getRightHeaderGroups()],e=>[...e].reverse(),u(e.options,s,"getRightFooterGroups")),e.getFlatHeaders=l(()=>[e.getHeaderGroups()],e=>e.map(e=>e.headers).flat(),u(e.options,s,"getFlatHeaders")),e.getLeftFlatHeaders=l(()=>[e.getLeftHeaderGroups()],e=>e.map(e=>e.headers).flat(),u(e.options,s,"getLeftFlatHeaders")),e.getCenterFlatHeaders=l(()=>[e.getCenterHeaderGroups()],e=>e.map(e=>e.headers).flat(),u(e.options,s,"getCenterFlatHeaders")),e.getRightFlatHeaders=l(()=>[e.getRightHeaderGroups()],e=>e.map(e=>e.headers).flat(),u(e.options,s,"getRightFlatHeaders")),e.getCenterLeafHeaders=l(()=>[e.getCenterFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),u(e.options,s,"getCenterLeafHeaders")),e.getLeftLeafHeaders=l(()=>[e.getLeftFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),u(e.options,s,"getLeftLeafHeaders")),e.getRightLeafHeaders=l(()=>[e.getRightFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),u(e.options,s,"getRightLeafHeaders")),e.getLeafHeaders=l(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(e,t,r)=>{var n,i,o,a,l,u;return[...null!=(n=null==(i=e[0])?void 0:i.headers)?n:[],...null!=(o=null==(a=t[0])?void 0:a.headers)?o:[],...null!=(l=null==(u=r[0])?void 0:u.headers)?l:[]].map(e=>e.getLeafHeaders()).flat()},u(e.options,s,"getLeafHeaders"))}},{getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:o("columnVisibility",e)}),createColumn:(e,t)=>{e.toggleVisibility=r=>{e.getCanHide()&&t.setColumnVisibility(t=>({...t,[e.id]:null!=r?r:!e.getIsVisible()}))},e.getIsVisible=()=>{var r,n;let i=e.columns;return null==(r=i.length?i.some(e=>e.getIsVisible()):null==(n=t.getState().columnVisibility)?void 0:n[e.id])||r},e.getCanHide=()=>{var r,n;return(null==(r=e.columnDef.enableHiding)||r)&&(null==(n=t.options.enableHiding)||n)},e.getToggleVisibilityHandler=()=>t=>{null==e.toggleVisibility||e.toggleVisibility(t.target.checked)}},createRow:(e,t)=>{e._getAllVisibleCells=l(()=>[e.getAllCells(),t.getState().columnVisibility],e=>e.filter(e=>e.column.getIsVisible()),u(t.options,"debugRows","_getAllVisibleCells")),e.getVisibleCells=l(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(e,t,r)=>[...e,...t,...r],u(t.options,"debugRows","getVisibleCells"))},createTable:e=>{let t=(t,r)=>l(()=>[r(),r().filter(e=>e.getIsVisible()).map(e=>e.id).join("_")],e=>e.filter(e=>null==e.getIsVisible?void 0:e.getIsVisible()),u(e.options,"debugColumns",t));e.getVisibleFlatColumns=t("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=t("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=t("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=t("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=t("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=t=>null==e.options.onColumnVisibilityChange?void 0:e.options.onColumnVisibilityChange(t),e.resetColumnVisibility=t=>{var r;e.setColumnVisibility(t?{}:null!=(r=e.initialState.columnVisibility)?r:{})},e.toggleAllColumnsVisible=t=>{var r;t=null!=(r=t)?r:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((e,r)=>({...e,[r.id]:t||!(null!=r.getCanHide&&r.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(e=>!(null!=e.getIsVisible&&e.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(e=>null==e.getIsVisible?void 0:e.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>t=>{var r;e.toggleAllColumnsVisible(null==(r=t.target)?void 0:r.checked)}}},{getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:o("columnOrder",e)}),createColumn:(e,t)=>{e.getIndex=l(e=>[k(t,e)],t=>t.findIndex(t=>t.id===e.id),u(t.options,"debugColumns","getIndex")),e.getIsFirstColumn=r=>{var n;return(null==(n=k(t,r)[0])?void 0:n.id)===e.id},e.getIsLastColumn=r=>{var n;let i=k(t,r);return(null==(n=i[i.length-1])?void 0:n.id)===e.id}},createTable:e=>{e.setColumnOrder=t=>null==e.options.onColumnOrderChange?void 0:e.options.onColumnOrderChange(t),e.resetColumnOrder=t=>{var r;e.setColumnOrder(t?[]:null!=(r=e.initialState.columnOrder)?r:[])},e._getOrderColumnsFn=l(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(e,t,r)=>n=>{let i=[];if(null!=e&&e.length){let t=[...e],r=[...n];for(;r.length&&t.length;){let e=t.shift(),n=r.findIndex(t=>t.id===e);n>-1&&i.push(r.splice(n,1)[0])}i=[...i,...r]}else i=n;return function(e,t,r){if(!(null!=t&&t.length)||!r)return e;let n=e.filter(e=>!t.includes(e.id));return"remove"===r?n:[...t.map(t=>e.find(e=>e.id===t)).filter(Boolean),...n]}(i,t,r)},u(e.options,"debugTable","_getOrderColumnsFn"))}},{getInitialState:e=>({columnPinning:C(),...e}),getDefaultOptions:e=>({onColumnPinningChange:o("columnPinning",e)}),createColumn:(e,t)=>{e.pin=r=>{let n=e.getLeafColumns().map(e=>e.id).filter(Boolean);t.setColumnPinning(e=>{var t,i,o,a,l,u;return"right"===r?{left:(null!=(o=null==e?void 0:e.left)?o:[]).filter(e=>!(null!=n&&n.includes(e))),right:[...(null!=(a=null==e?void 0:e.right)?a:[]).filter(e=>!(null!=n&&n.includes(e))),...n]}:"left"===r?{left:[...(null!=(l=null==e?void 0:e.left)?l:[]).filter(e=>!(null!=n&&n.includes(e))),...n],right:(null!=(u=null==e?void 0:e.right)?u:[]).filter(e=>!(null!=n&&n.includes(e)))}:{left:(null!=(t=null==e?void 0:e.left)?t:[]).filter(e=>!(null!=n&&n.includes(e))),right:(null!=(i=null==e?void 0:e.right)?i:[]).filter(e=>!(null!=n&&n.includes(e)))}})},e.getCanPin=()=>e.getLeafColumns().some(e=>{var r,n,i;return(null==(r=e.columnDef.enablePinning)||r)&&(null==(n=null!=(i=t.options.enableColumnPinning)?i:t.options.enablePinning)||n)}),e.getIsPinned=()=>{let r=e.getLeafColumns().map(e=>e.id),{left:n,right:i}=t.getState().columnPinning,o=r.some(e=>null==n?void 0:n.includes(e)),a=r.some(e=>null==i?void 0:i.includes(e));return o?"left":!!a&&"right"},e.getPinnedIndex=()=>{var r,n;let i=e.getIsPinned();return i?null!=(r=null==(n=t.getState().columnPinning)||null==(n=n[i])?void 0:n.indexOf(e.id))?r:-1:0}},createRow:(e,t)=>{e.getCenterVisibleCells=l(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,t.getState().columnPinning.right],(e,t,r)=>{let n=[...null!=t?t:[],...null!=r?r:[]];return e.filter(e=>!n.includes(e.column.id))},u(t.options,"debugRows","getCenterVisibleCells")),e.getLeftVisibleCells=l(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.column.id===t)).filter(Boolean).map(e=>({...e,position:"left"})),u(t.options,"debugRows","getLeftVisibleCells")),e.getRightVisibleCells=l(()=>[e._getAllVisibleCells(),t.getState().columnPinning.right],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.column.id===t)).filter(Boolean).map(e=>({...e,position:"right"})),u(t.options,"debugRows","getRightVisibleCells"))},createTable:e=>{e.setColumnPinning=t=>null==e.options.onColumnPinningChange?void 0:e.options.onColumnPinningChange(t),e.resetColumnPinning=t=>{var r,n;return e.setColumnPinning(t?C():null!=(r=null==(n=e.initialState)?void 0:n.columnPinning)?r:C())},e.getIsSomeColumnsPinned=t=>{var r,n,i;let o=e.getState().columnPinning;return t?!!(null==(r=o[t])?void 0:r.length):!!((null==(n=o.left)?void 0:n.length)||(null==(i=o.right)?void 0:i.length))},e.getLeftLeafColumns=l(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.id===t)).filter(Boolean),u(e.options,"debugColumns","getLeftLeafColumns")),e.getRightLeafColumns=l(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.id===t)).filter(Boolean),u(e.options,"debugColumns","getRightLeafColumns")),e.getCenterLeafColumns=l(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(e,t,r)=>{let n=[...null!=t?t:[],...null!=r?r:[]];return e.filter(e=>!n.includes(e.id))},u(e.options,"debugColumns","getCenterLeafColumns"))}},{createColumn:(e,t)=>{e._getFacetedRowModel=t.options.getFacetedRowModel&&t.options.getFacetedRowModel(t,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():t.getPreFilteredRowModel(),e._getFacetedUniqueValues=t.options.getFacetedUniqueValues&&t.options.getFacetedUniqueValues(t,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=t.options.getFacetedMinMaxValues&&t.options.getFacetedMinMaxValues(t,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},{getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:o("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,t)=>{e.getAutoFilterFn=()=>{let r=t.getCoreRowModel().flatRows[0],n=null==r?void 0:r.getValue(e.id);return"string"==typeof n?O.includesString:"number"==typeof n?O.inNumberRange:"boolean"==typeof n||null!==n&&"object"==typeof n?O.equals:Array.isArray(n)?O.arrIncludes:O.weakEquals},e.getFilterFn=()=>{var r,n;return a(e.columnDef.filterFn)?e.columnDef.filterFn:"auto"===e.columnDef.filterFn?e.getAutoFilterFn():null!=(r=null==(n=t.options.filterFns)?void 0:n[e.columnDef.filterFn])?r:O[e.columnDef.filterFn]},e.getCanFilter=()=>{var r,n,i;return(null==(r=e.columnDef.enableColumnFilter)||r)&&(null==(n=t.options.enableColumnFilters)||n)&&(null==(i=t.options.enableFilters)||i)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var r;return null==(r=t.getState().columnFilters)||null==(r=r.find(t=>t.id===e.id))?void 0:r.value},e.getFilterIndex=()=>{var r,n;return null!=(r=null==(n=t.getState().columnFilters)?void 0:n.findIndex(t=>t.id===e.id))?r:-1},e.setFilterValue=r=>{t.setColumnFilters(t=>{var n,o;let a=e.getFilterFn(),l=null==t?void 0:t.find(t=>t.id===e.id),u=i(r,l?l.value:void 0);if(P(a,u,e))return null!=(n=null==t?void 0:t.filter(t=>t.id!==e.id))?n:[];let s={id:e.id,value:u};return l?null!=(o=null==t?void 0:t.map(t=>t.id===e.id?s:t))?o:[]:null!=t&&t.length?[...t,s]:[s]})}},createRow:(e,t)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=t=>{let r=e.getAllLeafColumns();null==e.options.onColumnFiltersChange||e.options.onColumnFiltersChange(e=>{var n;return null==(n=i(t,e))?void 0:n.filter(e=>{let t=r.find(t=>t.id===e.id);return!(t&&P(t.getFilterFn(),e.value,t))&&!0})})},e.resetColumnFilters=t=>{var r,n;e.setColumnFilters(t?[]:null!=(r=null==(n=e.initialState)?void 0:n.columnFilters)?r:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel)?e.getPreFilteredRowModel():e._getFilteredRowModel()}},{createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},{getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:o("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:t=>{var r;let n=null==(r=e.getCoreRowModel().flatRows[0])||null==(r=r._getAllCellsByColumnId()[t.id])?void 0:r.getValue();return"string"==typeof n||"number"==typeof n}}),createColumn:(e,t)=>{e.getCanGlobalFilter=()=>{var r,n,i,o;return(null==(r=e.columnDef.enableGlobalFilter)||r)&&(null==(n=t.options.enableGlobalFilter)||n)&&(null==(i=t.options.enableFilters)||i)&&(null==(o=null==t.options.getColumnCanGlobalFilter?void 0:t.options.getColumnCanGlobalFilter(e))||o)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>O.includesString,e.getGlobalFilterFn=()=>{var t,r;let{globalFilterFn:n}=e.options;return a(n)?n:"auto"===n?e.getGlobalAutoFilterFn():null!=(t=null==(r=e.options.filterFns)?void 0:r[n])?t:O[n]},e.setGlobalFilter=t=>{null==e.options.onGlobalFilterChange||e.options.onGlobalFilterChange(t)},e.resetGlobalFilter=t=>{e.setGlobalFilter(t?void 0:e.initialState.globalFilter)}}},{getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:o("sorting",e),isMultiSortEvent:e=>e.shiftKey}),createColumn:(e,t)=>{e.getAutoSortingFn=()=>{let r=t.getFilteredRowModel().flatRows.slice(10),n=!1;for(let t of r){let r=null==t?void 0:t.getValue(e.id);if("[object Date]"===Object.prototype.toString.call(r))return U.datetime;if("string"==typeof r&&(n=!0,r.split(F).length>1))return U.alphanumeric}return n?U.text:U.basic},e.getAutoSortDir=()=>{let r=t.getFilteredRowModel().flatRows[0];return"string"==typeof(null==r?void 0:r.getValue(e.id))?"asc":"desc"},e.getSortingFn=()=>{var r,n;if(!e)throw Error();return a(e.columnDef.sortingFn)?e.columnDef.sortingFn:"auto"===e.columnDef.sortingFn?e.getAutoSortingFn():null!=(r=null==(n=t.options.sortingFns)?void 0:n[e.columnDef.sortingFn])?r:U[e.columnDef.sortingFn]},e.toggleSorting=(r,n)=>{let i=e.getNextSortingOrder(),o=null!=r;t.setSorting(a=>{let l,u=null==a?void 0:a.find(t=>t.id===e.id),s=null==a?void 0:a.findIndex(t=>t.id===e.id),c=[],f=o?r:"desc"===i;if("toggle"!=(l=null!=a&&a.length&&e.getCanMultiSort()&&n?u?"toggle":"add":null!=a&&a.length&&s!==a.length-1?"replace":u?"toggle":"replace")||o||i||(l="remove"),"add"===l){var d;(c=[...a,{id:e.id,desc:f}]).splice(0,c.length-(null!=(d=t.options.maxMultiSortColCount)?d:Number.MAX_SAFE_INTEGER))}else c="toggle"===l?a.map(t=>t.id===e.id?{...t,desc:f}:t):"remove"===l?a.filter(t=>t.id!==e.id):[{id:e.id,desc:f}];return c})},e.getFirstSortDir=()=>{var r,n;return(null!=(r=null!=(n=e.columnDef.sortDescFirst)?n:t.options.sortDescFirst)?r:"desc"===e.getAutoSortDir())?"desc":"asc"},e.getNextSortingOrder=r=>{var n,i;let o=e.getFirstSortDir(),a=e.getIsSorted();return a?(a===o||null!=(n=t.options.enableSortingRemoval)&&!n||!!r&&null!=(i=t.options.enableMultiRemove)&&!i)&&("desc"===a?"asc":"desc"):o},e.getCanSort=()=>{var r,n;return(null==(r=e.columnDef.enableSorting)||r)&&(null==(n=t.options.enableSorting)||n)&&!!e.accessorFn},e.getCanMultiSort=()=>{var r,n;return null!=(r=null!=(n=e.columnDef.enableMultiSort)?n:t.options.enableMultiSort)?r:!!e.accessorFn},e.getIsSorted=()=>{var r;let n=null==(r=t.getState().sorting)?void 0:r.find(t=>t.id===e.id);return!!n&&(n.desc?"desc":"asc")},e.getSortIndex=()=>{var r,n;return null!=(r=null==(n=t.getState().sorting)?void 0:n.findIndex(t=>t.id===e.id))?r:-1},e.clearSorting=()=>{t.setSorting(t=>null!=t&&t.length?t.filter(t=>t.id!==e.id):[])},e.getToggleSortingHandler=()=>{let r=e.getCanSort();return n=>{r&&(null==n.persist||n.persist(),null==e.toggleSorting||e.toggleSorting(void 0,!!e.getCanMultiSort()&&(null==t.options.isMultiSortEvent?void 0:t.options.isMultiSortEvent(n))))}}},createTable:e=>{e.setSorting=t=>null==e.options.onSortingChange?void 0:e.options.onSortingChange(t),e.resetSorting=t=>{var r,n;e.setSorting(t?[]:null!=(r=null==(n=e.initialState)?void 0:n.sorting)?r:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel)?e.getPreSortedRowModel():e._getSortedRowModel()}},{getDefaultColumnDef:()=>({aggregatedCell:e=>{var t,r;return null!=(t=null==(r=e.getValue())||null==r.toString?void 0:r.toString())?t:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:o("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,t)=>{e.toggleGrouping=()=>{t.setGrouping(t=>null!=t&&t.includes(e.id)?t.filter(t=>t!==e.id):[...null!=t?t:[],e.id])},e.getCanGroup=()=>{var r,n;return(null==(r=e.columnDef.enableGrouping)||r)&&(null==(n=t.options.enableGrouping)||n)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var r;return null==(r=t.getState().grouping)?void 0:r.includes(e.id)},e.getGroupedIndex=()=>{var r;return null==(r=t.getState().grouping)?void 0:r.indexOf(e.id)},e.getToggleGroupingHandler=()=>{let t=e.getCanGroup();return()=>{t&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{let r=t.getCoreRowModel().flatRows[0],n=null==r?void 0:r.getValue(e.id);return"number"==typeof n?M.sum:"[object Date]"===Object.prototype.toString.call(n)?M.extent:void 0},e.getAggregationFn=()=>{var r,n;if(!e)throw Error();return a(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:"auto"===e.columnDef.aggregationFn?e.getAutoAggregationFn():null!=(r=null==(n=t.options.aggregationFns)?void 0:n[e.columnDef.aggregationFn])?r:M[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=t=>null==e.options.onGroupingChange?void 0:e.options.onGroupingChange(t),e.resetGrouping=t=>{var r,n;e.setGrouping(t?[]:null!=(r=null==(n=e.initialState)?void 0:n.grouping)?r:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel)?e.getPreGroupedRowModel():e._getGroupedRowModel()},createRow:(e,t)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=r=>{if(e._groupingValuesCache.hasOwnProperty(r))return e._groupingValuesCache[r];let n=t.getColumn(r);return null!=n&&n.columnDef.getGroupingValue?(e._groupingValuesCache[r]=n.columnDef.getGroupingValue(e.original),e._groupingValuesCache[r]):e.getValue(r)},e._groupingValuesCache={}},createCell:(e,t,r,n)=>{e.getIsGrouped=()=>t.getIsGrouped()&&t.id===r.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&t.getIsGrouped(),e.getIsAggregated=()=>{var t;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!(null!=(t=r.subRows)&&t.length)}}},{getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:o("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let t=!1,r=!1;e._autoResetExpanded=()=>{var n,i;if(!t)return void e._queue(()=>{t=!0});if(null!=(n=null!=(i=e.options.autoResetAll)?i:e.options.autoResetExpanded)?n:!e.options.manualExpanding){if(r)return;r=!0,e._queue(()=>{e.resetExpanded(),r=!1})}},e.setExpanded=t=>null==e.options.onExpandedChange?void 0:e.options.onExpandedChange(t),e.toggleAllRowsExpanded=t=>{(null!=t?t:!e.getIsAllRowsExpanded())?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=t=>{var r,n;e.setExpanded(t?{}:null!=(r=null==(n=e.initialState)?void 0:n.expanded)?r:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(e=>e.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>t=>{null==t.persist||t.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{let t=e.getState().expanded;return!0===t||Object.values(t).some(Boolean)},e.getIsAllRowsExpanded=()=>{let t=e.getState().expanded;return"boolean"==typeof t?!0===t:!(!Object.keys(t).length||e.getRowModel().flatRows.some(e=>!e.getIsExpanded()))},e.getExpandedDepth=()=>{let t=0;return(!0===e.getState().expanded?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(e=>{let r=e.split(".");t=Math.max(t,r.length)}),t},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel)?e.getPreExpandedRowModel():e._getExpandedRowModel()},createRow:(e,t)=>{e.toggleExpanded=r=>{t.setExpanded(n=>{var i;let o=!0===n||!!(null!=n&&n[e.id]),a={};if(!0===n?Object.keys(t.getRowModel().rowsById).forEach(e=>{a[e]=!0}):a=n,r=null!=(i=r)?i:!o,!o&&r)return{...a,[e.id]:!0};if(o&&!r){let{[e.id]:t,...r}=a;return r}return n})},e.getIsExpanded=()=>{var r;let n=t.getState().expanded;return!!(null!=(r=null==t.options.getIsRowExpanded?void 0:t.options.getIsRowExpanded(e))?r:!0===n||(null==n?void 0:n[e.id]))},e.getCanExpand=()=>{var r,n,i;return null!=(r=null==t.options.getRowCanExpand?void 0:t.options.getRowCanExpand(e))?r:(null==(n=t.options.enableExpanding)||n)&&!!(null!=(i=e.subRows)&&i.length)},e.getIsAllParentsExpanded=()=>{let r=!0,n=e;for(;r&&n.parentId;)r=(n=t.getRow(n.parentId,!0)).getIsExpanded();return r},e.getToggleExpandedHandler=()=>{let t=e.getCanExpand();return()=>{t&&e.toggleExpanded()}}}},{getInitialState:e=>({...e,pagination:{...T(),...null==e?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:o("pagination",e)}),createTable:e=>{let t=!1,r=!1;e._autoResetPageIndex=()=>{var n,i;if(!t)return void e._queue(()=>{t=!0});if(null!=(n=null!=(i=e.options.autoResetAll)?i:e.options.autoResetPageIndex)?n:!e.options.manualPagination){if(r)return;r=!0,e._queue(()=>{e.resetPageIndex(),r=!1})}},e.setPagination=t=>null==e.options.onPaginationChange?void 0:e.options.onPaginationChange(e=>i(t,e)),e.resetPagination=t=>{var r;e.setPagination(t?T():null!=(r=e.initialState.pagination)?r:T())},e.setPageIndex=t=>{e.setPagination(r=>{let n=i(t,r.pageIndex);return n=Math.max(0,Math.min(n,void 0===e.options.pageCount||-1===e.options.pageCount?Number.MAX_SAFE_INTEGER:e.options.pageCount-1)),{...r,pageIndex:n}})},e.resetPageIndex=t=>{var r,n;e.setPageIndex(t?0:null!=(r=null==(n=e.initialState)||null==(n=n.pagination)?void 0:n.pageIndex)?r:0)},e.resetPageSize=t=>{var r,n;e.setPageSize(t?10:null!=(r=null==(n=e.initialState)||null==(n=n.pagination)?void 0:n.pageSize)?r:10)},e.setPageSize=t=>{e.setPagination(e=>{let r=Math.max(1,i(t,e.pageSize)),n=Math.floor(e.pageSize*e.pageIndex/r);return{...e,pageIndex:n,pageSize:r}})},e.setPageCount=t=>e.setPagination(r=>{var n;let o=i(t,null!=(n=e.options.pageCount)?n:-1);return"number"==typeof o&&(o=Math.max(-1,o)),{...r,pageCount:o}}),e.getPageOptions=l(()=>[e.getPageCount()],e=>{let t=[];return e&&e>0&&(t=[...Array(e)].fill(null).map((e,t)=>t)),t},u(e.options,"debugTable","getPageOptions")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{let{pageIndex:t}=e.getState().pagination,r=e.getPageCount();return -1===r||0!==r&&t<r-1},e.previousPage=()=>e.setPageIndex(e=>e-1),e.nextPage=()=>e.setPageIndex(e=>e+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel)?e.getPrePaginationRowModel():e._getPaginationRowModel(),e.getPageCount=()=>{var t;return null!=(t=e.options.pageCount)?t:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var t;return null!=(t=e.options.rowCount)?t:e.getPrePaginationRowModel().rows.length}}},{getInitialState:e=>({rowPinning:D(),...e}),getDefaultOptions:e=>({onRowPinningChange:o("rowPinning",e)}),createRow:(e,t)=>{e.pin=(r,n,i)=>{let o=n?e.getLeafRows().map(e=>{let{id:t}=e;return t}):[],a=new Set([...i?e.getParentRows().map(e=>{let{id:t}=e;return t}):[],e.id,...o]);t.setRowPinning(e=>{var t,n,i,o,l,u;return"bottom"===r?{top:(null!=(i=null==e?void 0:e.top)?i:[]).filter(e=>!(null!=a&&a.has(e))),bottom:[...(null!=(o=null==e?void 0:e.bottom)?o:[]).filter(e=>!(null!=a&&a.has(e))),...Array.from(a)]}:"top"===r?{top:[...(null!=(l=null==e?void 0:e.top)?l:[]).filter(e=>!(null!=a&&a.has(e))),...Array.from(a)],bottom:(null!=(u=null==e?void 0:e.bottom)?u:[]).filter(e=>!(null!=a&&a.has(e)))}:{top:(null!=(t=null==e?void 0:e.top)?t:[]).filter(e=>!(null!=a&&a.has(e))),bottom:(null!=(n=null==e?void 0:e.bottom)?n:[]).filter(e=>!(null!=a&&a.has(e)))}})},e.getCanPin=()=>{var r;let{enableRowPinning:n,enablePinning:i}=t.options;return"function"==typeof n?n(e):null==(r=null!=n?n:i)||r},e.getIsPinned=()=>{let r=[e.id],{top:n,bottom:i}=t.getState().rowPinning,o=r.some(e=>null==n?void 0:n.includes(e)),a=r.some(e=>null==i?void 0:i.includes(e));return o?"top":!!a&&"bottom"},e.getPinnedIndex=()=>{var r,n;let i=e.getIsPinned();if(!i)return -1;let o=null==(r="top"===i?t.getTopRows():t.getBottomRows())?void 0:r.map(e=>{let{id:t}=e;return t});return null!=(n=null==o?void 0:o.indexOf(e.id))?n:-1}},createTable:e=>{e.setRowPinning=t=>null==e.options.onRowPinningChange?void 0:e.options.onRowPinningChange(t),e.resetRowPinning=t=>{var r,n;return e.setRowPinning(t?D():null!=(r=null==(n=e.initialState)?void 0:n.rowPinning)?r:D())},e.getIsSomeRowsPinned=t=>{var r,n,i;let o=e.getState().rowPinning;return t?!!(null==(r=o[t])?void 0:r.length):!!((null==(n=o.top)?void 0:n.length)||(null==(i=o.bottom)?void 0:i.length))},e._getPinnedRows=(t,r,n)=>{var i;return(null==(i=e.options.keepPinnedRows)||i?(null!=r?r:[]).map(t=>{let r=e.getRow(t,!0);return r.getIsAllParentsExpanded()?r:null}):(null!=r?r:[]).map(e=>t.find(t=>t.id===e))).filter(Boolean).map(e=>({...e,position:n}))},e.getTopRows=l(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(t,r)=>e._getPinnedRows(t,r,"top"),u(e.options,"debugRows","getTopRows")),e.getBottomRows=l(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(t,r)=>e._getPinnedRows(t,r,"bottom"),u(e.options,"debugRows","getBottomRows")),e.getCenterRows=l(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(e,t,r)=>{let n=new Set([...null!=t?t:[],...null!=r?r:[]]);return e.filter(e=>!n.has(e.id))},u(e.options,"debugRows","getCenterRows"))}},{getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:o("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=t=>null==e.options.onRowSelectionChange?void 0:e.options.onRowSelectionChange(t),e.resetRowSelection=t=>{var r;return e.setRowSelection(t?{}:null!=(r=e.initialState.rowSelection)?r:{})},e.toggleAllRowsSelected=t=>{e.setRowSelection(r=>{t=void 0!==t?t:!e.getIsAllRowsSelected();let n={...r},i=e.getPreGroupedRowModel().flatRows;return t?i.forEach(e=>{e.getCanSelect()&&(n[e.id]=!0)}):i.forEach(e=>{delete n[e.id]}),n})},e.toggleAllPageRowsSelected=t=>e.setRowSelection(r=>{let n=void 0!==t?t:!e.getIsAllPageRowsSelected(),i={...r};return e.getRowModel().rows.forEach(t=>{I(i,t.id,n,!0,e)}),i}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=l(()=>[e.getState().rowSelection,e.getCoreRowModel()],(t,r)=>Object.keys(t).length?R(e,r):{rows:[],flatRows:[],rowsById:{}},u(e.options,"debugTable","getSelectedRowModel")),e.getFilteredSelectedRowModel=l(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(t,r)=>Object.keys(t).length?R(e,r):{rows:[],flatRows:[],rowsById:{}},u(e.options,"debugTable","getFilteredSelectedRowModel")),e.getGroupedSelectedRowModel=l(()=>[e.getState().rowSelection,e.getSortedRowModel()],(t,r)=>Object.keys(t).length?R(e,r):{rows:[],flatRows:[],rowsById:{}},u(e.options,"debugTable","getGroupedSelectedRowModel")),e.getIsAllRowsSelected=()=>{let t=e.getFilteredRowModel().flatRows,{rowSelection:r}=e.getState(),n=!!(t.length&&Object.keys(r).length);return n&&t.some(e=>e.getCanSelect()&&!r[e.id])&&(n=!1),n},e.getIsAllPageRowsSelected=()=>{let t=e.getPaginationRowModel().flatRows.filter(e=>e.getCanSelect()),{rowSelection:r}=e.getState(),n=!!t.length;return n&&t.some(e=>!r[e.id])&&(n=!1),n},e.getIsSomeRowsSelected=()=>{var t;let r=Object.keys(null!=(t=e.getState().rowSelection)?t:{}).length;return r>0&&r<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{let t=e.getPaginationRowModel().flatRows;return!e.getIsAllPageRowsSelected()&&t.filter(e=>e.getCanSelect()).some(e=>e.getIsSelected()||e.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>t=>{e.toggleAllRowsSelected(t.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>t=>{e.toggleAllPageRowsSelected(t.target.checked)}},createRow:(e,t)=>{e.toggleSelected=(r,n)=>{let i=e.getIsSelected();t.setRowSelection(o=>{var a;if(r=void 0!==r?r:!i,e.getCanSelect()&&i===r)return o;let l={...o};return I(l,e.id,r,null==(a=null==n?void 0:n.selectChildren)||a,t),l})},e.getIsSelected=()=>{let{rowSelection:r}=t.getState();return N(e,r)},e.getIsSomeSelected=()=>{let{rowSelection:r}=t.getState();return"some"===z(e,r)},e.getIsAllSubRowsSelected=()=>{let{rowSelection:r}=t.getState();return"all"===z(e,r)},e.getCanSelect=()=>{var r;return"function"==typeof t.options.enableRowSelection?t.options.enableRowSelection(e):null==(r=t.options.enableRowSelection)||r},e.getCanSelectSubRows=()=>{var r;return"function"==typeof t.options.enableSubRowSelection?t.options.enableSubRowSelection(e):null==(r=t.options.enableSubRowSelection)||r},e.getCanMultiSelect=()=>{var r;return"function"==typeof t.options.enableMultiRowSelection?t.options.enableMultiRowSelection(e):null==(r=t.options.enableMultiRowSelection)||r},e.getToggleSelectedHandler=()=>{let t=e.getCanSelect();return r=>{var n;t&&e.toggleSelected(null==(n=r.target)?void 0:n.checked)}}}},{getDefaultColumnDef:()=>E,getInitialState:e=>({columnSizing:{},columnSizingInfo:A(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:o("columnSizing",e),onColumnSizingInfoChange:o("columnSizingInfo",e)}),createColumn:(e,t)=>{e.getSize=()=>{var r,n,i;let o=t.getState().columnSizing[e.id];return Math.min(Math.max(null!=(r=e.columnDef.minSize)?r:E.minSize,null!=(n=null!=o?o:e.columnDef.size)?n:E.size),null!=(i=e.columnDef.maxSize)?i:E.maxSize)},e.getStart=l(e=>[e,k(t,e),t.getState().columnSizing],(t,r)=>r.slice(0,e.getIndex(t)).reduce((e,t)=>e+t.getSize(),0),u(t.options,"debugColumns","getStart")),e.getAfter=l(e=>[e,k(t,e),t.getState().columnSizing],(t,r)=>r.slice(e.getIndex(t)+1).reduce((e,t)=>e+t.getSize(),0),u(t.options,"debugColumns","getAfter")),e.resetSize=()=>{t.setColumnSizing(t=>{let{[e.id]:r,...n}=t;return n})},e.getCanResize=()=>{var r,n;return(null==(r=e.columnDef.enableResizing)||r)&&(null==(n=t.options.enableColumnResizing)||n)},e.getIsResizing=()=>t.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,t)=>{e.getSize=()=>{let t=0,r=e=>{if(e.subHeaders.length)e.subHeaders.forEach(r);else{var n;t+=null!=(n=e.column.getSize())?n:0}};return r(e),t},e.getStart=()=>{if(e.index>0){let t=e.headerGroup.headers[e.index-1];return t.getStart()+t.getSize()}return 0},e.getResizeHandler=r=>{let n=t.getColumn(e.column.id),i=null==n?void 0:n.getCanResize();return o=>{if(!n||!i||(null==o.persist||o.persist(),_(o)&&o.touches&&o.touches.length>1))return;let a=e.getSize(),l=e?e.getLeafHeaders().map(e=>[e.column.id,e.column.getSize()]):[[n.id,n.getSize()]],u=_(o)?Math.round(o.touches[0].clientX):o.clientX,s={},c=(e,r)=>{"number"==typeof r&&(t.setColumnSizingInfo(e=>{var n,i;let o="rtl"===t.options.columnResizeDirection?-1:1,a=(r-(null!=(n=null==e?void 0:e.startOffset)?n:0))*o,l=Math.max(a/(null!=(i=null==e?void 0:e.startSize)?i:0),-.999999);return e.columnSizingStart.forEach(e=>{let[t,r]=e;s[t]=Math.round(100*Math.max(r+r*l,0))/100}),{...e,deltaOffset:a,deltaPercentage:l}}),("onChange"===t.options.columnResizeMode||"end"===e)&&t.setColumnSizing(e=>({...e,...s})))},f=e=>c("move",e),d=e=>{c("end",e),t.setColumnSizingInfo(e=>({...e,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},h=r||("undefined"!=typeof document?document:null),p={moveHandler:e=>f(e.clientX),upHandler:e=>{null==h||h.removeEventListener("mousemove",p.moveHandler),null==h||h.removeEventListener("mouseup",p.upHandler),d(e.clientX)}},g={moveHandler:e=>(e.cancelable&&(e.preventDefault(),e.stopPropagation()),f(e.touches[0].clientX),!1),upHandler:e=>{var t;null==h||h.removeEventListener("touchmove",g.moveHandler),null==h||h.removeEventListener("touchend",g.upHandler),e.cancelable&&(e.preventDefault(),e.stopPropagation()),d(null==(t=e.touches[0])?void 0:t.clientX)}},y=!!function(){if("boolean"==typeof j)return j;let e=!1;try{let t=()=>{};window.addEventListener("test",t,{get passive(){return e=!0,!1}}),window.removeEventListener("test",t)}catch(t){e=!1}return j=e}()&&{passive:!1};_(o)?(null==h||h.addEventListener("touchmove",g.moveHandler,y),null==h||h.addEventListener("touchend",g.upHandler,y)):(null==h||h.addEventListener("mousemove",p.moveHandler,y),null==h||h.addEventListener("mouseup",p.upHandler,y)),t.setColumnSizingInfo(e=>({...e,startOffset:u,startSize:a,deltaOffset:0,deltaPercentage:0,columnSizingStart:l,isResizingColumn:n.id}))}}},createTable:e=>{e.setColumnSizing=t=>null==e.options.onColumnSizingChange?void 0:e.options.onColumnSizingChange(t),e.setColumnSizingInfo=t=>null==e.options.onColumnSizingInfoChange?void 0:e.options.onColumnSizingInfoChange(t),e.resetColumnSizing=t=>{var r;e.setColumnSizing(t?{}:null!=(r=e.initialState.columnSizing)?r:{})},e.resetHeaderSizeInfo=t=>{var r;e.setColumnSizingInfo(t?A():null!=(r=e.initialState.columnSizingInfo)?r:A())},e.getTotalSize=()=>{var t,r;return null!=(t=null==(r=e.getHeaderGroups()[0])?void 0:r.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getLeftTotalSize=()=>{var t,r;return null!=(t=null==(r=e.getLeftHeaderGroups()[0])?void 0:r.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getCenterTotalSize=()=>{var t,r;return null!=(t=null==(r=e.getCenterHeaderGroups()[0])?void 0:r.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getRightTotalSize=()=>{var t,r;return null!=(t=null==(r=e.getRightHeaderGroups()[0])?void 0:r.headers.reduce((e,t)=>e+t.getSize(),0))?t:0}}}];function G(e){var t,r;let n=[...H,...null!=(t=e._features)?t:[]],o={_features:n},a=o._features.reduce((e,t)=>Object.assign(e,null==t.getDefaultOptions?void 0:t.getDefaultOptions(o)),{}),s={...null!=(r=e.initialState)?r:{}};o._features.forEach(e=>{var t;s=null!=(t=null==e.getInitialState?void 0:e.getInitialState(s))?t:s});let c=[],f=!1,d={_features:n,options:{...a,...e},initialState:s,_queue:e=>{c.push(e),f||(f=!0,Promise.resolve().then(()=>{for(;c.length;)c.shift()();f=!1}).catch(e=>setTimeout(()=>{throw e})))},reset:()=>{o.setState(o.initialState)},setOptions:e=>{var t;t=i(e,o.options),o.options=o.options.mergeOptions?o.options.mergeOptions(a,t):{...a,...t}},getState:()=>o.options.state,setState:e=>{null==o.options.onStateChange||o.options.onStateChange(e)},_getRowId:(e,t,r)=>{var n;return null!=(n=null==o.options.getRowId?void 0:o.options.getRowId(e,t,r))?n:`${r?[r.id,t].join("."):t}`},getCoreRowModel:()=>(o._getCoreRowModel||(o._getCoreRowModel=o.options.getCoreRowModel(o)),o._getCoreRowModel()),getRowModel:()=>o.getPaginationRowModel(),getRow:(e,t)=>{let r=(t?o.getPrePaginationRowModel():o.getRowModel()).rowsById[e];if(!r&&!(r=o.getCoreRowModel().rowsById[e]))throw Error();return r},_getDefaultColumnDef:l(()=>[o.options.defaultColumn],e=>{var t;return e=null!=(t=e)?t:{},{header:e=>{let t=e.header.column.columnDef;return t.accessorKey?t.accessorKey:t.accessorFn?t.id:null},cell:e=>{var t,r;return null!=(t=null==(r=e.renderValue())||null==r.toString?void 0:r.toString())?t:null},...o._features.reduce((e,t)=>Object.assign(e,null==t.getDefaultColumnDef?void 0:t.getDefaultColumnDef()),{}),...e}},u(e,"debugColumns","_getDefaultColumnDef")),_getColumnDefs:()=>o.options.columns,getAllColumns:l(()=>[o._getColumnDefs()],e=>{let t=function(e,r,n){return void 0===n&&(n=0),e.map(e=>{let i=function(e,t,r,n){var i,o;let a,s={...e._getDefaultColumnDef(),...t},c=s.accessorKey,f=null!=(i=null!=(o=s.id)?o:c?"function"==typeof String.prototype.replaceAll?c.replaceAll(".","_"):c.replace(/\./g,"_"):void 0)?i:"string"==typeof s.header?s.header:void 0;if(s.accessorFn?a=s.accessorFn:c&&(a=c.includes(".")?e=>{let t=e;for(let e of c.split(".")){var r;t=null==(r=t)?void 0:r[e]}return t}:e=>e[s.accessorKey]),!f)throw Error();let d={id:`${String(f)}`,accessorFn:a,parent:n,depth:r,columnDef:s,columns:[],getFlatColumns:l(()=>[!0],()=>{var e;return[d,...null==(e=d.columns)?void 0:e.flatMap(e=>e.getFlatColumns())]},u(e.options,"debugColumns","column.getFlatColumns")),getLeafColumns:l(()=>[e._getOrderColumnsFn()],e=>{var t;return null!=(t=d.columns)&&t.length?e(d.columns.flatMap(e=>e.getLeafColumns())):[d]},u(e.options,"debugColumns","column.getLeafColumns"))};for(let t of e._features)null==t.createColumn||t.createColumn(d,e);return d}(o,e,n,r);return i.columns=e.columns?t(e.columns,i,n+1):[],i})};return t(e)},u(e,"debugColumns","getAllColumns")),getAllFlatColumns:l(()=>[o.getAllColumns()],e=>e.flatMap(e=>e.getFlatColumns()),u(e,"debugColumns","getAllFlatColumns")),_getAllFlatColumnsById:l(()=>[o.getAllFlatColumns()],e=>e.reduce((e,t)=>(e[t.id]=t,e),{}),u(e,"debugColumns","getAllFlatColumnsById")),getAllLeafColumns:l(()=>[o.getAllColumns(),o._getOrderColumnsFn()],(e,t)=>t(e.flatMap(e=>e.getLeafColumns())),u(e,"debugColumns","getAllLeafColumns")),getColumn:e=>o._getAllFlatColumnsById()[e]};Object.assign(o,d);for(let e=0;e<o._features.length;e++){let t=o._features[e];null==t||null==t.createTable||t.createTable(o)}return o}function $(){return e=>l(()=>[e.options.data],t=>{let r={rows:[],flatRows:[],rowsById:{}},n=function(t,i,o){void 0===i&&(i=0);let a=[];for(let u=0;u<t.length;u++){let s=d(e,e._getRowId(t[u],u,o),t[u],u,i,void 0,null==o?void 0:o.id);if(r.flatRows.push(s),r.rowsById[s.id]=s,a.push(s),e.options.getSubRows){var l;s.originalSubRows=e.options.getSubRows(t[u],u),null!=(l=s.originalSubRows)&&l.length&&(s.subRows=n(s.originalSubRows,i+1,s))}}return a};return r.rows=n(t),r},u(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}function K(){return e=>l(()=>[e.getState().sorting,e.getPreSortedRowModel()],(t,r)=>{if(!r.rows.length||!(null!=t&&t.length))return r;let n=e.getState().sorting,i=[],o=n.filter(t=>{var r;return null==(r=e.getColumn(t.id))?void 0:r.getCanSort()}),a={};o.forEach(t=>{let r=e.getColumn(t.id);r&&(a[t.id]={sortUndefined:r.columnDef.sortUndefined,invertSorting:r.columnDef.invertSorting,sortingFn:r.getSortingFn()})});let l=e=>{let t=e.map(e=>({...e}));return t.sort((e,t)=>{for(let n=0;n<o.length;n+=1){var r;let i=o[n],l=a[i.id],u=l.sortUndefined,s=null!=(r=null==i?void 0:i.desc)&&r,c=0;if(u){let r=e.getValue(i.id),n=t.getValue(i.id),o=void 0===r,a=void 0===n;if(o||a){if("first"===u)return o?-1:1;if("last"===u)return o?1:-1;c=o&&a?0:o?u:-u}}if(0===c&&(c=l.sortingFn(e,t,i.id)),0!==c)return s&&(c*=-1),l.invertSorting&&(c*=-1),c}return e.index-t.index}),t.forEach(e=>{var t;i.push(e),null!=(t=e.subRows)&&t.length&&(e.subRows=l(e.subRows))}),t};return{rows:l(r.rows),flatRows:i,rowsById:r.rowsById}},u(e.options,"debugTable","getSortedRowModel",()=>e._autoResetPageIndex()))}},1147:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if(!e||"object"!=typeof e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}},1492:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]])},1551:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(668),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,o=/^\w*$/;t.isKey=function(e,t){return!Array.isArray(e)&&(!!("number"==typeof e||"boolean"==typeof e||null==e||n.isSymbol(e))||"string"==typeof e&&(o.test(e)||!i.test(e))||null!=t&&Object.hasOwn(t,e))}},1571:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(2465),i=r(2194),o=r(4804),a=r(4517);t.iteratee=function(e){if(null==e)return n.identity;switch(typeof e){case"function":return e;case"object":if(Array.isArray(e)&&2===e.length)return a.matchesProperty(e[0],e[1]);return o.matches(e);case"string":case"symbol":case"number":return i.property(e)}}},1643:(e,t,r)=>{"use strict";r.d(t,{m:()=>n});var n={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout)}},1807:(e,t,r)=>{"use strict";r.d(t,{r:()=>o});var n=r(2115),i=(0,n.createContext)(null),o=()=>null!=(0,n.useContext)(i)},1847:(e,t,r)=>{"use strict";r.d(t,{i:()=>u});let n=Math.PI,i=2*n,o=i-1e-6;function a(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class l{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?a:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return a;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,i,o){this._append`C${+e},${+t},${+r},${+n},${this._x1=+i},${this._y1=+o}`}arcTo(e,t,r,i,o){if(e*=1,t*=1,r*=1,i*=1,(o*=1)<0)throw Error(`negative radius: ${o}`);let a=this._x1,l=this._y1,u=r-e,s=i-t,c=a-e,f=l-t,d=c*c+f*f;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(d>1e-6)if(Math.abs(f*u-s*c)>1e-6&&o){let h=r-a,p=i-l,g=u*u+s*s,y=Math.sqrt(g),v=Math.sqrt(d),m=o*Math.tan((n-Math.acos((g+d-(h*h+p*p))/(2*y*v)))/2),b=m/v,w=m/y;Math.abs(b-1)>1e-6&&this._append`L${e+b*c},${t+b*f}`,this._append`A${o},${o},0,0,${+(f*h>c*p)},${this._x1=e+w*u},${this._y1=t+w*s}`}else this._append`L${this._x1=e},${this._y1=t}`}arc(e,t,r,a,l,u){if(e*=1,t*=1,r*=1,u=!!u,r<0)throw Error(`negative radius: ${r}`);let s=r*Math.cos(a),c=r*Math.sin(a),f=e+s,d=t+c,h=1^u,p=u?a-l:l-a;null===this._x1?this._append`M${f},${d}`:(Math.abs(this._x1-f)>1e-6||Math.abs(this._y1-d)>1e-6)&&this._append`L${f},${d}`,r&&(p<0&&(p=p%i+i),p>o?this._append`A${r},${r},0,1,${h},${e-s},${t-c}A${r},${r},0,1,${h},${this._x1=f},${this._y1=d}`:p>1e-6&&this._append`A${r},${r},0,${+(p>=n)},${h},${this._x1=e+r*Math.cos(l)},${this._y1=t+r*Math.sin(l)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function u(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new l(t)}l.prototype},1928:(e,t,r)=>{"use strict";r.d(t,{i:()=>a});var n=r(4890);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var a=(e,t,r,i)=>{if(null==t)return n.k_;var a=function(e,t,r){return"axis"===t?"click"===r?e.axisInteraction.click:e.axisInteraction.hover:"click"===r?e.itemInteraction.click:e.itemInteraction.hover}(e,t,r);if(null==a)return n.k_;if(a.active)return a;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&null!=e.syncInteraction.index)return e.syncInteraction;var l=!0===e.settings.active;if(null!=a.index){if(l)return o(o({},a),{},{active:!0})}else if(null!=i)return{active:!0,coordinate:void 0,dataKey:void 0,index:i};return o(o({},n.k_),{},{coordinate:a.coordinate})}},1971:(e,t,r)=>{"use strict";r.d(t,{G:()=>f,j:()=>l});var n=r(5643),i=r(2115),o=r(5064),a=e=>e,l=()=>{var e=(0,i.useContext)(o.E);return e?e.store.dispatch:a},u=()=>{},s=()=>u,c=(e,t)=>e===t;function f(e){var t=(0,i.useContext)(o.E);return(0,n.useSyncExternalStoreWithSelector)(t?t.subscription.addNestedSub:s,t?t.store.getState:u,t?t.store.getState:u,t?e:u,c)}},1992:(e,t,r)=>{"use strict";r(4993)},2071:(e,t,r)=>{"use strict";r.d(t,{h:()=>b});var n=r(2115),i=r(2596),o=r(9584),a=r(5306),l=r(1971),u=r(2183),s=r(6124),c=r(1807),f=r(379),d=["dangerouslySetInnerHTML","ticks"];function h(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function g(e){var t=(0,l.j)();return(0,n.useEffect)(()=>(t((0,a.cU)(e)),()=>{t((0,a.fR)(e))}),[e,t]),null}var y=e=>{var t,{yAxisId:r,className:h,width:g,label:y}=e,v=(0,n.useRef)(null),m=(0,n.useRef)(null),b=(0,l.G)(s.c2),w=(0,c.r)(),x=(0,l.j)(),O="yAxis",S=(0,l.G)(e=>(0,u.iV)(e,O,r,w)),P=(0,l.G)(e=>(0,u.wP)(e,r)),M=(0,l.G)(e=>(0,u.KR)(e,r)),C=(0,l.G)(e=>(0,u.Zi)(e,O,r,w));if((0,n.useLayoutEffect)(()=>{if(!("auto"!==g||!P||(0,f.Z)(y)||(0,n.isValidElement)(y))){var e,t=v.current,i=null==t||null==(e=t.tickRefs)?void 0:e.current,{tickSize:o,tickMargin:l}=t.props,u=(e=>{var{ticks:t,label:r,labelGapWithTick:n=5,tickSize:i=0,tickMargin:o=0}=e,a=0;if(t){t.forEach(e=>{if(e){var t=e.getBoundingClientRect();t.width>a&&(a=t.width)}});var l=r?r.getBoundingClientRect().width:0;return Math.round(a+(i+o)+l+(r?n:0))}return 0})({ticks:i,label:m.current,labelGapWithTick:5,tickSize:o,tickMargin:l});Math.round(P.width)!==Math.round(u)&&x((0,a.QG)({id:r,width:u}))}},[v,null==v||null==(t=v.current)||null==(t=t.tickRefs)?void 0:t.current,null==P?void 0:P.width,P,x,y,r,g]),null==P||null==M)return null;var{dangerouslySetInnerHTML:E,ticks:A}=e,j=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,d);return n.createElement(o.u,p({},j,{ref:v,labelRef:m,scale:S,x:M.x,y:M.y,width:P.width,height:P.height,className:(0,i.$)("recharts-".concat(O," ").concat(O),h),viewBox:b,ticks:C}))},v=e=>{var t,r,i,o,a;return n.createElement(n.Fragment,null,n.createElement(g,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(i=e.angle)?i:0,minTickGap:null!=(o=e.minTickGap)?o:5,tick:null==(a=e.tick)||a,tickFormatter:e.tickFormatter}),n.createElement(y,e))},m={allowDataOverflow:u.cd.allowDataOverflow,allowDecimals:u.cd.allowDecimals,allowDuplicatedCategory:u.cd.allowDuplicatedCategory,hide:!1,mirror:u.cd.mirror,orientation:u.cd.orientation,padding:u.cd.padding,reversed:u.cd.reversed,scale:u.cd.scale,tickCount:u.cd.tickCount,type:u.cd.type,width:u.cd.width,yAxisId:0};class b extends n.Component{render(){return n.createElement(v,this.props)}}h(b,"displayName","YAxis"),h(b,"defaultProps",m)},2183:(e,t,r)=>{"use strict";r.d(t,{kz:()=>ir,fb:()=>n6,q:()=>im,tP:()=>iE,g1:()=>iI,iv:()=>i4,Nk:()=>n2,pM:()=>ie,Oz:()=>iy,tF:()=>i5,rj:()=>n0,ec:()=>nY,bb:()=>iw,xp:()=>iT,wL:()=>iP,sr:()=>ij,Qn:()=>ik,MK:()=>n7,IO:()=>nJ,P9:()=>is,S5:()=>ia,PU:()=>nF,cd:()=>nB,N8:()=>nU,eo:()=>nq,yi:()=>il,ZB:()=>i8,D5:()=>iU,iV:()=>iG,Hd:()=>n$,Gx:()=>oe,_y:()=>on,um:()=>nK,gT:()=>id,Kr:()=>iu,$X:()=>ip,Zi:()=>i7,CR:()=>i9,ld:()=>nZ,L$:()=>i0,Rl:()=>nL,Lw:()=>iX,KR:()=>i1,sf:()=>nV,wP:()=>i2,Y:()=>or});var n,i,o,a,l,u,s,c={};r.r(c),r.d(c,{scaleBand:()=>x,scaleDiverging:()=>function e(){var t=e$(r3()(eE));return t.copy=function(){return r5(t,e())},g.apply(t,arguments)},scaleDivergingLog:()=>function e(){var t=eQ(r3()).domain([.1,1,10]);return t.copy=function(){return r5(t,e()).base(t.base())},g.apply(t,arguments)},scaleDivergingPow:()=>r8,scaleDivergingSqrt:()=>r7,scaleDivergingSymlog:()=>function e(){var t=e2(r3());return t.copy=function(){return r5(t,e()).constant(t.constant())},g.apply(t,arguments)},scaleIdentity:()=>function e(t){var r;function n(e){return null==e||isNaN(e*=1)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,eM),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,eM):[0,1],e$(n)},scaleImplicit:()=>b,scaleLinear:()=>function e(){var t=eD();return t.copy=function(){return ek(t,e())},p.apply(t,arguments),e$(t)},scaleLog:()=>function e(){let t=eQ(eT()).domain([1,10]);return t.copy=()=>ek(t,e()).base(t.base()),p.apply(t,arguments),t},scaleOrdinal:()=>w,scalePoint:()=>O,scalePow:()=>e8,scaleQuantile:()=>function e(){var t,r=[],n=[],i=[];function o(){var e=0,t=Math.max(1,n.length);for(i=Array(t-1);++e<t;)i[e-1]=function(e,t,r=I){if(!(!(n=e.length)||isNaN(t*=1))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,o=Math.floor(i),a=+r(e[o],o,e);return a+(r(e[o+1],o+1,e)-a)*(i-o)}}(r,e/t);return a}function a(e){return null==e||isNaN(e*=1)?t:n[N(i,e)]}return a.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?i[t-1]:r[0],t<i.length?i[t]:r[r.length-1]]},a.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t*=1)||r.push(t);return r.sort(_),o()},a.range=function(e){return arguments.length?(n=Array.from(e),o()):n.slice()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.quantiles=function(){return i.slice()},a.copy=function(){return e().domain(r).range(n).unknown(t)},p.apply(a,arguments)},scaleQuantize:()=>function e(){var t,r=0,n=1,i=1,o=[.5],a=[0,1];function l(e){return null!=e&&e<=e?a[N(o,e,0,i)]:t}function u(){var e=-1;for(o=Array(i);++e<i;)o[e]=((e+1)*n-(e-i)*r)/(i+1);return l}return l.domain=function(e){return arguments.length?([r,n]=e,r*=1,n*=1,u()):[r,n]},l.range=function(e){return arguments.length?(i=(a=Array.from(e)).length-1,u()):a.slice()},l.invertExtent=function(e){var t=a.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,o[0]]:t>=i?[o[i-1],n]:[o[t-1],o[t]]},l.unknown=function(e){return arguments.length&&(t=e),l},l.thresholds=function(){return o.slice()},l.copy=function(){return e().domain([r,n]).range(a).unknown(t)},p.apply(e$(l),arguments)},scaleRadial:()=>function e(){var t,r=eD(),n=[0,1],i=!1;function o(e){var n,o=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(o)?t:i?Math.round(o):o}return o.invert=function(e){return r.invert(e9(e))},o.domain=function(e){return arguments.length?(r.domain(e),o):r.domain()},o.range=function(e){return arguments.length?(r.range((n=Array.from(e,eM)).map(e9)),o):n.slice()},o.rangeRound=function(e){return o.range(e).round(!0)},o.round=function(e){return arguments.length?(i=!!e,o):i},o.clamp=function(e){return arguments.length?(r.clamp(e),o):r.clamp()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.copy=function(){return e(r.domain(),n).round(i).clamp(r.clamp()).unknown(t)},p.apply(o,arguments),e$(o)},scaleSequential:()=>function e(){var t=e$(r2()(eE));return t.copy=function(){return r5(t,e())},g.apply(t,arguments)},scaleSequentialLog:()=>function e(){var t=eQ(r2()).domain([1,10]);return t.copy=function(){return r5(t,e()).base(t.base())},g.apply(t,arguments)},scaleSequentialPow:()=>r6,scaleSequentialQuantile:()=>function e(){var t=[],r=eE;function n(e){if(null!=e&&!isNaN(e*=1))return r((N(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r*=1)||t.push(r);return t.sort(_),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t*=1)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n*=1)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t*=1))){if(t<=0||n<2)return tt(e);if(t>=1)return te(e);var n,i=(n-1)*t,o=Math.floor(i),a=te((function e(t,r,n=0,i=1/0,o){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(t.length-1,i)),!(n<=r&&r<=i))return t;for(o=void 0===o?tr:function(e=_){if(e===_)return tr;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(o);i>n;){if(i-n>600){let a=i-n+1,l=r-n+1,u=Math.log(a),s=.5*Math.exp(2*u/3),c=.5*Math.sqrt(u*s*(a-s)/a)*(l-a/2<0?-1:1),f=Math.max(n,Math.floor(r-l*s/a+c)),d=Math.min(i,Math.floor(r+(a-l)*s/a+c));e(t,r,f,d,o)}let a=t[r],l=n,u=i;for(tn(t,n,r),o(t[i],a)>0&&tn(t,n,i);l<u;){for(tn(t,l,u),++l,--u;0>o(t[l],a);)++l;for(;o(t[u],a)>0;)--u}0===o(t[n],a)?tn(t,n,u):tn(t,++u,i),u<=r&&(n=u+1),r<=u&&(i=u-1)}return t})(e,o).subarray(0,o+1));return a+(tt(e.subarray(o+1))-a)*(i-o)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},g.apply(n,arguments)},scaleSequentialSqrt:()=>r4,scaleSequentialSymlog:()=>function e(){var t=e2(r2());return t.copy=function(){return r5(t,e()).constant(t.constant())},g.apply(t,arguments)},scaleSqrt:()=>e7,scaleSymlog:()=>function e(){var t=e2(eT());return t.copy=function(){return ek(t,e()).constant(t.constant())},p.apply(t,arguments)},scaleThreshold:()=>function e(){var t,r=[.5],n=[0,1],i=1;function o(e){return null!=e&&e<=e?n[N(r,e,0,i)]:t}return o.domain=function(e){return arguments.length?(i=Math.min((r=Array.from(e)).length,n.length-1),o):r.slice()},o.range=function(e){return arguments.length?(n=Array.from(e),i=Math.min(r.length,n.length-1),o):n.slice()},o.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},o.unknown=function(e){return arguments.length?(t=e,o):t},o.copy=function(){return e().domain(r).range(n).unknown(t)},p.apply(o,arguments)},scaleTime:()=>r0,scaleUtc:()=>r1,tickFormat:()=>eG});var f=r(8924),d=r(3949),h=r.n(d);function p(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function g(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class y extends Map{constructor(e,t=m){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(v(this,e))}has(e){return super.has(v(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function v({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function m(e){return null!==e&&"object"==typeof e?e.valueOf():e}let b=Symbol("implicit");function w(){var e=new y,t=[],r=[],n=b;function i(i){let o=e.get(i);if(void 0===o){if(n!==b)return n;e.set(i,o=t.push(i)-1)}return r[o%r.length]}return i.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new y,r))e.has(n)||e.set(n,t.push(n)-1);return i},i.range=function(e){return arguments.length?(r=Array.from(e),i):r.slice()},i.unknown=function(e){return arguments.length?(n=e,i):n},i.copy=function(){return w(t,r).unknown(n)},p.apply(i,arguments),i}function x(){var e,t,r=w().unknown(void 0),n=r.domain,i=r.range,o=0,a=1,l=!1,u=0,s=0,c=.5;function f(){var r=n().length,f=a<o,d=f?a:o,h=f?o:a;e=(h-d)/Math.max(1,r-u+2*s),l&&(e=Math.floor(e)),d+=(h-d-e*(r-u))*c,t=e*(1-u),l&&(d=Math.round(d),t=Math.round(t));var p=(function(e,t,r){e*=1,t*=1,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((t-e)/r)),o=Array(i);++n<i;)o[n]=e+n*r;return o})(r).map(function(t){return d+e*t});return i(f?p.reverse():p)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([o,a]=e,o*=1,a*=1,f()):[o,a]},r.rangeRound=function(e){return[o,a]=e,o*=1,a*=1,l=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(l=!!e,f()):l},r.padding=function(e){return arguments.length?(u=Math.min(1,s=+e),f()):u},r.paddingInner=function(e){return arguments.length?(u=Math.min(1,e),f()):u},r.paddingOuter=function(e){return arguments.length?(s=+e,f()):s},r.align=function(e){return arguments.length?(c=Math.max(0,Math.min(1,e)),f()):c},r.copy=function(){return x(n(),[o,a]).round(l).paddingInner(u).paddingOuter(s).align(c)},p.apply(f(),arguments)}function O(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(x.apply(null,arguments).paddingInner(1))}let S=Math.sqrt(50),P=Math.sqrt(10),M=Math.sqrt(2);function C(e,t,r){let n,i,o,a=(t-e)/Math.max(0,r),l=Math.floor(Math.log10(a)),u=a/Math.pow(10,l),s=u>=S?10:u>=P?5:u>=M?2:1;return(l<0?(n=Math.round(e*(o=Math.pow(10,-l)/s)),i=Math.round(t*o),n/o<e&&++n,i/o>t&&--i,o=-o):(n=Math.round(e/(o=Math.pow(10,l)*s)),i=Math.round(t/o),n*o<e&&++n,i*o>t&&--i),i<n&&.5<=r&&r<2)?C(e,t,2*r):[n,i,o]}function E(e,t,r){if(t*=1,e*=1,!((r*=1)>0))return[];if(e===t)return[e];let n=t<e,[i,o,a]=n?C(t,e,r):C(e,t,r);if(!(o>=i))return[];let l=o-i+1,u=Array(l);if(n)if(a<0)for(let e=0;e<l;++e)u[e]=-((o-e)/a);else for(let e=0;e<l;++e)u[e]=(o-e)*a;else if(a<0)for(let e=0;e<l;++e)u[e]=-((i+e)/a);else for(let e=0;e<l;++e)u[e]=(i+e)*a;return u}function A(e,t,r){return C(e*=1,t*=1,r*=1)[2]}function j(e,t,r){t*=1,e*=1,r*=1;let n=t<e,i=n?A(t,e,r):A(e,t,r);return(n?-1:1)*(i<0?-(1/i):i)}function _(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function k(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function T(e){let t,r,n;function i(e,n,o=0,a=e.length){if(o<a){if(0!==t(n,n))return a;do{let t=o+a>>>1;0>r(e[t],n)?o=t+1:a=t}while(o<a)}return o}return 2!==e.length?(t=_,r=(t,r)=>_(e(t),r),n=(t,r)=>e(t)-r):(t=e===_||e===k?e:D,r=e,n=e),{left:i,center:function(e,t,r=0,o=e.length){let a=i(e,t,r,o-1);return a>r&&n(e[a-1],t)>-n(e[a],t)?a-1:a},right:function(e,n,i=0,o=e.length){if(i<o){if(0!==t(n,n))return o;do{let t=i+o>>>1;0>=r(e[t],n)?i=t+1:o=t}while(i<o)}return i}}}function D(){return 0}function I(e){return null===e?NaN:+e}let R=T(_),N=R.right;function z(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function F(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function L(){}R.left,T(I).center;var B="\\s*([+-]?\\d+)\\s*",V="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",U="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",H=/^#([0-9a-f]{3,8})$/,G=RegExp(`^rgb\\(${B},${B},${B}\\)$`),$=RegExp(`^rgb\\(${U},${U},${U}\\)$`),K=RegExp(`^rgba\\(${B},${B},${B},${V}\\)$`),q=RegExp(`^rgba\\(${U},${U},${U},${V}\\)$`),Z=RegExp(`^hsl\\(${V},${U},${U}\\)$`),W=RegExp(`^hsla\\(${V},${U},${U},${V}\\)$`),Y={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function X(){return this.rgb().formatHex()}function J(){return this.rgb().formatRgb()}function Q(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=H.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?ee(t):3===r?new en(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?et(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?et(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=G.exec(e))?new en(t[1],t[2],t[3],1):(t=$.exec(e))?new en(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=K.exec(e))?et(t[1],t[2],t[3],t[4]):(t=q.exec(e))?et(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=Z.exec(e))?es(t[1],t[2]/100,t[3]/100,1):(t=W.exec(e))?es(t[1],t[2]/100,t[3]/100,t[4]):Y.hasOwnProperty(e)?ee(Y[e]):"transparent"===e?new en(NaN,NaN,NaN,0):null}function ee(e){return new en(e>>16&255,e>>8&255,255&e,1)}function et(e,t,r,n){return n<=0&&(e=t=r=NaN),new en(e,t,r,n)}function er(e,t,r,n){var i;return 1==arguments.length?((i=e)instanceof L||(i=Q(i)),i)?new en((i=i.rgb()).r,i.g,i.b,i.opacity):new en:new en(e,t,r,null==n?1:n)}function en(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function ei(){return`#${eu(this.r)}${eu(this.g)}${eu(this.b)}`}function eo(){let e=ea(this.opacity);return`${1===e?"rgb(":"rgba("}${el(this.r)}, ${el(this.g)}, ${el(this.b)}${1===e?")":`, ${e})`}`}function ea(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function el(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function eu(e){return((e=el(e))<16?"0":"")+e.toString(16)}function es(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new ef(e,t,r,n)}function ec(e){if(e instanceof ef)return new ef(e.h,e.s,e.l,e.opacity);if(e instanceof L||(e=Q(e)),!e)return new ef;if(e instanceof ef)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),o=Math.max(t,r,n),a=NaN,l=o-i,u=(o+i)/2;return l?(a=t===o?(r-n)/l+(r<n)*6:r===o?(n-t)/l+2:(t-r)/l+4,l/=u<.5?o+i:2-o-i,a*=60):l=u>0&&u<1?0:a,new ef(a,l,u,e.opacity)}function ef(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function ed(e){return(e=(e||0)%360)<0?e+360:e}function eh(e){return Math.max(0,Math.min(1,e||0))}function ep(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function eg(e,t,r,n,i){var o=e*e,a=o*e;return((1-3*e+3*o-a)*t+(4-6*o+3*a)*r+(1+3*e+3*o-3*a)*n+a*i)/6}z(L,Q,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:X,formatHex:X,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return ec(this).formatHsl()},formatRgb:J,toString:J}),z(en,er,F(L,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new en(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new en(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new en(el(this.r),el(this.g),el(this.b),ea(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:ei,formatHex:ei,formatHex8:function(){return`#${eu(this.r)}${eu(this.g)}${eu(this.b)}${eu((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:eo,toString:eo})),z(ef,function(e,t,r,n){return 1==arguments.length?ec(e):new ef(e,t,r,null==n?1:n)},F(L,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new ef(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new ef(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new en(ep(e>=240?e-240:e+120,i,n),ep(e,i,n),ep(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new ef(ed(this.h),eh(this.s),eh(this.l),ea(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=ea(this.opacity);return`${1===e?"hsl(":"hsla("}${ed(this.h)}, ${100*eh(this.s)}%, ${100*eh(this.l)}%${1===e?")":`, ${e})`}`}}));let ey=e=>()=>e;function ev(e,t){var r=t-e;return r?function(t){return e+t*r}:ey(isNaN(e)?t:e)}let em=function e(t){var r,n=1==(r=+t)?ev:function(e,t){var n,i,o;return t-e?(n=e,i=t,n=Math.pow(n,o=r),i=Math.pow(i,o)-n,o=1/o,function(e){return Math.pow(n+e*i,o)}):ey(isNaN(e)?t:e)};function i(e,t){var r=n((e=er(e)).r,(t=er(t)).r),i=n(e.g,t.g),o=n(e.b,t.b),a=ev(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=i(t),e.b=o(t),e.opacity=a(t),e+""}}return i.gamma=e,i}(1);function eb(e){return function(t){var r,n,i=t.length,o=Array(i),a=Array(i),l=Array(i);for(r=0;r<i;++r)n=er(t[r]),o[r]=n.r||0,a[r]=n.g||0,l[r]=n.b||0;return o=e(o),a=e(a),l=e(l),n.opacity=1,function(e){return n.r=o(e),n.g=a(e),n.b=l(e),n+""}}}function ew(e,t){return e*=1,t*=1,function(r){return e*(1-r)+t*r}}eb(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),i=e[n],o=e[n+1],a=n>0?e[n-1]:2*i-o,l=n<t-1?e[n+2]:2*o-i;return eg((r-n/t)*t,a,i,o,l)}}),eb(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),i=e[(n+t-1)%t],o=e[n%t],a=e[(n+1)%t],l=e[(n+2)%t];return eg((r-n/t)*t,i,o,a,l)}});var ex=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,eO=RegExp(ex.source,"g");function eS(e,t){var r,n,i=typeof t;return null==t||"boolean"===i?ey(t):("number"===i?ew:"string"===i?(n=Q(t))?(t=n,em):function(e,t){var r,n,i,o,a,l=ex.lastIndex=eO.lastIndex=0,u=-1,s=[],c=[];for(e+="",t+="";(i=ex.exec(e))&&(o=eO.exec(t));)(a=o.index)>l&&(a=t.slice(l,a),s[u]?s[u]+=a:s[++u]=a),(i=i[0])===(o=o[0])?s[u]?s[u]+=o:s[++u]=o:(s[++u]=null,c.push({i:u,x:ew(i,o)})),l=eO.lastIndex;return l<t.length&&(a=t.slice(l),s[u]?s[u]+=a:s[++u]=a),s.length<2?c[0]?(r=c[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=c.length,function(e){for(var r,n=0;n<t;++n)s[(r=c[n]).i]=r.x(e);return s.join("")})}:t instanceof Q?em:t instanceof Date?function(e,t){var r=new Date;return e*=1,t*=1,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,i=e?Math.min(n,e.length):0,o=Array(i),a=Array(n);for(r=0;r<i;++r)o[r]=eS(e[r],t[r]);for(;r<n;++r)a[r]=t[r];return function(e){for(r=0;r<i;++r)a[r]=o[r](e);return a}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},i={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=eS(e[r],t[r]):i[r]=t[r];return function(e){for(r in n)i[r]=n[r](e);return i}}:ew:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,i=t.slice();return function(o){for(r=0;r<n;++r)i[r]=e[r]*(1-o)+t[r]*o;return i}})(e,t)}function eP(e,t){return e*=1,t*=1,function(r){return Math.round(e*(1-r)+t*r)}}function eM(e){return+e}var eC=[0,1];function eE(e){return e}function eA(e,t){var r;return(t-=e*=1)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function ej(e,t,r){var n=e[0],i=e[1],o=t[0],a=t[1];return i<n?(n=eA(i,n),o=r(a,o)):(n=eA(n,i),o=r(o,a)),function(e){return o(n(e))}}function e_(e,t,r){var n=Math.min(e.length,t.length)-1,i=Array(n),o=Array(n),a=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++a<n;)i[a]=eA(e[a],e[a+1]),o[a]=r(t[a],t[a+1]);return function(t){var r=N(e,t,1,n)-1;return o[r](i[r](t))}}function ek(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function eT(){var e,t,r,n,i,o,a=eC,l=eC,u=eS,s=eE;function c(){var e,t,r,u=Math.min(a.length,l.length);return s!==eE&&(e=a[0],t=a[u-1],e>t&&(r=e,e=t,t=r),s=function(r){return Math.max(e,Math.min(t,r))}),n=u>2?e_:ej,i=o=null,f}function f(t){return null==t||isNaN(t*=1)?r:(i||(i=n(a.map(e),l,u)))(e(s(t)))}return f.invert=function(r){return s(t((o||(o=n(l,a.map(e),ew)))(r)))},f.domain=function(e){return arguments.length?(a=Array.from(e,eM),c()):a.slice()},f.range=function(e){return arguments.length?(l=Array.from(e),c()):l.slice()},f.rangeRound=function(e){return l=Array.from(e),u=eP,c()},f.clamp=function(e){return arguments.length?(s=!!e||eE,c()):s!==eE},f.interpolate=function(e){return arguments.length?(u=e,c()):u},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,c()}}function eD(){return eT()(eE,eE)}var eI=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function eR(e){var t;if(!(t=eI.exec(e)))throw Error("invalid format: "+e);return new eN({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function eN(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function ez(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function eF(e){return(e=ez(Math.abs(e)))?e[1]:NaN}function eL(e,t){var r=ez(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}eR.prototype=eN.prototype,eN.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let eB={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>eL(100*e,t),r:eL,s:function(e,t){var r=ez(e,t);if(!r)return e+"";var i=r[0],o=r[1],a=o-(n=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,l=i.length;return a===l?i:a>l?i+Array(a-l+1).join("0"):a>0?i.slice(0,a)+"."+i.slice(a):"0."+Array(1-a).join("0")+ez(e,Math.max(0,t+a-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function eV(e){return e}var eU=Array.prototype.map,eH=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function eG(e,t,r,n){var i,l,u=j(e,t,r);switch((n=eR(null==n?",f":n)).type){case"s":var s=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(l=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(eF(s)/3)))-eF(Math.abs(u))))||(n.precision=l),a(n,s);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(l=Math.max(0,eF(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(i=Math.abs(i=u)))-eF(i))+1)||(n.precision=l-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(l=Math.max(0,-eF(Math.abs(u))))||(n.precision=l-("%"===n.type)*2)}return o(n)}function e$(e){var t=e.domain;return e.ticks=function(e){var r=t();return E(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return eG(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,i,o=t(),a=0,l=o.length-1,u=o[a],s=o[l],c=10;for(s<u&&(i=u,u=s,s=i,i=a,a=l,l=i);c-- >0;){if((i=A(u,s,r))===n)return o[a]=u,o[l]=s,t(o);if(i>0)u=Math.floor(u/i)*i,s=Math.ceil(s/i)*i;else if(i<0)u=Math.ceil(u*i)/i,s=Math.floor(s*i)/i;else break;n=i}return e},e}function eK(e,t){e=e.slice();var r,n=0,i=e.length-1,o=e[n],a=e[i];return a<o&&(r=n,n=i,i=r,r=o,o=a,a=r),e[n]=t.floor(o),e[i]=t.ceil(a),e}function eq(e){return Math.log(e)}function eZ(e){return Math.exp(e)}function eW(e){return-Math.log(-e)}function eY(e){return-Math.exp(-e)}function eX(e){return isFinite(e)?+("1e"+e):e<0?0:e}function eJ(e){return(t,r)=>-e(-t,r)}function eQ(e){let t,r,n=e(eq,eZ),i=n.domain,a=10;function l(){var o,l;return t=(o=a)===Math.E?Math.log:10===o&&Math.log10||2===o&&Math.log2||(o=Math.log(o),e=>Math.log(e)/o),r=10===(l=a)?eX:l===Math.E?Math.exp:e=>Math.pow(l,e),i()[0]<0?(t=eJ(t),r=eJ(r),e(eW,eY)):e(eq,eZ),n}return n.base=function(e){return arguments.length?(a=+e,l()):a},n.domain=function(e){return arguments.length?(i(e),l()):i()},n.ticks=e=>{let n,o,l=i(),u=l[0],s=l[l.length-1],c=s<u;c&&([u,s]=[s,u]);let f=t(u),d=t(s),h=null==e?10:+e,p=[];if(!(a%1)&&d-f<h){if(f=Math.floor(f),d=Math.ceil(d),u>0){for(;f<=d;++f)for(n=1;n<a;++n)if(!((o=f<0?n/r(-f):n*r(f))<u)){if(o>s)break;p.push(o)}}else for(;f<=d;++f)for(n=a-1;n>=1;--n)if(!((o=f>0?n/r(-f):n*r(f))<u)){if(o>s)break;p.push(o)}2*p.length<h&&(p=E(u,s,h))}else p=E(f,d,Math.min(d-f,h)).map(r);return c?p.reverse():p},n.tickFormat=(e,i)=>{if(null==e&&(e=10),null==i&&(i=10===a?"s":","),"function"!=typeof i&&(a%1||null!=(i=eR(i)).precision||(i.trim=!0),i=o(i)),e===1/0)return i;let l=Math.max(1,a*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*a<a-.5&&(n*=a),n<=l?i(e):""}},n.nice=()=>i(eK(i(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function e0(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function e1(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function e2(e){var t=1,r=e(e0(1),e1(t));return r.constant=function(r){return arguments.length?e(e0(t=+r),e1(t)):t},e$(r)}function e5(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function e6(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function e4(e){return e<0?-e*e:e*e}function e3(e){var t=e(eE,eE),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(eE,eE):.5===r?e(e6,e4):e(e5(r),e5(1/r)):r},e$(t)}function e8(){var e=e3(eT());return e.copy=function(){return ek(e,e8()).exponent(e.exponent())},p.apply(e,arguments),e}function e7(){return e8.apply(null,arguments).exponent(.5)}function e9(e){return Math.sign(e)*e*e}function te(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function tt(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}function tr(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:+(e>t))}function tn(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}o=(i=function(e){var t,r,i,o=void 0===e.grouping||void 0===e.thousands?eV:(t=eU.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var i=e.length,o=[],a=0,l=t[0],u=0;i>0&&l>0&&(u+l+1>n&&(l=Math.max(1,n-u)),o.push(e.substring(i-=l,i+l)),!((u+=l+1)>n));)l=t[a=(a+1)%t.length];return o.reverse().join(r)}),a=void 0===e.currency?"":e.currency[0]+"",l=void 0===e.currency?"":e.currency[1]+"",u=void 0===e.decimal?".":e.decimal+"",s=void 0===e.numerals?eV:(i=eU.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return i[+e]})}),c=void 0===e.percent?"%":e.percent+"",f=void 0===e.minus?"−":e.minus+"",d=void 0===e.nan?"NaN":e.nan+"";function h(e){var t=(e=eR(e)).fill,r=e.align,i=e.sign,h=e.symbol,p=e.zero,g=e.width,y=e.comma,v=e.precision,m=e.trim,b=e.type;"n"===b?(y=!0,b="g"):eB[b]||(void 0===v&&(v=12),m=!0,b="g"),(p||"0"===t&&"="===r)&&(p=!0,t="0",r="=");var w="$"===h?a:"#"===h&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===h?l:/[%p]/.test(b)?c:"",O=eB[b],S=/[defgprs%]/.test(b);function P(e){var a,l,c,h=w,P=x;if("c"===b)P=O(e)+P,e="";else{var M=(e*=1)<0||1/e<0;if(e=isNaN(e)?d:O(Math.abs(e),v),m&&(e=function(e){e:for(var t,r=e.length,n=1,i=-1;n<r;++n)switch(e[n]){case".":i=t=n;break;case"0":0===i&&(i=n),t=n;break;default:if(!+e[n])break e;i>0&&(i=0)}return i>0?e.slice(0,i)+e.slice(t+1):e}(e)),M&&0==+e&&"+"!==i&&(M=!1),h=(M?"("===i?i:f:"-"===i||"("===i?"":i)+h,P=("s"===b?eH[8+n/3]:"")+P+(M&&"("===i?")":""),S){for(a=-1,l=e.length;++a<l;)if(48>(c=e.charCodeAt(a))||c>57){P=(46===c?u+e.slice(a+1):e.slice(a))+P,e=e.slice(0,a);break}}}y&&!p&&(e=o(e,1/0));var C=h.length+e.length+P.length,E=C<g?Array(g-C+1).join(t):"";switch(y&&p&&(e=o(E+e,E.length?g-P.length:1/0),E=""),r){case"<":e=h+e+P+E;break;case"=":e=h+E+e+P;break;case"^":e=E.slice(0,C=E.length>>1)+h+e+P+E.slice(C);break;default:e=E+h+e+P}return s(e)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),P.toString=function(){return e+""},P}return{format:h,formatPrefix:function(e,t){var r=h(((e=eR(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(eF(t)/3))),i=Math.pow(10,-n),o=eH[8+n/3];return function(e){return r(i*e)+o}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,a=i.formatPrefix;let ti=new Date,to=new Date;function ta(e,t,r,n){function i(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return i.floor=t=>(e(t=new Date(+t)),t),i.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),i.round=e=>{let t=i(e),r=i.ceil(e);return e-t<r-e?t:r},i.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),i.range=(r,n,o)=>{let a,l=[];if(r=i.ceil(r),o=null==o?1:Math.floor(o),!(r<n)||!(o>0))return l;do l.push(a=new Date(+r)),t(r,o),e(r);while(a<r&&r<n);return l},i.filter=r=>ta(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e)if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}),r&&(i.count=(t,n)=>(ti.setTime(+t),to.setTime(+n),e(ti),e(to),Math.floor(r(ti,to))),i.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?i.filter(n?t=>n(t)%e==0:t=>i.count(0,t)%e==0):i:null),i}let tl=ta(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);tl.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?ta(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):tl:null,tl.range;let tu=ta(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());tu.range;let ts=ta(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());ts.range;let tc=ta(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());tc.range;let tf=ta(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());tf.range;let td=ta(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());td.range;let th=ta(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);th.range;let tp=ta(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);tp.range;let tg=ta(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function ty(e){return ta(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}tg.range;let tv=ty(0),tm=ty(1),tb=ty(2),tw=ty(3),tx=ty(4),tO=ty(5),tS=ty(6);function tP(e){return ta(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}tv.range,tm.range,tb.range,tw.range,tx.range,tO.range,tS.range;let tM=tP(0),tC=tP(1),tE=tP(2),tA=tP(3),tj=tP(4),t_=tP(5),tk=tP(6);tM.range,tC.range,tE.range,tA.range,tj.range,t_.range,tk.range;let tT=ta(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());tT.range;let tD=ta(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());tD.range;let tI=ta(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());tI.every=e=>isFinite(e=Math.floor(e))&&e>0?ta(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,tI.range;let tR=ta(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function tN(e,t,r,n,i,o){let a=[[tu,1,1e3],[tu,5,5e3],[tu,15,15e3],[tu,30,3e4],[o,1,6e4],[o,5,3e5],[o,15,9e5],[o,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function l(t,r,n){let i=Math.abs(r-t)/n,o=T(([,,e])=>e).right(a,i);if(o===a.length)return e.every(j(t/31536e6,r/31536e6,n));if(0===o)return tl.every(Math.max(j(t,r,n),1));let[l,u]=a[i/a[o-1][2]<a[o][2]/i?o-1:o];return l.every(u)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let i=r&&"function"==typeof r.range?r:l(e,t,r),o=i?i.range(e,+t+1):[];return n?o.reverse():o},l]}tR.every=e=>isFinite(e=Math.floor(e))&&e>0?ta(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,tR.range;let[tz,tF]=tN(tR,tD,tM,tg,td,tc),[tL,tB]=tN(tI,tT,tv,th,tf,ts);function tV(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function tU(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function tH(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var tG={"-":"",_:" ",0:"0"},t$=/^\s*\d+/,tK=/^%/,tq=/[\\^$*+?|[\]().{}]/g;function tZ(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",o=i.length;return n+(o<r?Array(r-o+1).join(t)+i:i)}function tW(e){return e.replace(tq,"\\$&")}function tY(e){return RegExp("^(?:"+e.map(tW).join("|")+")","i")}function tX(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function tJ(e,t,r){var n=t$.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function tQ(e,t,r){var n=t$.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function t0(e,t,r){var n=t$.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function t1(e,t,r){var n=t$.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function t2(e,t,r){var n=t$.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function t5(e,t,r){var n=t$.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function t6(e,t,r){var n=t$.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function t4(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function t3(e,t,r){var n=t$.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function t8(e,t,r){var n=t$.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function t7(e,t,r){var n=t$.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function t9(e,t,r){var n=t$.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function re(e,t,r){var n=t$.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function rt(e,t,r){var n=t$.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function rr(e,t,r){var n=t$.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function rn(e,t,r){var n=t$.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function ri(e,t,r){var n=t$.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function ro(e,t,r){var n=tK.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function ra(e,t,r){var n=t$.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function rl(e,t,r){var n=t$.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function ru(e,t){return tZ(e.getDate(),t,2)}function rs(e,t){return tZ(e.getHours(),t,2)}function rc(e,t){return tZ(e.getHours()%12||12,t,2)}function rf(e,t){return tZ(1+th.count(tI(e),e),t,3)}function rd(e,t){return tZ(e.getMilliseconds(),t,3)}function rh(e,t){return rd(e,t)+"000"}function rp(e,t){return tZ(e.getMonth()+1,t,2)}function rg(e,t){return tZ(e.getMinutes(),t,2)}function ry(e,t){return tZ(e.getSeconds(),t,2)}function rv(e){var t=e.getDay();return 0===t?7:t}function rm(e,t){return tZ(tv.count(tI(e)-1,e),t,2)}function rb(e){var t=e.getDay();return t>=4||0===t?tx(e):tx.ceil(e)}function rw(e,t){return e=rb(e),tZ(tx.count(tI(e),e)+(4===tI(e).getDay()),t,2)}function rx(e){return e.getDay()}function rO(e,t){return tZ(tm.count(tI(e)-1,e),t,2)}function rS(e,t){return tZ(e.getFullYear()%100,t,2)}function rP(e,t){return tZ((e=rb(e)).getFullYear()%100,t,2)}function rM(e,t){return tZ(e.getFullYear()%1e4,t,4)}function rC(e,t){var r=e.getDay();return tZ((e=r>=4||0===r?tx(e):tx.ceil(e)).getFullYear()%1e4,t,4)}function rE(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+tZ(t/60|0,"0",2)+tZ(t%60,"0",2)}function rA(e,t){return tZ(e.getUTCDate(),t,2)}function rj(e,t){return tZ(e.getUTCHours(),t,2)}function r_(e,t){return tZ(e.getUTCHours()%12||12,t,2)}function rk(e,t){return tZ(1+tp.count(tR(e),e),t,3)}function rT(e,t){return tZ(e.getUTCMilliseconds(),t,3)}function rD(e,t){return rT(e,t)+"000"}function rI(e,t){return tZ(e.getUTCMonth()+1,t,2)}function rR(e,t){return tZ(e.getUTCMinutes(),t,2)}function rN(e,t){return tZ(e.getUTCSeconds(),t,2)}function rz(e){var t=e.getUTCDay();return 0===t?7:t}function rF(e,t){return tZ(tM.count(tR(e)-1,e),t,2)}function rL(e){var t=e.getUTCDay();return t>=4||0===t?tj(e):tj.ceil(e)}function rB(e,t){return e=rL(e),tZ(tj.count(tR(e),e)+(4===tR(e).getUTCDay()),t,2)}function rV(e){return e.getUTCDay()}function rU(e,t){return tZ(tC.count(tR(e)-1,e),t,2)}function rH(e,t){return tZ(e.getUTCFullYear()%100,t,2)}function rG(e,t){return tZ((e=rL(e)).getUTCFullYear()%100,t,2)}function r$(e,t){return tZ(e.getUTCFullYear()%1e4,t,4)}function rK(e,t){var r=e.getUTCDay();return tZ((e=r>=4||0===r?tj(e):tj.ceil(e)).getUTCFullYear()%1e4,t,4)}function rq(){return"+0000"}function rZ(){return"%"}function rW(e){return+e}function rY(e){return Math.floor(e/1e3)}function rX(e){return new Date(e)}function rJ(e){return e instanceof Date?+e:+new Date(+e)}function rQ(e,t,r,n,i,o,a,l,u,s){var c=eD(),f=c.invert,d=c.domain,h=s(".%L"),p=s(":%S"),g=s("%I:%M"),y=s("%I %p"),v=s("%a %d"),m=s("%b %d"),b=s("%B"),w=s("%Y");function x(e){return(u(e)<e?h:l(e)<e?p:a(e)<e?g:o(e)<e?y:n(e)<e?i(e)<e?v:m:r(e)<e?b:w)(e)}return c.invert=function(e){return new Date(f(e))},c.domain=function(e){return arguments.length?d(Array.from(e,rJ)):d().map(rX)},c.ticks=function(t){var r=d();return e(r[0],r[r.length-1],null==t?10:t)},c.tickFormat=function(e,t){return null==t?x:s(t)},c.nice=function(e){var r=d();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?d(eK(r,e)):c},c.copy=function(){return ek(c,rQ(e,t,r,n,i,o,a,l,u,s))},c}function r0(){return p.apply(rQ(tL,tB,tI,tT,tv,th,tf,ts,tu,u).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function r1(){return p.apply(rQ(tz,tF,tR,tD,tM,tp,td,tc,tu,s).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function r2(){var e,t,r,n,i,o=0,a=1,l=eE,u=!1;function s(t){return null==t||isNaN(t*=1)?i:l(0===r?.5:(t=(n(t)-e)*r,u?Math.max(0,Math.min(1,t)):t))}function c(e){return function(t){var r,n;return arguments.length?([r,n]=t,l=e(r,n),s):[l(0),l(1)]}}return s.domain=function(i){return arguments.length?([o,a]=i,e=n(o*=1),t=n(a*=1),r=e===t?0:1/(t-e),s):[o,a]},s.clamp=function(e){return arguments.length?(u=!!e,s):u},s.interpolator=function(e){return arguments.length?(l=e,s):l},s.range=c(eS),s.rangeRound=c(eP),s.unknown=function(e){return arguments.length?(i=e,s):i},function(i){return n=i,e=i(o),t=i(a),r=e===t?0:1/(t-e),s}}function r5(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function r6(){var e=e3(r2());return e.copy=function(){return r5(e,r6()).exponent(e.exponent())},g.apply(e,arguments)}function r4(){return r6.apply(null,arguments).exponent(.5)}function r3(){var e,t,r,n,i,o,a,l=0,u=.5,s=1,c=1,f=eE,d=!1;function h(e){return isNaN(e*=1)?a:(e=.5+((e=+o(e))-t)*(c*e<c*t?n:i),f(d?Math.max(0,Math.min(1,e)):e))}function p(e){return function(t){var r,n,i;return arguments.length?([r,n,i]=t,f=function(e,t){void 0===t&&(t=e,e=eS);for(var r=0,n=t.length-1,i=t[0],o=Array(n<0?0:n);r<n;)o[r]=e(i,i=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return o[t](e-t)}}(e,[r,n,i]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([l,u,s]=a,e=o(l*=1),t=o(u*=1),r=o(s*=1),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),c=t<e?-1:1,h):[l,u,s]},h.clamp=function(e){return arguments.length?(d=!!e,h):d},h.interpolator=function(e){return arguments.length?(f=e,h):f},h.range=p(eS),h.rangeRound=p(eP),h.unknown=function(e){return arguments.length?(a=e,h):a},function(a){return o=a,e=a(l),t=a(u),r=a(s),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),c=t<e?-1:1,h}}function r8(){var e=e3(r3());return e.copy=function(){return r5(e,r8()).exponent(e.exponent())},g.apply(e,arguments)}function r7(){return r8.apply(null,arguments).exponent(.5)}u=(l=function(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,o=e.days,a=e.shortDays,l=e.months,u=e.shortMonths,s=tY(i),c=tX(i),f=tY(o),d=tX(o),h=tY(a),p=tX(a),g=tY(l),y=tX(l),v=tY(u),m=tX(u),b={a:function(e){return a[e.getDay()]},A:function(e){return o[e.getDay()]},b:function(e){return u[e.getMonth()]},B:function(e){return l[e.getMonth()]},c:null,d:ru,e:ru,f:rh,g:rP,G:rC,H:rs,I:rc,j:rf,L:rd,m:rp,M:rg,p:function(e){return i[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:rW,s:rY,S:ry,u:rv,U:rm,V:rw,w:rx,W:rO,x:null,X:null,y:rS,Y:rM,Z:rE,"%":rZ},w={a:function(e){return a[e.getUTCDay()]},A:function(e){return o[e.getUTCDay()]},b:function(e){return u[e.getUTCMonth()]},B:function(e){return l[e.getUTCMonth()]},c:null,d:rA,e:rA,f:rD,g:rG,G:rK,H:rj,I:r_,j:rk,L:rT,m:rI,M:rR,p:function(e){return i[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:rW,s:rY,S:rN,u:rz,U:rF,V:rB,w:rV,W:rU,x:null,X:null,y:rH,Y:r$,Z:rq,"%":rZ},x={a:function(e,t,r){var n=h.exec(t.slice(r));return n?(e.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=v.exec(t.slice(r));return n?(e.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=g.exec(t.slice(r));return n?(e.m=y.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return P(e,t,r,n)},d:t7,e:t7,f:ri,g:t6,G:t5,H:re,I:re,j:t9,L:rn,m:t8,M:rt,p:function(e,t,r){var n=s.exec(t.slice(r));return n?(e.p=c.get(n[0].toLowerCase()),r+n[0].length):-1},q:t3,Q:ra,s:rl,S:rr,u:tQ,U:t0,V:t1,w:tJ,W:t2,x:function(e,t,n){return P(e,r,t,n)},X:function(e,t,r){return P(e,n,t,r)},y:t6,Y:t5,Z:t4,"%":ro};function O(e,t){return function(r){var n,i,o,a=[],l=-1,u=0,s=e.length;for(r instanceof Date||(r=new Date(+r));++l<s;)37===e.charCodeAt(l)&&(a.push(e.slice(u,l)),null!=(i=tG[n=e.charAt(++l)])?n=e.charAt(++l):i="e"===n?" ":"0",(o=t[n])&&(n=o(r,i)),a.push(n),u=l+1);return a.push(e.slice(u,l)),a.join("")}}function S(e,t){return function(r){var n,i,o=tH(1900,void 0,1);if(P(o,e,r+="",0)!=r.length)return null;if("Q"in o)return new Date(o.Q);if("s"in o)return new Date(1e3*o.s+("L"in o?o.L:0));if(!t||"Z"in o||(o.Z=0),"p"in o&&(o.H=o.H%12+12*o.p),void 0===o.m&&(o.m="q"in o?o.q:0),"V"in o){if(o.V<1||o.V>53)return null;"w"in o||(o.w=1),"Z"in o?(n=(i=(n=tU(tH(o.y,0,1))).getUTCDay())>4||0===i?tC.ceil(n):tC(n),n=tp.offset(n,(o.V-1)*7),o.y=n.getUTCFullYear(),o.m=n.getUTCMonth(),o.d=n.getUTCDate()+(o.w+6)%7):(n=(i=(n=tV(tH(o.y,0,1))).getDay())>4||0===i?tm.ceil(n):tm(n),n=th.offset(n,(o.V-1)*7),o.y=n.getFullYear(),o.m=n.getMonth(),o.d=n.getDate()+(o.w+6)%7)}else("W"in o||"U"in o)&&("w"in o||(o.w="u"in o?o.u%7:+("W"in o)),i="Z"in o?tU(tH(o.y,0,1)).getUTCDay():tV(tH(o.y,0,1)).getDay(),o.m=0,o.d="W"in o?(o.w+6)%7+7*o.W-(i+5)%7:o.w+7*o.U-(i+6)%7);return"Z"in o?(o.H+=o.Z/100|0,o.M+=o.Z%100,tU(o)):tV(o)}}function P(e,t,r,n){for(var i,o,a=0,l=t.length,u=r.length;a<l;){if(n>=u)return -1;if(37===(i=t.charCodeAt(a++))){if(!(o=x[(i=t.charAt(a++))in tG?t.charAt(a++):i])||(n=o(e,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(t,b),w.x=O(r,w),w.X=O(n,w),w.c=O(t,w),{format:function(e){var t=O(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=S(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",w);return t.toString=function(){return e},t},utcParse:function(e){var t=S(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,l.parse,s=l.utcFormat,l.utcParse;var r9=r(7238),ne=r(9827),nt=r(356),nr=r(6377),nn=r(8892);function ni(e){if(Array.isArray(e)&&2===e.length){var[t,r]=e;if((0,nn.H)(t)&&(0,nn.H)(r))return!0}return!1}function no(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}var na=r(8870),nl=r.n(na),nu=e=>e,ns={},nc=e=>function t(){let r;return 0==arguments.length||1==arguments.length&&(r=arguments.length<=0?void 0:arguments[0],r===ns)?t:e(...arguments)},nf=(e,t)=>1===e?t:nc(function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var o=n.filter(e=>e!==ns).length;return o>=e?t(...n):nf(e-o,nc(function(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];return t(...n.map(e=>e===ns?r.shift():e),...r)}))}),nd=e=>nf(e.length,e),nh=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},np=nd((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map(e=>t[e]).map(e)),ng=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return nu;var n=t.reverse(),i=n[0],o=n.slice(1);return function(){return o.reduce((e,t)=>t(e),i(...arguments))}},ny=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),nv=e=>{var t=null,r=null;return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return t&&i.every((e,r)=>{var n;return e===(null==(n=t)?void 0:n[r])})?r:(t=i,r=e(...i))}};function nm(e){return 0===e?1:Math.floor(new(nl())(e).abs().log(10).toNumber())+1}function nb(e,t,r){for(var n=new(nl())(e),i=0,o=[];n.lt(t)&&i<1e5;)o.push(n.toNumber()),n=n.add(r),i++;return o}nd((e,t,r)=>{var n=+e;return n+r*(t-n)}),nd((e,t,r)=>{var n=t-e;return(r-e)/(n=n||1/0)}),nd((e,t,r)=>{var n=t-e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});var nw=e=>{var[t,r]=e,[n,i]=[t,r];return t>r&&([n,i]=[r,t]),[n,i]},nx=(e,t,r)=>{if(e.lte(0))return new(nl())(0);var n=nm(e.toNumber()),i=new(nl())(10).pow(n),o=e.div(i),a=1!==n?.05:.1,l=new(nl())(Math.ceil(o.div(a).toNumber())).add(r).mul(a).mul(i);return new(nl())(t?l.toNumber():Math.ceil(l.toNumber()))},nO=function(e,t,r,n){var i,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new(nl())(0),tickMin:new(nl())(0),tickMax:new(nl())(0)};var a=nx(new(nl())(t).sub(e).div(r-1),n,o),l=Math.ceil((i=e<=0&&t>=0?new(nl())(0):(i=new(nl())(e).add(t).div(2)).sub(new(nl())(i).mod(a))).sub(e).div(a).toNumber()),u=Math.ceil(new(nl())(t).sub(i).div(a).toNumber()),s=l+u+1;return s>r?nO(e,t,r,n,o+1):(s<r&&(u=t>0?u+(r-s):u,l=t>0?l:l+(r-s)),{step:a,tickMin:i.sub(new(nl())(l).mul(a)),tickMax:i.add(new(nl())(u).mul(a))})},nS=nv(function(e){var[t,r]=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],o=Math.max(n,2),[a,l]=nw([t,r]);if(a===-1/0||l===1/0){var u=l===1/0?[a,...nh(0,n-1).map(()=>1/0)]:[...nh(0,n-1).map(()=>-1/0),l];return t>r?ny(u):u}if(a===l){var s=new(nl())(1),c=new(nl())(a);if(!c.isint()&&i){var f=Math.abs(a);f<1?(s=new(nl())(10).pow(nm(a)-1),c=new(nl())(Math.floor(c.div(s).toNumber())).mul(s)):f>1&&(c=new(nl())(Math.floor(a)))}else 0===a?c=new(nl())(Math.floor((n-1)/2)):i||(c=new(nl())(Math.floor(a)));var d=Math.floor((n-1)/2);return ng(np(e=>c.add(new(nl())(e-d).mul(s)).toNumber()),nh)(0,n)}var{step:h,tickMin:p,tickMax:g}=nO(a,l,o,i,0),y=nb(p,g.add(new(nl())(.1).mul(h)),h);return t>r?ny(y):y}),nP=nv(function(e,t){var[r,n]=e,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[o,a]=nw([r,n]);if(o===-1/0||a===1/0)return[r,n];if(o===a)return[o];var l=Math.max(t,2),u=nx(new(nl())(a).sub(o).div(l-1),i,0),s=[...nb(new(nl())(o),new(nl())(a),u),a];return!1===i&&(s=s.map(e=>Math.round(e))),r>n?ny(s):s}),nM=r(2589),nC=r(6908),nE=r(6124),nA=r(972),nj=r(8478),n_=r(7062),nk=(e,t)=>t,nT=(e,t,r)=>r,nD=r(8190),nI=r(4421);function nR(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nN(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nR(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nR(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var nz=[0,"auto"],nF={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},nL=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return null==r?nF:r},nB={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:nz,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:nI.tQ},nV=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return null==r?nB:r},nU={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},nH=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return null==r?nU:r},nG=(e,t,r)=>{switch(t){case"xAxis":return nL(e,r);case"yAxis":return nV(e,r);case"zAxis":return nH(e,r);case"angleAxis":return(0,n_.Be)(e,r);case"radiusAxis":return(0,n_.Gl)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},n$=(e,t,r)=>{switch(t){case"xAxis":return nL(e,r);case"yAxis":return nV(e,r);case"angleAxis":return(0,n_.Be)(e,r);case"radiusAxis":return(0,n_.Gl)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},nK=e=>e.graphicalItems.countOfBars>0;function nq(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var nZ=e=>e.graphicalItems.cartesianItems,nW=(0,f.Mz)([nk,nT],nq),nY=(e,t,r)=>e.filter(r).filter(e=>(null==t?void 0:t.includeHidden)===!0||!e.hide),nX=(0,f.Mz)([nZ,nG,nW],nY),nJ=e=>e.filter(e=>void 0===e.stackId),nQ=(0,f.Mz)([nX],nJ),n0=e=>e.map(e=>e.data).filter(Boolean).flat(1),n1=(0,f.Mz)([nX],n0),n2=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=t;return e.length>0?e:r.slice(n,i+1)},n5=(0,f.Mz)([n1,nt.HS],n2),n6=(e,t,r)=>(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,ne.kr)(e,t.dataKey)})):r.length>0?r.map(e=>e.dataKey).flatMap(t=>e.map(e=>({value:(0,ne.kr)(e,t)}))):e.map(e=>({value:e})),n4=(0,f.Mz)([n5,nG,nX],n6);function n3(e,t){switch(e){case"xAxis":return"x"===t.direction;case"yAxis":return"y"===t.direction;default:return!1}}function n8(e){return e.filter(e=>(0,nr.vh)(e)||e instanceof Date).map(Number).filter(e=>!1===(0,nr.M8)(e))}var n7=(e,t,r)=>Object.fromEntries(Object.entries(t.reduce((e,t)=>(null==t.stackId||(null==e[t.stackId]&&(e[t.stackId]=[]),e[t.stackId].push(t)),e),{})).map(t=>{var[n,i]=t,o=i.map(e=>e.dataKey);return[n,{stackedData:(0,ne.yy)(e,o,r),graphicalItems:i}]})),n9=(0,f.Mz)([n5,nX,nj.eC],n7),ie=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:i}=t;if("zAxis"!==r){var o=(0,ne.Mk)(e,n,i);if(null==o||0!==o[0]||0!==o[1])return o}},it=(0,f.Mz)([n9,nt.LF,nk],ie),ir=(e,t,r,n)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var i,o,a=null==(i=r.errorBars)?void 0:i.filter(e=>n3(n,e)),l=(0,ne.kr)(e,null!=(o=t.dataKey)?o:r.dataKey);return{value:l,errorDomain:function(e,t,r){return!r||"number"!=typeof t||(0,nr.M8)(t)||!r.length?[]:n8(r.flatMap(r=>{var n,i,o=(0,ne.kr)(e,r.dataKey);if(Array.isArray(o)?[n,i]=o:n=i=o,(0,nn.H)(n)&&(0,nn.H)(i))return[t-n,t+i]}))}(e,l,a)}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,ne.kr)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]})),ii=(0,f.Mz)(n5,nG,nQ,nk,ir);function io(e){var{value:t}=e;if((0,nr.vh)(t)||t instanceof Date)return t}var ia=e=>{var t;if(null==e||!("domain"in e))return nz;if(null!=e.domain)return e.domain;if(null!=e.ticks){if("number"===e.type){var r=n8(e.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===e.type)return e.ticks.map(String)}return null!=(t=null==e?void 0:e.domain)?t:nz},il=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.filter(Boolean);if(0!==n.length){var i=n.flat();return[Math.min(...i),Math.max(...i)]}},iu=e=>e.referenceElements.dots,is=(e,t,r)=>e.filter(e=>"extendDomain"===e.ifOverflow).filter(e=>"xAxis"===t?e.xAxisId===r:e.yAxisId===r),ic=(0,f.Mz)([iu,nk,nT],is),id=e=>e.referenceElements.areas,ih=(0,f.Mz)([id,nk,nT],is),ip=e=>e.referenceElements.lines,ig=(0,f.Mz)([ip,nk,nT],is),iy=(e,t)=>{var r=n8(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},iv=(0,f.Mz)(ic,nk,iy),im=(e,t)=>{var r=n8(e.flatMap(e=>["xAxis"===t?e.x1:e.y1,"xAxis"===t?e.x2:e.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},ib=(0,f.Mz)([ih,nk],im),iw=(e,t)=>{var r=n8(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},ix=(0,f.Mz)(ig,nk,iw),iO=(0,f.Mz)(iv,ix,ib,(e,t,r)=>il(e,r,t)),iS=(0,f.Mz)([nG],ia),iP=(e,t,r,n,i)=>{var o=function(e,t){if(t&&"function"!=typeof e&&Array.isArray(e)&&2===e.length){var r,n,[i,o]=e;if((0,nn.H)(i))r=i;else if("function"==typeof i)return;if((0,nn.H)(o))n=o;else if("function"==typeof o)return;var a=[r,n];if(ni(a))return a}}(t,e.allowDataOverflow);return null!=o?o:function(e,t,r){if(r||null!=t){if("function"==typeof e&&null!=t)try{var n=e(t,r);if(ni(n))return no(n,t,r)}catch(e){}if(Array.isArray(e)&&2===e.length){var i,o,[a,l]=e;if("auto"===a)null!=t&&(i=Math.min(...t));else if((0,nr.Et)(a))i=a;else if("function"==typeof a)try{null!=t&&(i=a(null==t?void 0:t[0]))}catch(e){}else if("string"==typeof a&&ne.IH.test(a)){var u=ne.IH.exec(a);if(null==u||null==t)i=void 0;else{var s=+u[1];i=t[0]-s}}else i=null==t?void 0:t[0];if("auto"===l)null!=t&&(o=Math.max(...t));else if((0,nr.Et)(l))o=l;else if("function"==typeof l)try{null!=t&&(o=l(null==t?void 0:t[1]))}catch(e){}else if("string"==typeof l&&ne.qx.test(l)){var c=ne.qx.exec(l);if(null==c||null==t)o=void 0;else{var f=+c[1];o=t[1]+f}}else o=null==t?void 0:t[1];var d=[i,o];if(ni(d))return null==t?d:no(d,t,r)}}}(t,il(r,i,(e=>{var t=n8(e.flatMap(e=>[e.value,e.errorDomain]).flat(1));if(0!==t.length)return[Math.min(...t),Math.max(...t)]})(n)),e.allowDataOverflow)},iM=(0,f.Mz)([nG,iS,it,ii,iO],iP),iC=[0,1],iE=(e,t,r,n,i,o,a)=>{if(null!=e&&null!=r&&0!==r.length){var{dataKey:l,type:u}=e,s=(0,ne._L)(t,o);return s&&null==l?h()(0,r.length):"category"===u?((e,t,r)=>{var n=e.map(io).filter(e=>null!=e);return r&&(null==t.dataKey||t.allowDuplicatedCategory&&(0,nr.CG)(n))?h()(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))})(n,e,s):"expand"===i?iC:a}},iA=(0,f.Mz)([nG,r9.fz,n5,n4,nj.eC,nk,iM],iE),ij=(e,t,r,n,i)=>{if(null!=e){var{scale:o,type:a}=e;if("auto"===o)return"radial"===t&&"radiusAxis"===i?"band":"radial"===t&&"angleAxis"===i?"linear":"category"===a&&n&&(n.indexOf("LineChart")>=0||n.indexOf("AreaChart")>=0||n.indexOf("ComposedChart")>=0&&!r)?"point":"category"===a?"band":"linear";if("string"==typeof o){var l="scale".concat((0,nr.Zb)(o));return l in c?l:"point"}}},i_=(0,f.Mz)([nG,r9.fz,nK,nj.iO,nk],ij);function ik(e,t,r,n){if(null!=r&&null!=n){if("function"==typeof e.scale)return e.scale.copy().domain(r).range(n);var i=function(e){if(null!=e){if(e in c)return c[e]();var t="scale".concat((0,nr.Zb)(e));if(t in c)return c[t]()}}(t);if(null!=i){var o=i.domain(r).range(n);return(0,ne.YB)(o),o}}}var iT=(e,t,r)=>{var n=ia(t);if("auto"===r||"linear"===r){if(null!=t&&t.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&ni(e))return nS(e,t.tickCount,t.allowDecimals);if(null!=t&&t.tickCount&&"number"===t.type&&ni(e))return nP(e,t.tickCount,t.allowDecimals)}},iD=(0,f.Mz)([iA,n$,i_],iT),iI=(e,t,r,n)=>"angleAxis"!==n&&(null==e?void 0:e.type)==="number"&&ni(t)&&Array.isArray(r)&&r.length>0?[Math.min(t[0],r[0]),Math.max(t[1],r[r.length-1])]:t,iR=(0,f.Mz)([nG,iA,iD,nk],iI),iN=(0,f.Mz)(n4,nG,(e,t)=>{if(t&&"number"===t.type){var r=1/0,n=Array.from(n8(e.map(e=>e.value))).sort((e,t)=>e-t);if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(0===i)return 1/0;for(var o=0;o<n.length-1;o++)r=Math.min(r,n[o+1]-n[o]);return r/i}}),iz=(0,f.Mz)(iN,r9.fz,nj.gY,nE.HZ,(e,t,r,n)=>n,(e,t,r,n,i)=>{if(!(0,nn.H)(e))return 0;var o="vertical"===t?n.height:n.width;if("gap"===i)return e*o/2;if("no-gap"===i){var a=(0,nr.F4)(r,e*o),l=e*o/2;return l-a-(l-a)/o*a}return 0}),iF=(0,f.Mz)(nL,(e,t)=>{var r=nL(e,t);return null==r||"string"!=typeof r.padding?0:iz(e,"xAxis",t,r.padding)},(e,t)=>{if(null==e)return{left:0,right:0};var r,n,{padding:i}=e;return"string"==typeof i?{left:t,right:t}:{left:(null!=(r=i.left)?r:0)+t,right:(null!=(n=i.right)?n:0)+t}}),iL=(0,f.Mz)(nV,(e,t)=>{var r=nV(e,t);return null==r||"string"!=typeof r.padding?0:iz(e,"yAxis",t,r.padding)},(e,t)=>{if(null==e)return{top:0,bottom:0};var r,n,{padding:i}=e;return"string"==typeof i?{top:t,bottom:t}:{top:(null!=(r=i.top)?r:0)+t,bottom:(null!=(n=i.bottom)?n:0)+t}}),iB=(0,f.Mz)([nE.HZ,iF,nA.U,nA.C,(e,t,r)=>r],(e,t,r,n,i)=>{var{padding:o}=n;return i?[o.left,r.width-o.right]:[e.left+t.left,e.left+e.width-t.right]}),iV=(0,f.Mz)([nE.HZ,r9.fz,iL,nA.U,nA.C,(e,t,r)=>r],(e,t,r,n,i,o)=>{var{padding:a}=i;return o?[n.height-a.bottom,a.top]:"horizontal"===t?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]}),iU=(e,t,r,n)=>{var i;switch(t){case"xAxis":return iB(e,r,n);case"yAxis":return iV(e,r,n);case"zAxis":return null==(i=nH(e,r))?void 0:i.range;case"angleAxis":return(0,n_.Cv)(e);case"radiusAxis":return(0,n_.Dc)(e,r);default:return}},iH=(0,f.Mz)([nG,iU],nD.I),iG=(0,f.Mz)([nG,i_,iR,iH],ik);function i$(e,t){return e.id<t.id?-1:+(e.id>t.id)}(0,f.Mz)(nX,nk,(e,t)=>e.flatMap(e=>{var t;return null!=(t=e.errorBars)?t:[]}).filter(e=>n3(t,e)));var iK=(e,t)=>t,iq=(e,t,r)=>r,iZ=(0,f.Mz)(nC.h,iK,iq,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(i$)),iW=(0,f.Mz)(nC.W,iK,iq,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(i$)),iY=(e,t)=>({width:e.width,height:t.height}),iX=(0,f.Mz)(nE.HZ,nL,iY),iJ=(0,f.Mz)(nM.A$,nE.HZ,iZ,iK,iq,(e,t,r,n,i)=>{var o,a={};return r.forEach(r=>{var l=iY(t,r);null==o&&(o=((e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}})(t,n,e));var u="top"===n&&!i||"bottom"===n&&i;a[r.id]=o-Number(u)*l.height,o+=(u?-1:1)*l.height}),a}),iQ=(0,f.Mz)(nM.Lp,nE.HZ,iW,iK,iq,(e,t,r,n,i)=>{var o,a={};return r.forEach(r=>{var l=((e,t)=>({width:"number"==typeof t.width?t.width:nI.tQ,height:e.height}))(t,r);null==o&&(o=((e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}})(t,n,e));var u="left"===n&&!i||"right"===n&&i;a[r.id]=o-Number(u)*l.width,o+=(u?-1:1)*l.width}),a}),i0=(e,t)=>{var r=(0,nE.HZ)(e),n=nL(e,t);if(null!=n){var i=iJ(e,n.orientation,n.mirror)[t];return null==i?{x:r.left,y:0}:{x:r.left,y:i}}},i1=(e,t)=>{var r=(0,nE.HZ)(e),n=nV(e,t);if(null!=n){var i=iQ(e,n.orientation,n.mirror)[t];return null==i?{x:0,y:r.top}:{x:i,y:r.top}}},i2=(0,f.Mz)(nE.HZ,nV,(e,t)=>({width:"number"==typeof t.width?t.width:nI.tQ,height:e.height})),i5=(e,t,r,n)=>{if(null!=r){var{allowDuplicatedCategory:i,type:o,dataKey:a}=r,l=(0,ne._L)(e,n),u=t.map(e=>e.value);if(a&&l&&"category"===o&&i&&(0,nr.CG)(u))return u}},i6=(0,f.Mz)([r9.fz,n4,nG,nk],i5),i4=(e,t,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:o}=r;if((0,ne._L)(e,n)&&("number"===i||"auto"!==o))return t.map(e=>e.value)}},i3=(0,f.Mz)([r9.fz,n4,n$,nk],i4),i8=(0,f.Mz)([r9.fz,(e,t,r)=>{switch(t){case"xAxis":return nL(e,r);case"yAxis":return nV(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},i_,iG,i6,i3,iU,iD,nk],(e,t,r,n,i,o,a,l,u)=>{if(null==t)return null;var s=(0,ne._L)(e,u);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:u,categoricalDomain:o,duplicateDomain:i,isCategorical:s,niceTicks:l,range:a,realScaleType:r,scale:n}}),i7=(0,f.Mz)([r9.fz,n$,i_,iG,iD,iU,i6,i3,nk],(e,t,r,n,i,o,a,l,u)=>{if(null!=t&&null!=n){var s=(0,ne._L)(e,u),{type:c,ticks:f,tickCount:d}=t,h="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,p="category"===c&&n.bandwidth?n.bandwidth()/h:0;p="angleAxis"===u&&null!=o&&o.length>=2?2*(0,nr.sA)(o[0]-o[1])*p:p;var g=f||i;return g?g.map((e,t)=>({index:t,coordinate:n(a?a.indexOf(e):e)+p,value:e,offset:p})).filter(e=>!(0,nr.M8)(e.coordinate)):s&&l?l.map((e,t)=>({coordinate:n(e)+p,value:e,index:t,offset:p})):n.ticks?n.ticks(d).map(e=>({coordinate:n(e)+p,value:e,offset:p})):n.domain().map((e,t)=>({coordinate:n(e)+p,value:a?a[e]:e,index:t,offset:p}))}}),i9=(0,f.Mz)([r9.fz,n$,iG,iU,i6,i3,nk],(e,t,r,n,i,o,a)=>{if(null!=t&&null!=r&&null!=n&&n[0]!==n[1]){var l=(0,ne._L)(e,a),{tickCount:u}=t,s=0;return(s="angleAxis"===a&&(null==n?void 0:n.length)>=2?2*(0,nr.sA)(n[0]-n[1])*s:s,l&&o)?o.map((e,t)=>({coordinate:r(e)+s,value:e,index:t,offset:s})):r.ticks?r.ticks(u).map(e=>({coordinate:r(e)+s,value:e,offset:s})):r.domain().map((e,t)=>({coordinate:r(e)+s,value:i?i[e]:e,index:t,offset:s}))}}),oe=(0,f.Mz)(nG,iG,(e,t)=>{if(null!=e&&null!=t)return nN(nN({},e),{},{scale:t})}),ot=(0,f.Mz)([nG,i_,iA,iH],ik),or=(0,f.Mz)((e,t,r)=>nH(e,r),ot,(e,t)=>{if(null!=e&&null!=t)return nN(nN({},e),{},{scale:t})}),on=(0,f.Mz)([r9.fz,nC.h,nC.W],(e,t,r)=>{switch(e){case"horizontal":return t.some(e=>e.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(e=>e.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}})},2188:(e,t,r)=>{e.exports=r(5252).isEqual},2194:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(921);t.property=function(e){return function(t){return n.get(t,e)}}},2248:(e,t,r)=>{"use strict";r.d(t,{Vi:()=>c,ZF:()=>s,g5:()=>u,iZ:()=>h});var n=r(5710),i=r(4532),o=(0,n.Z0)({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(e){e.countOfBars+=1},removeBar(e){e.countOfBars-=1},addCartesianGraphicalItem(e,t){e.cartesianItems.push((0,i.h4)(t.payload))},replaceCartesianGraphicalItem(e,t){var{prev:r,next:n}=t.payload,o=(0,i.ss)(e).cartesianItems.indexOf((0,i.h4)(r));o>-1&&(e.cartesianItems[o]=(0,i.h4)(n))},removeCartesianGraphicalItem(e,t){var r=(0,i.ss)(e).cartesianItems.indexOf((0,i.h4)(t.payload));r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push((0,i.h4)(t.payload))},removePolarGraphicalItem(e,t){var r=(0,i.ss)(e).polarItems.indexOf((0,i.h4)(t.payload));r>-1&&e.polarItems.splice(r,1)}}}),{addBar:a,removeBar:l,addCartesianGraphicalItem:u,replaceCartesianGraphicalItem:s,removeCartesianGraphicalItem:c,addPolarGraphicalItem:f,removePolarGraphicalItem:d}=o.actions,h=o.reducer},2348:(e,t,r)=>{"use strict";r.d(t,{W:()=>u});var n=r(2115),i=r(2596),o=r(788),a=["children","className"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var u=n.forwardRef((e,t)=>{var{children:r,className:u}=e,s=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,a),c=(0,i.$)("recharts-layer",u);return n.createElement("g",l({className:c},(0,o.J9)(s,!0),{ref:t}),r)})},2384:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObject=function(e){return null!==e&&("object"==typeof e||"function"==typeof e)}},2391:(e,t,r)=>{"use strict";r.d(t,{t:()=>tt});var n=r(2115),i=r(6641);r(1992);var o={notify(){},get:()=>[]},a="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,l="undefined"!=typeof navigator&&"ReactNative"===navigator.product,u=a||l?n.useLayoutEffect:n.useEffect;Object.getOwnPropertyNames,Object.getOwnPropertySymbols,Object.getOwnPropertyDescriptor,Object.getPrototypeOf,Object.prototype;var s=Symbol.for("react-redux-context"),c="undefined"!=typeof globalThis?globalThis:{},f=function(){if(!n.createContext)return{};let e=c[s]??=new Map,t=e.get(n.createContext);return t||(t=n.createContext(null),e.set(n.createContext,t)),t}(),d=function(e){let{children:t,context:r,serverState:i,store:a}=e,l=n.useMemo(()=>{let e=function(e,t){let r,n=o,i=0,a=!1;function l(){c.onStateChange&&c.onStateChange()}function u(){if(i++,!r){let t,i;r=e.subscribe(l),t=null,i=null,n={clear(){t=null,i=null},notify(){let e=t;for(;e;)e.callback(),e=e.next},get(){let e=[],r=t;for(;r;)e.push(r),r=r.next;return e},subscribe(e){let r=!0,n=i={callback:e,next:null,prev:i};return n.prev?n.prev.next=n:t=n,function(){r&&null!==t&&(r=!1,n.next?n.next.prev=n.prev:i=n.prev,n.prev?n.prev.next=n.next:t=n.next)}}}}}function s(){i--,r&&0===i&&(r(),r=void 0,n.clear(),n=o)}let c={addNestedSub:function(e){u();let t=n.subscribe(e),r=!1;return()=>{r||(r=!0,t(),s())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:l,isSubscribed:function(){return a},trySubscribe:function(){a||(a=!0,u())},tryUnsubscribe:function(){a&&(a=!1,s())},getListeners:()=>n};return c}(a);return{store:a,subscription:e,getServerState:i?()=>i:void 0}},[a,i]),s=n.useMemo(()=>a.getState(),[a]);return u(()=>{let{subscription:e}=l;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),s!==a.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[l,s]),n.createElement((r||f).Provider,{value:l},t)},h=r(52),p=r(5710),g=r(4890),y=r(4487),v=(0,p.Z0)({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:m,setLayout:b,setChartSize:w,setScale:x}=v.actions,O=v.reducer,S=r(8924),P=r(7238),M=r(215),C=r(6124),E=r(4732),A=r(7062),j=(0,S.Mz)([(e,t)=>t,P.fz,A.D0,M.Re,M.gL,M.R4,E.r1,C.HZ],E.aX),_=r(6523),k=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}},T=(0,p.VP)("mouseClick"),D=(0,p.Nc)();D.startListening({actionCreator:T,effect:(e,t)=>{var r=e.payload,n=j(t.getState(),k(r));(null==n?void 0:n.activeIndex)!=null&&t.dispatch((0,g.jF)({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var I=(0,p.VP)("mouseMove"),R=(0,p.Nc)();function N(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}R.startListening({actionCreator:I,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=(0,_.au)(n,n.tooltip.settings.shared),o=j(n,k(r));"axis"===i&&((null==o?void 0:o.activeIndex)!=null?t.dispatch((0,g.Nt)({activeIndex:o.activeIndex,activeDataKey:void 0,activeCoordinate:o.activeCoordinate})):t.dispatch((0,g.xS)()))}});var z=r(5306),F=r(2248),L=r(4532),B=(0,p.Z0)({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=(0,L.ss)(e).dots.findIndex(e=>e===t.payload);-1!==r&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=(0,L.ss)(e).areas.findIndex(e=>e===t.payload);-1!==r&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=(0,L.ss)(e).lines.findIndex(e=>e===t.payload);-1!==r&&e.lines.splice(r,1)}}}),{addDot:V,removeDot:U,addArea:H,removeArea:G,addLine:$,removeLine:K}=B.actions,q=B.reducer,Z={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},W=(0,p.Z0)({name:"brush",initialState:Z,reducers:{setBrushSettings:(e,t)=>null==t.payload?Z:t.payload}}),{setBrushSettings:Y}=W.actions,X=W.reducer,J=r(2634),Q={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},ee=(0,p.Z0)({name:"rootProps",initialState:Q,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=null!=(r=t.payload.barGap)?r:Q.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),et=ee.reducer,{updateOptions:er}=ee.actions,en=(0,p.Z0)({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=(0,L.h4)(t.payload)},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=(0,L.h4)(t.payload)},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:ei,removeRadiusAxis:eo,addAngleAxis:ea,removeAngleAxis:el}=en.actions,eu=en.reducer,es=(0,p.Z0)({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:ec}=es.actions,ef=es.reducer,ed=r(2183),eh=r(841),ep=(0,p.VP)("keyDown"),eg=(0,p.VP)("focus"),ey=(0,p.Nc)();ey.startListening({actionCreator:ep,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,i=e.payload;if("ArrowRight"===i||"ArrowLeft"===i||"Enter"===i){var o=Number((0,eh.P)(n,(0,M.n4)(r))),a=(0,M.R4)(r);if("Enter"===i){var l=(0,E.pg)(r,"axis","hover",String(n.index));t.dispatch((0,g.o4)({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:l}));return}var u=o+("ArrowRight"===i?1:-1)*("left-to-right"===(0,ed._y)(r)?1:-1);if(null!=a&&!(u>=a.length)&&!(u<0)){var s=(0,E.pg)(r,"axis","hover",String(u));t.dispatch((0,g.o4)({active:!0,activeIndex:u.toString(),activeDataKey:void 0,activeCoordinate:s}))}}}}}),ey.startListening({actionCreator:eg,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var i=(0,E.pg)(r,"axis","hover",String("0"));t.dispatch((0,g.o4)({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:i}))}}}});var ev=(0,p.VP)("externalEvent"),em=(0,p.Nc)();em.startListening({actionCreator:ev,effect:(e,t)=>{if(null!=e.payload.handler){var r=t.getState(),n={activeCoordinate:(0,M.eE)(r),activeDataKey:(0,M.Xb)(r),activeIndex:(0,M.A2)(r),activeLabel:(0,M.BZ)(r),activeTooltipIndex:(0,M.A2)(r),isTooltipActive:(0,M.yn)(r)};e.payload.handler(n,e.payload.reactEvent)}}});var eb=r(4421),ew=r(6670),ex=r(5714),eO=(0,S.Mz)([ex.J],e=>e.tooltipItemPayloads),eS=(0,S.Mz)([eO,ew.x,(e,t,r)=>t,(e,t,r)=>r],(e,t,r,n)=>{var i=e.find(e=>e.settings.dataKey===n);if(null!=i){var{positions:o}=i;if(null!=o)return t(o,r)}}),eP=(0,p.VP)("touchMove"),eM=(0,p.Nc)();eM.startListening({actionCreator:eP,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=(0,_.au)(n,n.tooltip.settings.shared);if("axis"===i){var o=j(n,k({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(null==o?void 0:o.activeIndex)!=null&&t.dispatch((0,g.Nt)({activeIndex:o.activeIndex,activeDataKey:void 0,activeCoordinate:o.activeCoordinate}))}else if("item"===i){var a,l=r.touches[0],u=document.elementFromPoint(l.clientX,l.clientY);if(!u||!u.getAttribute)return;var s=u.getAttribute(eb.F0),c=null!=(a=u.getAttribute(eb.um))?a:void 0,f=eS(t.getState(),s,c);t.dispatch((0,g.RD)({activeDataKey:c,activeIndex:s,activeCoordinate:f}))}}});var eC=(0,h.HY)({brush:X,cartesianAxis:z.CA,chartData:y.LV,graphicalItems:F.iZ,layout:O,legend:J.CU,options:i.lJ,polarAxis:eu,polarOptions:ef,referenceElements:q,rootProps:et,tooltip:g.En}),eE=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return(0,p.U1)({reducer:eC,preloadedState:e,middleware:e=>e({serializableCheck:!1}).concat([D.middleware,R.middleware,ey.middleware,em.middleware,eM.middleware]),devTools:{serialize:{replacer:N},name:"recharts-".concat(t)}})},eA=r(1807),ej=r(5064);function e_(e){var{preloadedState:t,children:r,reduxStoreName:i}=e,o=(0,eA.r)(),a=(0,n.useRef)(null);if(o)return r;null==a.current&&(a.current=eE(t,i));var l=ej.E;return n.createElement(d,{context:l,store:a.current},r)}var ek=r(1971),eT=e=>{var{chartData:t}=e,r=(0,ek.j)(),i=(0,eA.r)();return(0,n.useEffect)(()=>i?()=>{}:(r((0,y.hq)(t)),()=>{r((0,y.hq)(void 0))}),[t,r,i]),null};function eD(e){var{layout:t,width:r,height:i,margin:o}=e,a=(0,ek.j)(),l=(0,eA.r)();return(0,n.useEffect)(()=>{l||(a(b(t)),a(w({width:r,height:i})),a(m(o)))},[a,l,t,r,i,o]),null}function eI(e){var t=(0,ek.j)();return(0,n.useEffect)(()=>{t(er(e))},[t,e]),null}var eR=r(788),eN=r(6752),ez=r(2790),eF=r(972),eL=r(8892),eB=["children"];function eV(){return(eV=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var eU={width:"100%",height:"100%"},eH=(0,n.forwardRef)((e,t)=>{var r,i,o=(0,P.yi)(),a=(0,P.rY)(),l=(0,eN.$)();if(!(0,eL.F)(o)||!(0,eL.F)(a))return null;var{children:u,otherAttributes:s,title:c,desc:f}=e;return r="number"==typeof s.tabIndex?s.tabIndex:l?0:void 0,i="string"==typeof s.role?s.role:l?"application":void 0,n.createElement(ez.u,eV({},s,{title:c,desc:f,role:i,tabIndex:r,width:o,height:a,style:eU,ref:t}),u)}),eG=e=>{var{children:t}=e,r=(0,ek.G)(eF.U);if(!r)return null;var{width:i,height:o,y:a,x:l}=r;return n.createElement(ez.u,{width:i,height:o,x:l,y:a},t)},e$=(0,n.forwardRef)((e,t)=>{var{children:r}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,eB);return(0,eA.r)()?n.createElement(eG,null,r):n.createElement(eH,eV({ref:t},i),r)}),eK=r(2596),eq=r(6850),eZ=r(2589),eW=r(5115),eY=r(8060);function eX(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var eJ=(0,n.forwardRef)((e,t)=>{var{children:r,className:i,height:o,onClick:a,onContextMenu:l,onDoubleClick:u,onMouseDown:s,onMouseEnter:c,onMouseLeave:f,onMouseMove:d,onMouseUp:h,onTouchEnd:p,onTouchMove:y,onTouchStart:v,style:m,width:b}=e,w=(0,ek.j)(),[O,S]=(0,n.useState)(null),[P,M]=(0,n.useState)(null);(0,eq.l3)();var C=function(){var e=(0,ek.j)(),[t,r]=(0,n.useState)(null),i=(0,ek.G)(eZ.et);return(0,n.useEffect)(()=>{if(null!=t){var r=t.getBoundingClientRect().width/t.offsetWidth;(0,eL.H)(r)&&r!==i&&e(x(r))}},[t,e,i]),r}(),E=(0,n.useCallback)(e=>{C(e),"function"==typeof t&&t(e),S(e),M(e)},[C,t,S,M]),A=(0,n.useCallback)(e=>{w(T(e)),w(ev({handler:a,reactEvent:e}))},[w,a]),j=(0,n.useCallback)(e=>{w(I(e)),w(ev({handler:c,reactEvent:e}))},[w,c]),_=(0,n.useCallback)(e=>{w((0,g.xS)()),w(ev({handler:f,reactEvent:e}))},[w,f]),k=(0,n.useCallback)(e=>{w(I(e)),w(ev({handler:d,reactEvent:e}))},[w,d]),D=(0,n.useCallback)(()=>{w(eg())},[w]),R=(0,n.useCallback)(e=>{w(ep(e.key))},[w]),N=(0,n.useCallback)(e=>{w(ev({handler:l,reactEvent:e}))},[w,l]),z=(0,n.useCallback)(e=>{w(ev({handler:u,reactEvent:e}))},[w,u]),F=(0,n.useCallback)(e=>{w(ev({handler:s,reactEvent:e}))},[w,s]),L=(0,n.useCallback)(e=>{w(ev({handler:h,reactEvent:e}))},[w,h]),B=(0,n.useCallback)(e=>{w(ev({handler:v,reactEvent:e}))},[w,v]),V=(0,n.useCallback)(e=>{w(eP(e)),w(ev({handler:y,reactEvent:e}))},[w,y]),U=(0,n.useCallback)(e=>{w(ev({handler:p,reactEvent:e}))},[w,p]);return n.createElement(eW.$.Provider,{value:O},n.createElement(eY.t.Provider,{value:P},n.createElement("div",{className:(0,eK.$)("recharts-wrapper",i),style:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eX(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eX(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({position:"relative",cursor:"default",width:b,height:o},m),onClick:A,onContextMenu:N,onDoubleClick:z,onFocus:D,onKeyDown:R,onMouseDown:F,onMouseEnter:j,onMouseLeave:_,onMouseMove:k,onMouseUp:L,onTouchEnd:U,onTouchMove:V,onTouchStart:B,ref:E},r)))}),eQ=r(6377),e0=r(8234),e1=(0,n.createContext)(void 0),e2=e=>{var{children:t}=e,[r]=(0,n.useState)("".concat((0,eQ.NF)("recharts"),"-clip")),i=(0,e0.oM)();if(null==i)return null;var{x:o,y:a,width:l,height:u}=i;return n.createElement(e1.Provider,{value:r},n.createElement("defs",null,n.createElement("clipPath",{id:r},n.createElement("rect",{x:o,y:a,height:u,width:l}))),t)},e5=["children","className","width","height","style","compact","title","desc"],e6=(0,n.forwardRef)((e,t)=>{var{children:r,className:i,width:o,height:a,style:l,compact:u,title:s,desc:c}=e,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,e5),d=(0,eR.J9)(f,!1);return u?n.createElement(e$,{otherAttributes:d,title:s,desc:c},r):n.createElement(eJ,{className:i,style:l,width:o,height:a,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},n.createElement(e$,{otherAttributes:d,title:s,desc:c,ref:t},n.createElement(e2,null,r)))}),e4=r(3389),e3=["width","height"];function e8(){return(e8=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var e7={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},e9=(0,n.forwardRef)(function(e,t){var r,i=(0,e4.e)(e.categoricalChartProps,e7),{width:o,height:a}=i,l=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,e3);if(!(0,eL.F)(o)||!(0,eL.F)(a))return null;var{chartName:u,defaultTooltipEventType:s,validateTooltipEventTypes:c,tooltipPayloadSearcher:f,categoricalChartProps:d}=e;return n.createElement(e_,{preloadedState:{options:{chartName:u,defaultTooltipEventType:s,validateTooltipEventTypes:c,tooltipPayloadSearcher:f,eventEmitter:void 0}},reduxStoreName:null!=(r=d.id)?r:u},n.createElement(eT,{chartData:d.data}),n.createElement(eD,{width:o,height:a,layout:i.layout,margin:i.margin}),n.createElement(eI,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),n.createElement(e6,e8({},l,{width:o,height:a,ref:t})))}),te=["item"],tt=(0,n.forwardRef)((e,t)=>n.createElement(e9,{chartName:"ScatterChart",defaultTooltipEventType:"item",validateTooltipEventTypes:te,tooltipPayloadSearcher:i.uN,categoricalChartProps:e,ref:t}))},2429:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(4117);t.cloneDeep=function(e){return n.cloneDeepWithImpl(e,void 0,e,new Map,void 0)}},2434:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(7064),i=r(5998),o=r(4373);t.sortBy=function(e,...t){let r=t.length;return r>1&&o.isIterateeCall(e,t[0],t[1])?t=[]:r>2&&o.isIterateeCall(t[0],t[1],t[2])&&(t=[t[0]]),n.orderBy(e,i.flatten(t),["asc"])}},2436:(e,t,r)=>{"use strict";var n=r(2115),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=n.useState,a=n.useEffect,l=n.useLayoutEffect,u=n.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=o({inst:{value:r,getSnapshot:t}}),i=n[0].inst,c=n[1];return l(function(){i.value=r,i.getSnapshot=t,s(i)&&c({inst:i})},[e,r,t]),a(function(){return s(i)&&c({inst:i}),e(function(){s(i)&&c({inst:i})})},[e]),u(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:c},2465:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.identity=function(e){return e}},2494:(e,t,r)=>{"use strict";r.d(t,{s:()=>o});var n=r(512),i=r.n(n);function o(e,t,r){return!0===t?i()(e,r):"function"==typeof t?i()(e,t):e}},2520:(e,t,r)=>{"use strict";var n=r(9641).Buffer;Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let i=r(1147),o=r(8221),a=r(5160),l=r(2721),u=r(3616);t.isEqualWith=function(e,t,r){return function e(t,r,s,c,f,d,h){let p=h(t,r,s,c,f,d);if(void 0!==p)return p;if(typeof t==typeof r)switch(typeof t){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return t===r;case"number":return t===r||Object.is(t,r)}return function t(r,s,c,f){if(Object.is(r,s))return!0;let d=a.getTag(r),h=a.getTag(s);if(d===l.argumentsTag&&(d=l.objectTag),h===l.argumentsTag&&(h=l.objectTag),d!==h)return!1;switch(d){case l.stringTag:return r.toString()===s.toString();case l.numberTag:{let e=r.valueOf(),t=s.valueOf();return u.eq(e,t)}case l.booleanTag:case l.dateTag:case l.symbolTag:return Object.is(r.valueOf(),s.valueOf());case l.regexpTag:return r.source===s.source&&r.flags===s.flags;case l.functionTag:return r===s}let p=(c=c??new Map).get(r),g=c.get(s);if(null!=p&&null!=g)return p===s;c.set(r,s),c.set(s,r);try{switch(d){case l.mapTag:if(r.size!==s.size)return!1;for(let[t,n]of r.entries())if(!s.has(t)||!e(n,s.get(t),t,r,s,c,f))return!1;return!0;case l.setTag:{if(r.size!==s.size)return!1;let t=Array.from(r.values()),n=Array.from(s.values());for(let i=0;i<t.length;i++){let o=t[i],a=n.findIndex(t=>e(o,t,void 0,r,s,c,f));if(-1===a)return!1;n.splice(a,1)}return!0}case l.arrayTag:case l.uint8ArrayTag:case l.uint8ClampedArrayTag:case l.uint16ArrayTag:case l.uint32ArrayTag:case l.bigUint64ArrayTag:case l.int8ArrayTag:case l.int16ArrayTag:case l.int32ArrayTag:case l.bigInt64ArrayTag:case l.float32ArrayTag:case l.float64ArrayTag:if(void 0!==n&&n.isBuffer(r)!==n.isBuffer(s)||r.length!==s.length)return!1;for(let t=0;t<r.length;t++)if(!e(r[t],s[t],t,r,s,c,f))return!1;return!0;case l.arrayBufferTag:if(r.byteLength!==s.byteLength)return!1;return t(new Uint8Array(r),new Uint8Array(s),c,f);case l.dataViewTag:if(r.byteLength!==s.byteLength||r.byteOffset!==s.byteOffset)return!1;return t(new Uint8Array(r),new Uint8Array(s),c,f);case l.errorTag:return r.name===s.name&&r.message===s.message;case l.objectTag:{if(!(t(r.constructor,s.constructor,c,f)||i.isPlainObject(r)&&i.isPlainObject(s)))return!1;let n=[...Object.keys(r),...o.getSymbols(r)],a=[...Object.keys(s),...o.getSymbols(s)];if(n.length!==a.length)return!1;for(let t=0;t<n.length;t++){let i=n[t],o=r[i];if(!Object.hasOwn(s,i))return!1;let a=s[i];if(!e(o,a,i,r,s,c,f))return!1}return!0}default:return!1}}finally{c.delete(r),c.delete(s)}}(t,r,d,h)}(e,t,void 0,void 0,void 0,void 0,r)}},2525:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2589:(e,t,r)=>{"use strict";r.d(t,{A$:()=>i,HK:()=>a,Lp:()=>n,et:()=>o});var n=e=>e.layout.width,i=e=>e.layout.height,o=e=>e.layout.scale,a=e=>e.layout.margin},2596:(e,t,r)=>{"use strict";function n(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=function e(t){var r,n,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t)if(Array.isArray(t)){var o=t.length;for(r=0;r<o;r++)t[r]&&(n=e(t[r]))&&(i&&(i+=" "),i+=n)}else for(n in t)t[n]&&(i&&(i+=" "),i+=n);return i}(e))&&(n&&(n+=" "),n+=t);return n}r.d(t,{$:()=>n})},2634:(e,t,r)=>{"use strict";r.d(t,{CU:()=>c,Lx:()=>u,h1:()=>l,hx:()=>a,u3:()=>s});var n=r(5710),i=r(4532),o=(0,n.Z0)({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push((0,i.h4)(t.payload))},removeLegendPayload(e,t){var r=(0,i.ss)(e).payload.indexOf((0,i.h4)(t.payload));r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:a,setLegendSettings:l,addLegendPayload:u,removeLegendPayload:s}=o.actions,c=o.reducer},2657:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2661:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function o(e,t,n,o,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var l=new i(n,o||e,a),u=r?r+t:t;return e._events[u]?e._events[u].fn?e._events[u]=[e._events[u],l]:e._events[u].push(l):(e._events[u]=l,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function l(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),l.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},l.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,o=n.length,a=Array(o);i<o;i++)a[i]=n[i].fn;return a},l.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},l.prototype.emit=function(e,t,n,i,o,a){var l=r?r+e:e;if(!this._events[l])return!1;var u,s,c=this._events[l],f=arguments.length;if(c.fn){switch(c.once&&this.removeListener(e,c.fn,void 0,!0),f){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,t),!0;case 3:return c.fn.call(c.context,t,n),!0;case 4:return c.fn.call(c.context,t,n,i),!0;case 5:return c.fn.call(c.context,t,n,i,o),!0;case 6:return c.fn.call(c.context,t,n,i,o,a),!0}for(s=1,u=Array(f-1);s<f;s++)u[s-1]=arguments[s];c.fn.apply(c.context,u)}else{var d,h=c.length;for(s=0;s<h;s++)switch(c[s].once&&this.removeListener(e,c[s].fn,void 0,!0),f){case 1:c[s].fn.call(c[s].context);break;case 2:c[s].fn.call(c[s].context,t);break;case 3:c[s].fn.call(c[s].context,t,n);break;case 4:c[s].fn.call(c[s].context,t,n,i);break;default:if(!u)for(d=1,u=Array(f-1);d<f;d++)u[d-1]=arguments[d];c[s].fn.apply(c[s].context,u)}}return!0},l.prototype.on=function(e,t,r){return o(this,e,t,r,!1)},l.prototype.once=function(e,t,r){return o(this,e,t,r,!0)},l.prototype.removeListener=function(e,t,n,i){var o=r?r+e:e;if(!this._events[o])return this;if(!t)return a(this,o),this;var l=this._events[o];if(l.fn)l.fn!==t||i&&!l.once||n&&l.context!==n||a(this,o);else{for(var u=0,s=[],c=l.length;u<c;u++)(l[u].fn!==t||i&&!l[u].once||n&&l[u].context!==n)&&s.push(l[u]);s.length?this._events[o]=1===s.length?s[0]:s:a(this,o)}return this},l.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=r,l.EventEmitter=l,e.exports=l},2694:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(668);t.toNumber=function(e){return n.isSymbol(e)?NaN:Number(e)}},2721:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.argumentsTag="[object Arguments]",t.arrayBufferTag="[object ArrayBuffer]",t.arrayTag="[object Array]",t.bigInt64ArrayTag="[object BigInt64Array]",t.bigUint64ArrayTag="[object BigUint64Array]",t.booleanTag="[object Boolean]",t.dataViewTag="[object DataView]",t.dateTag="[object Date]",t.errorTag="[object Error]",t.float32ArrayTag="[object Float32Array]",t.float64ArrayTag="[object Float64Array]",t.functionTag="[object Function]",t.int16ArrayTag="[object Int16Array]",t.int32ArrayTag="[object Int32Array]",t.int8ArrayTag="[object Int8Array]",t.mapTag="[object Map]",t.numberTag="[object Number]",t.objectTag="[object Object]",t.regexpTag="[object RegExp]",t.setTag="[object Set]",t.stringTag="[object String]",t.symbolTag="[object Symbol]",t.uint16ArrayTag="[object Uint16Array]",t.uint32ArrayTag="[object Uint32Array]",t.uint8ArrayTag="[object Uint8Array]",t.uint8ClampedArrayTag="[object Uint8ClampedArray]"},2744:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8132),i=r(2384),o=r(6633),a=r(3616);function l(e,t,r,n){if(t===e)return!0;switch(typeof t){case"object":return function(e,t,r,n){if(null==t)return!0;if(Array.isArray(t))return u(e,t,r,n);if(t instanceof Map){var i=e,a=t,l=r,c=n;if(0===a.size)return!0;if(!(i instanceof Map))return!1;for(let[e,t]of a.entries())if(!1===l(i.get(e),t,e,i,a,c))return!1;return!0}if(t instanceof Set)return s(e,t,r,n);let f=Object.keys(t);if(null==e)return 0===f.length;if(0===f.length)return!0;if(n&&n.has(t))return n.get(t)===e;n&&n.set(t,e);try{for(let i=0;i<f.length;i++){let a=f[i];if(!o.isPrimitive(e)&&!(a in e)||void 0===t[a]&&void 0!==e[a]||null===t[a]&&null!==e[a]||!r(e[a],t[a],a,e,t,n))return!1}return!0}finally{n&&n.delete(t)}}(e,t,r,n);case"function":if(Object.keys(t).length>0)return l(e,{...t},r,n);return a.eq(e,t);default:if(!i.isObject(e))return a.eq(e,t);if("string"==typeof t)return""===t;return!0}}function u(e,t,r,n){if(0===t.length)return!0;if(!Array.isArray(e))return!1;let i=new Set;for(let o=0;o<t.length;o++){let a=t[o],l=!1;for(let u=0;u<e.length;u++){if(i.has(u))continue;let s=e[u],c=!1;if(r(s,a,o,e,t,n)&&(c=!0),c){i.add(u),l=!0;break}}if(!l)return!1}return!0}function s(e,t,r,n){return 0===t.size||e instanceof Set&&u([...e],[...t],r,n)}t.isMatchWith=function(e,t,r){return"function"!=typeof r?n.isMatch(e,t):l(e,t,function e(t,n,i,o,a,u){let s=r(t,n,i,o,a,u);return void 0!==s?!!s:l(t,n,e,u)},new Map)},t.isSetMatch=s},2767:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.noop=function(){}},2790:(e,t,r)=>{"use strict";r.d(t,{u:()=>u});var n=r(2115),i=r(2596),o=r(788),a=["children","width","height","viewBox","className","style","title","desc"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var u=(0,n.forwardRef)((e,t)=>{var{children:r,width:u,height:s,viewBox:c,className:f,style:d,title:h,desc:p}=e,g=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,a),y=c||{width:u,height:s,x:0,y:0},v=(0,i.$)("recharts-surface",f);return n.createElement("svg",l({},(0,o.J9)(g,!0,"svg"),{className:v,width:u,height:s,style:d,viewBox:"".concat(y.x," ").concat(y.y," ").concat(y.width," ").concat(y.height),ref:t}),n.createElement("title",null,h),n.createElement("desc",null,p),r)})},2962:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8673);t.throttle=function(e,t=0,r={}){let{leading:i=!0,trailing:o=!0}=r;return n.debounce(e,t,{leading:i,maxWait:t,trailing:o})}},3109:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},3205:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(4545),i=r(8412),o=r(177),a=r(4072);t.has=function(e,t){let r;if(0===(r=Array.isArray(t)?t:"string"==typeof t&&n.isDeepKey(t)&&e?.[t]==null?a.toPath(t):[t]).length)return!1;let l=e;for(let e=0;e<r.length;e++){let t=r[e];if((null==l||!Object.hasOwn(l,t))&&!((Array.isArray(l)||o.isArguments(l))&&i.isIndex(t)&&t<l.length))return!1;l=l[t]}return!0}},3389:(e,t,r)=>{"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return Object.keys(t).reduce((e,r)=>(void 0===e[r]&&void 0!==t[r]&&(e[r]=t[r]),e),r)}r.d(t,{e:()=>i})},3540:(e,t,r)=>{"use strict";r.d(t,{u:()=>f});var n=r(2596),i=r(2115),o=r(400),a=r.n(o),l=r(6377),u=r(675);function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var f=(0,i.forwardRef)((e,t)=>{var{aspect:r,initialDimension:o={width:-1,height:-1},width:s="100%",height:f="100%",minWidth:d=0,minHeight:h,maxHeight:p,children:g,debounce:y=0,id:v,className:m,onResize:b,style:w={}}=e,x=(0,i.useRef)(null),O=(0,i.useRef)();O.current=b,(0,i.useImperativeHandle)(t,()=>x.current);var[S,P]=(0,i.useState)({containerWidth:o.width,containerHeight:o.height}),M=(0,i.useCallback)((e,t)=>{P(r=>{var n=Math.round(e),i=Math.round(t);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,i.useEffect)(()=>{var e=e=>{var t,{width:r,height:n}=e[0].contentRect;M(r,n),null==(t=O.current)||t.call(O,r,n)};y>0&&(e=a()(e,y,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),{width:r,height:n}=x.current.getBoundingClientRect();return M(r,n),t.observe(x.current),()=>{t.disconnect()}},[M,y]);var C=(0,i.useMemo)(()=>{var{containerWidth:e,containerHeight:t}=S;if(e<0||t<0)return null;(0,u.R)((0,l._3)(s)||(0,l._3)(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",s,f),(0,u.R)(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=(0,l._3)(s)?e:s,o=(0,l._3)(f)?t:f;return r&&r>0&&(n?o=n/r:o&&(n=o*r),p&&o>p&&(o=p)),(0,u.R)(n>0||o>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,o,s,f,d,h,r),i.Children.map(g,e=>(0,i.cloneElement)(e,{width:n,height:o,style:c({width:n,height:o},e.props.style)}))},[r,g,f,p,h,d,S,s]);return i.createElement("div",{id:v?"".concat(v):void 0,className:(0,n.$)("recharts-responsive-container",m),style:c(c({},w),{},{width:s,height:f,minWidth:d,minHeight:h,maxHeight:p}),ref:x},i.createElement("div",{style:{width:0,height:0,overflow:"visible"}},C))})},3597:(e,t,r)=>{"use strict";r.d(t,{QQ:()=>i,VU:()=>a,XC:()=>s,_U:()=>u,j2:()=>l});var n=r(2115),i=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],o=["points","pathLength"],a={svg:["viewBox","children"],polygon:o,polyline:o},l=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],u=(e,t)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,n.isValidElement)(e)&&(r=e.props),"object"!=typeof r&&"function"!=typeof r)return null;var i={};return Object.keys(r).forEach(e=>{l.includes(e)&&(i[e]=t||(t=>r[e](r,t)))}),i},s=(e,t,r)=>{if(null===e||"object"!=typeof e&&"function"!=typeof e)return null;var n=null;return Object.keys(e).forEach(i=>{var o=e[i];l.includes(i)&&"function"==typeof o&&(n||(n={}),n[i]=e=>(o(t,r,e),null))}),n}},3616:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.eq=function(e,t){return e===t||Number.isNaN(e)&&Number.isNaN(t)}},3676:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.uniqBy=function(e,t){let r=new Map;for(let n=0;n<e.length;n++){let i=e[n],o=t(i);r.has(o)||r.set(o,i)}return Array.from(r.values())}},3786:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},3949:(e,t,r)=>{e.exports=r(9901).range},4013:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var n=r(6377),i=r(9827);function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var l=(e,t,r,o,l,u,s)=>{if(null!=t&&null!=u){var{chartData:c,computedData:f,dataStartIndex:d,dataEndIndex:h}=r;return e.reduce((e,r)=>{var p,g,y,v,m,{dataDefinedOnItem:b,settings:w}=r,x=function(e,t,r){return Array.isArray(e)&&e&&t+r!==0?e.slice(t,r+1):e}((p=b,g=c,null!=p?p:g),d,h),O=null!=(y=null==w?void 0:w.dataKey)?y:null==o?void 0:o.dataKey,S=null==w?void 0:w.nameKey;return Array.isArray(v=null!=o&&o.dataKey&&Array.isArray(x)&&!Array.isArray(x[0])&&"axis"===s?(0,n.eP)(x,o.dataKey,l):u(x,t,f,S))?v.forEach(t=>{var r=a(a({},w),{},{name:t.name,unit:t.unit,color:void 0,fill:void 0});e.push((0,i.GF)({tooltipEntrySettings:r,dataKey:t.dataKey,payload:t.payload,value:(0,i.kr)(t.payload,t.dataKey),name:t.name}))}):e.push((0,i.GF)({tooltipEntrySettings:w,dataKey:O,payload:v,value:(0,i.kr)(v,O),name:null!=(m=(0,i.kr)(v,S))?m:null==w?void 0:w.name})),e},[])}}},4072:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toPath=function(e){let t=[],r=e.length;if(0===r)return t;let n=0,i="",o="",a=!1;for(46===e.charCodeAt(0)&&(t.push(""),n++);n<r;){let l=e[n];o?"\\"===l&&n+1<r?i+=e[++n]:l===o?o="":i+=l:a?'"'===l||"'"===l?o=l:"]"===l?(a=!1,t.push(i),i=""):i+=l:"["===l?(a=!0,i&&(t.push(i),i="")):"."===l?i&&(t.push(i),i=""):i+=l,n++}return i&&t.push(i),t}},4117:(e,t,r)=>{"use strict";var n=r(9641).Buffer;Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let i=r(8221),o=r(5160),a=r(2721),l=r(6633),u=r(885);function s(e,t,r,i=new Map,f){let d=f?.(e,t,r,i);if(null!=d)return d;if(l.isPrimitive(e))return e;if(i.has(e))return i.get(e);if(Array.isArray(e)){let t=Array(e.length);i.set(e,t);for(let n=0;n<e.length;n++)t[n]=s(e[n],n,r,i,f);return Object.hasOwn(e,"index")&&(t.index=e.index),Object.hasOwn(e,"input")&&(t.input=e.input),t}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){let t=new RegExp(e.source,e.flags);return t.lastIndex=e.lastIndex,t}if(e instanceof Map){let t=new Map;for(let[n,o]of(i.set(e,t),e))t.set(n,s(o,n,r,i,f));return t}if(e instanceof Set){let t=new Set;for(let n of(i.set(e,t),e))t.add(s(n,void 0,r,i,f));return t}if(void 0!==n&&n.isBuffer(e))return e.subarray();if(u.isTypedArray(e)){let t=new(Object.getPrototypeOf(e)).constructor(e.length);i.set(e,t);for(let n=0;n<e.length;n++)t[n]=s(e[n],n,r,i,f);return t}if(e instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){let t=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return i.set(e,t),c(t,e,r,i,f),t}if("undefined"!=typeof File&&e instanceof File){let t=new File([e],e.name,{type:e.type});return i.set(e,t),c(t,e,r,i,f),t}if(e instanceof Blob){let t=new Blob([e],{type:e.type});return i.set(e,t),c(t,e,r,i,f),t}if(e instanceof Error){let t=new e.constructor;return i.set(e,t),t.message=e.message,t.name=e.name,t.stack=e.stack,t.cause=e.cause,c(t,e,r,i,f),t}if("object"==typeof e&&function(e){switch(o.getTag(e)){case a.argumentsTag:case a.arrayTag:case a.arrayBufferTag:case a.dataViewTag:case a.booleanTag:case a.dateTag:case a.float32ArrayTag:case a.float64ArrayTag:case a.int8ArrayTag:case a.int16ArrayTag:case a.int32ArrayTag:case a.mapTag:case a.numberTag:case a.objectTag:case a.regexpTag:case a.setTag:case a.stringTag:case a.symbolTag:case a.uint8ArrayTag:case a.uint8ClampedArrayTag:case a.uint16ArrayTag:case a.uint32ArrayTag:return!0;default:return!1}}(e)){let t=Object.create(Object.getPrototypeOf(e));return i.set(e,t),c(t,e,r,i,f),t}return e}function c(e,t,r=e,n,o){let a=[...Object.keys(t),...i.getSymbols(t)];for(let i=0;i<a.length;i++){let l=a[i],u=Object.getOwnPropertyDescriptor(e,l);(null==u||u.writable)&&(e[l]=s(t[l],l,r,n,o))}}t.cloneDeepWith=function(e,t){return s(e,void 0,e,new Map,t)},t.cloneDeepWithImpl=s,t.copyProperties=c},4373:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8412),i=r(8179),o=r(2384),a=r(3616);t.isIterateeCall=function(e,t,r){return!!o.isObject(r)&&(!!("number"==typeof t&&i.isArrayLike(r)&&n.isIndex(t))&&t<r.length||"string"==typeof t&&t in r)&&a.eq(r[t],e)}},4421:(e,t,r)=>{"use strict";r.d(t,{F0:()=>n,tQ:()=>o,um:()=>i});var n="data-recharts-item-index",i="data-recharts-item-data-key",o=60},4460:(e,t,r)=>{"use strict";r.d(t,{i:()=>E});var n=r(2115),i=r(2188),o=r.n(i),a=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],l=(e,t)=>e.map((e,r)=>e*t**r).reduce((e,t)=>e+t),u=(e,t)=>r=>l(a(e,t),r),s=function(){let e,t;for(var r,n,i,o,s=arguments.length,c=Array(s),f=0;f<s;f++)c[f]=arguments[f];if(1===c.length)switch(c[0]){case"linear":[r,i,n,o]=[0,0,1,1];break;case"ease":[r,i,n,o]=[.25,.1,.25,1];break;case"ease-in":[r,i,n,o]=[.42,0,1,1];break;case"ease-out":[r,i,n,o]=[.42,0,.58,1];break;case"ease-in-out":[r,i,n,o]=[0,0,.58,1];break;default:var d=c[0].split("(");"cubic-bezier"===d[0]&&4===d[1].split(")")[0].split(",").length&&([r,i,n,o]=d[1].split(")")[0].split(",").map(e=>parseFloat(e)))}else 4===c.length&&([r,i,n,o]=c);var h=u(r,n),p=u(i,o),g=(e=r,t=n,r=>l([...a(e,t).map((e,t)=>e*t).slice(1),0],r)),y=e=>e>1?1:e<0?0:e,v=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var i=h(r)-t,o=g(r);if(1e-4>Math.abs(i-t)||o<1e-4)break;r=y(r-i/o)}return p(r)};return v.isStepper=!1,v},c=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,i=(e,i,o)=>{var a=o+(-(e-i)*t-o*r)*n/1e3,l=o*n/1e3+e;return 1e-4>Math.abs(l-i)&&1e-4>Math.abs(a)?[i,0]:[l,a]};return i.isStepper=!0,i.dt=n,i};function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var h=(e,t)=>Object.keys(t).reduce((r,n)=>d(d({},r),{},{[n]:e(n,t[n])}),{});function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function g(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y=(e,t,r)=>e+(t-e)*r,v=e=>{var{from:t,to:r}=e;return t!==r},m=(e,t,r)=>{var n=h((t,r)=>{if(v(r)){var[n,i]=e(r.from,r.to,r.velocity);return g(g({},r),{},{from:n,velocity:i})}return r},t);return r<1?h((e,t)=>v(t)?g(g({},t),{},{velocity:y(t.velocity,n[e].velocity,r),from:y(t.from,n[e].from,r)}):t,t):m(e,n,r-1)};class b{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=o=>{o-r>=t?e(o):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}var w=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function x(){return(x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function S(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?O(Object(r),!0).forEach(function(t){P(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function P(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class M extends n.PureComponent{componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:i,to:a,from:l}=this.props,{style:u}=this.state;if(r){if(!t){this.state&&u&&(n&&u[n]!==a||!n&&u!==a)&&this.setState({style:n?{[n]:a}:a});return}if(!o()(e.to,a)||!e.canBegin||!e.isActive){var s=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var c=s||i?l:e.to;this.state&&u&&(n&&u[n]!==c||!n&&u!==c)&&this.setState({style:n?{[n]:c}:c}),this.runAnimation(S(S({},this.props),{},{from:c,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var t,r,n,i,o,a,l,u,f,d,p,b,w,x,O,S,P,M,C,E,A,j,_,k,T,{from:D,to:I,duration:R,easing:N,begin:z,onAnimationEnd:F,onAnimationStart:L}=e,B=(j=(e=>{if("string"==typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return s(e);case"spring":return c();default:if("cubic-bezier"===e.split("(")[0])return s(e)}return"function"==typeof e?e:null})(N),_=this.changeStyle,k=this.manager.getTimeoutController(),T=[Object.keys(D),Object.keys(I)].reduce((e,t)=>e.filter(e=>t.includes(e))),!0===j.isStepper?(t=D,r=I,n=j,i=T,o=_,a=k,u=i.reduce((e,n)=>g(g({},e),{},{[n]:{from:t[n],velocity:0,to:r[n]}}),{}),f=null,d=e=>{l||(l=e);var i=(e-l)/n.dt;u=m(n,u,i),o(g(g(g({},t),r),h((e,t)=>t.from,u))),l=e,Object.values(u).filter(v).length&&(f=a.setTimeout(d))},()=>(f=a.setTimeout(d),()=>{f()})):(p=D,b=I,w=j,x=R,O=T,S=_,P=k,C=null,E=O.reduce((e,t)=>g(g({},e),{},{[t]:[p[t],b[t]]}),{}),A=e=>{M||(M=e);var t=(e-M)/x,r=h((e,r)=>y(...r,w(t)),E);if(S(g(g(g({},p),b),r)),t<1)C=P.setTimeout(A);else{var n=h((e,t)=>y(...t,w(1)),E);S(g(g(g({},p),b),n))}},()=>(C=P.setTimeout(A),()=>{C()})));this.manager.start([L,z,()=>{this.stopJSAnimation=B()},R,F])}runAnimation(e){let t;var{begin:r,duration:n,attributeName:i,to:o,easing:a,onAnimationStart:l,onAnimationEnd:u,children:s}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof a||"function"==typeof s||"spring"===a)return void this.runJSAnimation(e);var c=i?{[i]:o}:o,f=(t=Object.keys(c),t.map(e=>"".concat(e.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase()))," ").concat(n,"ms ").concat(a)).join(","));this.manager.start([l,r,S(S({},c),{},{transition:f}),n,u])}render(){var e=this.props,{children:t,begin:r,duration:i,attributeName:o,easing:a,isActive:l,from:u,to:s,canBegin:c,onAnimationEnd:f,shouldReAnimate:d,onAnimationReStart:h,animationManager:p}=e,g=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,w),y=n.Children.count(t),v=this.state.style;if("function"==typeof t)return t(v);if(!l||0===y||i<=0)return t;var m=e=>{var{style:t={},className:r}=e.props;return(0,n.cloneElement)(e,S(S({},g),{},{style:S(S({},t),v),className:r}))};return 1===y?m(n.Children.only(t)):n.createElement("div",null,n.Children.map(t,e=>m(e)))}constructor(e,t){super(e,t),P(this,"mounted",!1),P(this,"manager",null),P(this,"stopJSAnimation",null),P(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:o,children:a,duration:l,animationManager:u}=this.props;if(this.manager=u,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0){this.state={style:{}},"function"==typeof a&&(this.state={style:o});return}if(i){if("function"==typeof a){this.state={style:i};return}this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}}P(M,"displayName","Animate"),P(M,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var C=(0,n.createContext)(null);function E(e){var t,r,i,o,a,l,u,s=(0,n.useContext)(C);return n.createElement(M,x({},e,{animationManager:null!=(l=null!=(u=e.animationManager)?u:s)?l:(t=new b,r=()=>null,i=!1,o=null,a=e=>{if(!i){if(Array.isArray(e)){if(!e.length)return;var[n,...l]=e;if("number"==typeof n){o=t.setTimeout(a.bind(null,l),n);return}a(n),o=t.setTimeout(a.bind(null,l));return}"object"==typeof e&&r(e),"function"==typeof e&&e()}},{stop:()=>{i=!0},start:e=>{i=!1,o&&(o(),o=null),a(e)},subscribe:e=>(r=e,()=>{r=()=>null}),getTimeoutController:()=>t})}))}},4487:(e,t,r)=>{"use strict";r.d(t,{LV:()=>l,M:()=>o,hq:()=>i});var n=(0,r(5710).Z0)({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(e,t){if(e.chartData=t.payload,null==t.payload){e.dataStartIndex=0,e.dataEndIndex=0;return}t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;null!=r&&(e.dataStartIndex=r),null!=n&&(e.dataEndIndex=n)}}}),{setChartData:i,setDataStartEndIndexes:o,setComputedData:a}=n.actions,l=n.reducer},4517:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8132),i=r(6200),o=r(7298),a=r(921),l=r(3205);t.matchesProperty=function(e,t){switch(typeof e){case"object":Object.is(e?.valueOf(),-0)&&(e="-0");break;case"number":e=i.toKey(e)}return t=o.cloneDeep(t),function(r){let i=a.get(r,e);return void 0===i?l.has(r,e):void 0===t?void 0===i:n.isMatch(i,t)}}},4532:(e,t,r)=>{"use strict";r.d(t,{Qx:()=>s,a6:()=>c,h4:()=>$,jM:()=>G,ss:()=>U});var n,i=Symbol.for("immer-nothing"),o=Symbol.for("immer-draftable"),a=Symbol.for("immer-state");function l(e,...t){throw Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var u=Object.getPrototypeOf;function s(e){return!!e&&!!e[a]}function c(e){return!!e&&(d(e)||Array.isArray(e)||!!e[o]||!!e.constructor?.[o]||v(e)||m(e))}var f=Object.prototype.constructor.toString();function d(e){if(!e||"object"!=typeof e)return!1;let t=u(e);if(null===t)return!0;let r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===f}function h(e,t){0===p(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function p(e){let t=e[a];return t?t.type_:Array.isArray(e)?1:v(e)?2:3*!!m(e)}function g(e,t){return 2===p(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function y(e,t,r){let n=p(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function v(e){return e instanceof Map}function m(e){return e instanceof Set}function b(e){return e.copy_||e.base_}function w(e,t){if(v(e))return new Map(e);if(m(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let r=d(e);if(!0!==t&&("class_only"!==t||r)){let t=u(e);return null!==t&&r?{...e}:Object.assign(Object.create(t),e)}{let t=Object.getOwnPropertyDescriptors(e);delete t[a];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){let i=r[n],o=t[i];!1===o.writable&&(o.writable=!0,o.configurable=!0),(o.get||o.set)&&(t[i]={configurable:!0,writable:!0,enumerable:o.enumerable,value:e[i]})}return Object.create(u(e),t)}}function x(e,t=!1){return S(e)||s(e)||!c(e)||(p(e)>1&&(e.set=e.add=e.clear=e.delete=O),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>x(t,!0))),e}function O(){l(2)}function S(e){return Object.isFrozen(e)}var P={};function M(e){let t=P[e];return t||l(0,e),t}function C(e,t){t&&(M("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function E(e){A(e),e.drafts_.forEach(_),e.drafts_=null}function A(e){e===n&&(n=e.parent_)}function j(e){return n={drafts_:[],parent_:n,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function _(e){let t=e[a];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function k(e,t){t.unfinalizedDrafts_=t.drafts_.length;let r=t.drafts_[0];return void 0!==e&&e!==r?(r[a].modified_&&(E(t),l(4)),c(e)&&(e=T(t,e),t.parent_||I(t,e)),t.patches_&&M("Patches").generateReplacementPatches_(r[a].base_,e,t.patches_,t.inversePatches_)):e=T(t,r,[]),E(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==i?e:void 0}function T(e,t,r){if(S(t))return t;let n=t[a];if(!n)return h(t,(i,o)=>D(e,n,t,i,o,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return I(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;let t=n.copy_,i=t,o=!1;3===n.type_&&(i=new Set(t),t.clear(),o=!0),h(i,(i,a)=>D(e,n,t,i,a,r,o)),I(e,t,!1),r&&e.patches_&&M("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function D(e,t,r,n,i,o,a){if(s(i)){let a=T(e,i,o&&t&&3!==t.type_&&!g(t.assigned_,n)?o.concat(n):void 0);if(y(r,n,a),!s(a))return;e.canAutoFreeze_=!1}else a&&r.add(i);if(c(i)&&!S(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;T(e,i),(!t||!t.scope_.parent_)&&"symbol"!=typeof n&&Object.prototype.propertyIsEnumerable.call(r,n)&&I(e,i)}}function I(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&x(t,r)}var R={get(e,t){if(t===a)return e;let r=b(e);if(!g(r,t)){var n=e,i=r,o=t;let a=F(i,o);return a?"value"in a?a.value:a.get?.call(n.draft_):void 0}let l=r[t];return e.finalized_||!c(l)?l:l===z(e.base_,t)?(B(e),e.copy_[t]=V(l,e)):l},has:(e,t)=>t in b(e),ownKeys:e=>Reflect.ownKeys(b(e)),set(e,t,r){let n=F(b(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){let n=z(b(e),t),i=n?.[a];if(i&&i.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if((r===n?0!==r||1/r==1/n:r!=r&&n!=n)&&(void 0!==r||g(e.base_,t)))return!0;B(e),L(e)}return!!(e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t]))||(e.copy_[t]=r,e.assigned_[t]=!0,!0)},deleteProperty:(e,t)=>(void 0!==z(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,B(e),L(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){let r=b(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){l(11)},getPrototypeOf:e=>u(e.base_),setPrototypeOf(){l(12)}},N={};function z(e,t){let r=e[a];return(r?b(r):e)[t]}function F(e,t){if(!(t in e))return;let r=u(e);for(;r;){let e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=u(r)}}function L(e){!e.modified_&&(e.modified_=!0,e.parent_&&L(e.parent_))}function B(e){e.copy_||(e.copy_=w(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function V(e,t){let r=v(e)?M("MapSet").proxyMap_(e,t):m(e)?M("MapSet").proxySet_(e,t):function(e,t){let r=Array.isArray(e),i={type_:+!!r,scope_:t?t.scope_:n,modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1},o=i,a=R;r&&(o=[i],a=N);let{revoke:l,proxy:u}=Proxy.revocable(o,a);return i.draft_=u,i.revoke_=l,u}(e,t);return(t?t.scope_:n).drafts_.push(r),r}function U(e){return s(e)||l(10,e),function e(t){let r;if(!c(t)||S(t))return t;let n=t[a];if(n){if(!n.modified_)return n.base_;n.finalized_=!0,r=w(t,n.scope_.immer_.useStrictShallowCopy_)}else r=w(t,!0);return h(r,(t,n)=>{y(r,t,e(n))}),n&&(n.finalized_=!1),r}(e)}h(R,(e,t)=>{N[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),N.deleteProperty=function(e,t){return N.set.call(this,e,t,void 0)},N.set=function(e,t,r){return R.set.call(this,e[0],t,r,e[0])};var H=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{let n;if("function"==typeof e&&"function"!=typeof t){let r=t;t=e;let n=this;return function(e=r,...i){return n.produce(e,e=>t.call(this,e,...i))}}if("function"!=typeof t&&l(6),void 0!==r&&"function"!=typeof r&&l(7),c(e)){let i=j(this),o=V(e,void 0),a=!0;try{n=t(o),a=!1}finally{a?E(i):A(i)}return C(i,r),k(n,i)}if(e&&"object"==typeof e)l(1,e);else{if(void 0===(n=t(e))&&(n=e),n===i&&(n=void 0),this.autoFreeze_&&x(n,!0),r){let t=[],i=[];M("Patches").generateReplacementPatches_(e,n,t,i),r(t,i)}return n}},this.produceWithPatches=(e,t)=>{let r,n;return"function"==typeof e?(t,...r)=>this.produceWithPatches(t,t=>e(t,...r)):[this.produce(e,t,(e,t)=>{r=e,n=t}),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){c(e)||l(8),s(e)&&(e=U(e));let t=j(this),r=V(e,void 0);return r[a].isManual_=!0,A(t),r}finishDraft(e,t){let r=e&&e[a];r&&r.isManual_||l(9);let{scope_:n}=r;return C(n,t),k(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){let n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));let n=M("Patches").applyPatches_;return s(e)?n(e,t):this.produce(e,e=>n(e,t))}},G=H.produce;function $(e){return e}H.produceWithPatches.bind(H),H.setAutoFreeze.bind(H),H.setUseStrictShallowCopy.bind(H),H.applyPatches.bind(H),H.createDraft.bind(H),H.finishDraft.bind(H)},4538:(e,t,r)=>{"use strict";r.d(t,{M:()=>f});var n=r(2115),i=r(2596),o=r(788),a=r(3389),l=r(4460);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var s=(e,t,r,n,i)=>{var o,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,u=r>=0?1:-1,s=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&i instanceof Array){for(var c=[0,0,0,0],f=0;f<4;f++)c[f]=i[f]>a?a:i[f];o="M".concat(e,",").concat(t+l*c[0]),c[0]>0&&(o+="A ".concat(c[0],",").concat(c[0],",0,0,").concat(s,",").concat(e+u*c[0],",").concat(t)),o+="L ".concat(e+r-u*c[1],",").concat(t),c[1]>0&&(o+="A ".concat(c[1],",").concat(c[1],",0,0,").concat(s,",\n        ").concat(e+r,",").concat(t+l*c[1])),o+="L ".concat(e+r,",").concat(t+n-l*c[2]),c[2]>0&&(o+="A ".concat(c[2],",").concat(c[2],",0,0,").concat(s,",\n        ").concat(e+r-u*c[2],",").concat(t+n)),o+="L ".concat(e+u*c[3],",").concat(t+n),c[3]>0&&(o+="A ".concat(c[3],",").concat(c[3],",0,0,").concat(s,",\n        ").concat(e,",").concat(t+n-l*c[3])),o+="Z"}else if(a>0&&i===+i&&i>0){var d=Math.min(a,i);o="M ".concat(e,",").concat(t+l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e+u*d,",").concat(t,"\n            L ").concat(e+r-u*d,",").concat(t,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e+r,",").concat(t+l*d,"\n            L ").concat(e+r,",").concat(t+n-l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e+r-u*d,",").concat(t+n,"\n            L ").concat(e+u*d,",").concat(t+n,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e,",").concat(t+n-l*d," Z")}else o="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return o},c={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},f=e=>{var t=(0,a.e)(e,c),r=(0,n.useRef)(null),[f,d]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&d(e)}catch(e){}},[]);var{x:h,y:p,width:g,height:y,radius:v,className:m}=t,{animationEasing:b,animationDuration:w,animationBegin:x,isAnimationActive:O,isUpdateAnimationActive:S}=t;if(h!==+h||p!==+p||g!==+g||y!==+y||0===g||0===y)return null;var P=(0,i.$)("recharts-rectangle",m);return S?n.createElement(l.i,{canBegin:f>0,from:{width:g,height:y,x:h,y:p},to:{width:g,height:y,x:h,y:p},duration:w,animationEasing:b,isActive:S},e=>{var{width:i,height:a,x:c,y:d}=e;return n.createElement(l.i,{canBegin:f>0,from:"0px ".concat(-1===f?1:f,"px"),to:"".concat(f,"px 0px"),attributeName:"strokeDasharray",begin:x,duration:w,isActive:O,easing:b},n.createElement("path",u({},(0,o.J9)(t,!0),{className:P,d:s(c,d,i,a,v),ref:r})))}):n.createElement("path",u({},(0,o.J9)(t,!0),{className:P,d:s(h,p,g,y,v)}))}},4545:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isDeepKey=function(e){switch(typeof e){case"number":case"symbol":return!1;case"string":return e.includes(".")||e.includes("[")||e.includes("]")}}},4664:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(2694);t.toFinite=function(e){return e?(e=n.toNumber(e))===1/0||e===-1/0?(e<0?-1:1)*Number.MAX_VALUE:e==e?e:0:0===e?e:0}},4683:(e,t,r)=>{"use strict";r.d(t,{s:()=>k});var n=r(2115),i=r(7650),o=r(8060),a=r(2596),l=r(2790),u=r(9795),s=r(3597);function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class h extends n.PureComponent{renderIcon(e,t){var{inactiveColor:r}=this.props,i=32/6,o=32/3,a=e.inactive?r:e.color,l=null!=t?t:e.type;if("none"===l)return null;if("plainline"===l)return n.createElement("line",{strokeWidth:4,fill:"none",stroke:a,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===l)return n.createElement("path",{strokeWidth:4,fill:"none",stroke:a,d:"M0,".concat(16,"h").concat(o,"\n            A").concat(i,",").concat(i,",0,1,1,").concat(2*o,",").concat(16,"\n            H").concat(32,"M").concat(2*o,",").concat(16,"\n            A").concat(i,",").concat(i,",0,1,1,").concat(o,",").concat(16),className:"recharts-legend-icon"});if("rect"===l)return n.createElement("path",{stroke:"none",fill:a,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(n.isValidElement(e.legendIcon)){var s=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return delete s.legendIcon,n.cloneElement(e.legendIcon,s)}return n.createElement(u.i,{fill:a,cx:16,cy:16,size:32,sizeType:"diameter",type:l})}renderItems(){var{payload:e,iconSize:t,layout:r,formatter:i,inactiveColor:o,iconType:u}=this.props,f={x:0,y:0,width:32,height:32},d={display:"horizontal"===r?"inline-block":"block",marginRight:10},h={display:"inline-block",verticalAlign:"middle",marginRight:4};return e.map((e,r)=>{var p=e.formatter||i,g=(0,a.$)({"recharts-legend-item":!0,["legend-item-".concat(r)]:!0,inactive:e.inactive});if("none"===e.type)return null;var y=e.inactive?o:e.color,v=p?p(e.value,e,r):e.value;return n.createElement("li",c({className:g,style:d,key:"legend-item-".concat(r)},(0,s.XC)(this.props,e,r)),n.createElement(l.u,{width:t,height:t,viewBox:f,style:h,"aria-label":"".concat(v," legend icon")},this.renderIcon(e,u)),n.createElement("span",{className:"recharts-legend-item-text",style:{color:y}},v))})}render(){var{payload:e,layout:t,align:r}=this.props;return e&&e.length?n.createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===t?r:"left"}},this.renderItems()):null}}d(h,"displayName","Legend"),d(h,"defaultProps",{align:"center",iconSize:14,inactiveColor:"#ccc",layout:"horizontal",verticalAlign:"middle"});var p=r(6377),g=r(2494),y=r(1971),v=r(5803),m=r(7918),b=r(7238),w=r(2634),x=["contextPayload"];function O(){return(O=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function S(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function P(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?S(Object(r),!0).forEach(function(t){M(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function M(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function C(e){return e.value}function E(e){var{contextPayload:t}=e,r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,x),i=(0,g.s)(t,e.payloadUniqBy,C),o=P(P({},r),{},{payload:i});return n.isValidElement(e.content)?n.cloneElement(e.content,o):"function"==typeof e.content?n.createElement(e.content,o):n.createElement(h,o)}function A(e){var t=(0,y.j)();return(0,n.useEffect)(()=>{t((0,w.h1)(e))},[t,e]),null}function j(e){var t=(0,y.j)();return(0,n.useEffect)(()=>(t((0,w.hx)(e)),()=>{t((0,w.hx)({width:0,height:0}))}),[t,e]),null}function _(e){var t=(0,y.G)(v.g0),r=(0,o.M)(),a=(0,b.Kp)(),{width:l,height:u,wrapperStyle:s,portal:c}=e,[f,d]=(0,m.V)([t]),h=(0,b.yi)(),p=(0,b.rY)(),g=h-(a.left||0)-(a.right||0),w=k.getWidthOrHeight(e.layout,u,l,g),x=c?s:P(P({position:"absolute",width:(null==w?void 0:w.width)||l||"auto",height:(null==w?void 0:w.height)||u||"auto"},function(e,t,r,n,i,o){var a,l,{layout:u,align:s,verticalAlign:c}=t;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(a="center"===s&&"vertical"===u?{left:((n||0)-o.width)/2}:"right"===s?{right:r&&r.right||0}:{left:r&&r.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(l="middle"===c?{top:((i||0)-o.height)/2}:"bottom"===c?{bottom:r&&r.bottom||0}:{top:r&&r.top||0}),P(P({},a),l)}(s,e,a,h,p,f)),s),S=null!=c?c:r;if(null==S)return null;var M=n.createElement("div",{className:"recharts-legend-wrapper",style:x,ref:d},n.createElement(A,{layout:e.layout,align:e.align,verticalAlign:e.verticalAlign,itemSorter:e.itemSorter}),n.createElement(j,{width:f.width,height:f.height}),n.createElement(E,O({},e,w,{margin:a,chartWidth:h,chartHeight:p,contextPayload:t})));return(0,i.createPortal)(M,S)}class k extends n.PureComponent{static getWidthOrHeight(e,t,r,n){return"vertical"===e&&(0,p.Et)(t)?{height:t}:"horizontal"===e?{width:r||n}:null}render(){return n.createElement(_,this.props)}}M(k,"displayName","Legend"),M(k,"defaultProps",{align:"center",iconSize:14,itemSorter:"value",layout:"horizontal",verticalAlign:"bottom"})},4732:(e,t,r)=>{"use strict";r.d(t,{BZ:()=>D,aX:()=>N,dS:()=>T,dp:()=>j,fW:()=>O,pg:()=>k,r1:()=>C,u9:()=>I,yn:()=>R});var n=r(8924),i=r(241),o=r.n(i),a=r(1971),l=r(9827),u=r(356),s=r(215),c=r(8478),f=r(7238),d=r(6124),h=r(2589),p=r(530),g=r(1928),y=r(841),v=r(4968),m=r(5146),b=r(6670),w=r(5714),x=r(4013),O=()=>(0,a.G)(c.iO),S=(e,t)=>t,P=(e,t,r)=>r,M=(e,t,r,n)=>n,C=(0,n.Mz)(s.R4,e=>o()(e,e=>e.coordinate)),E=(0,n.Mz)([w.J,S,P,M],g.i),A=(0,n.Mz)([E,s.n4],y.P),j=(e,t,r)=>{if(null!=t){var n=(0,w.J)(e);return"axis"===t?"hover"===r?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:"hover"===r?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}},_=(0,n.Mz)([w.J,S,P,M],m.q),k=(0,n.Mz)([h.Lp,h.A$,f.fz,d.HZ,s.R4,M,_,b.x],v.o),T=(0,n.Mz)([E,k],(e,t)=>{var r;return null!=(r=e.coordinate)?r:t}),D=(0,n.Mz)(s.R4,A,p.E),I=(0,n.Mz)([_,A,u.LF,s.Dn,D,b.x,S],x.N),R=(0,n.Mz)([E],e=>({isActive:e.active,activeIndex:e.index})),N=(e,t,r,n,i,o,a,u)=>{if(e&&t&&n&&i&&o){var s=(0,l.r4)(e.chartX,e.chartY,t,r,u);if(s){var c=(0,l.SW)(s,t),f=(0,l.gH)(c,a,o,n,i),d=(0,l.bk)(t,o,f,s);return{activeIndex:String(f),activeCoordinate:d}}}}},4754:(e,t,r)=>{"use strict";r.d(t,{d:()=>T});var n=r(2115),i=r(675),o=r(6377),a=r(788),l=r(9827),u=r(9035),s=r(9584),c=r(7238),f=r(2183),d=r(1971),h=r(1807),p=r(3389),g=["x1","y1","x2","y2","key"],y=["offset"],v=["xAxisId","yAxisId"],m=["xAxisId","yAxisId"];function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function w(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function x(){return(x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var S=e=>{var{fill:t}=e;if(!t||"none"===t)return null;var{fillOpacity:r,x:i,y:o,width:a,height:l,ry:u}=e;return n.createElement("rect",{x:i,y:o,ry:u,width:a,height:l,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function P(e,t){var r;if(n.isValidElement(e))r=n.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var{x1:i,y1:o,x2:l,y2:u,key:s}=t,c=O(t,g),f=(0,a.J9)(c,!1),{offset:d}=f,h=O(f,y);r=n.createElement("line",x({},h,{x1:i,y1:o,x2:l,y2:u,fill:"none",key:s}))}return r}function M(e){var{x:t,width:r,horizontal:i=!0,horizontalPoints:o}=e;if(!i||!o||!o.length)return null;var{xAxisId:a,yAxisId:l}=e,u=O(e,v),s=o.map((e,n)=>P(i,w(w({},u),{},{x1:t,y1:e,x2:t+r,y2:e,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},s)}function C(e){var{y:t,height:r,vertical:i=!0,verticalPoints:o}=e;if(!i||!o||!o.length)return null;var{xAxisId:a,yAxisId:l}=e,u=O(e,m),s=o.map((e,n)=>P(i,w(w({},u),{},{x1:e,y1:t,x2:e,y2:t+r,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},s)}function E(e){var{horizontalFill:t,fillOpacity:r,x:i,y:o,width:a,height:l,horizontalPoints:u,horizontal:s=!0}=e;if(!s||!t||!t.length)return null;var c=u.map(e=>Math.round(e+o-o)).sort((e,t)=>e-t);o!==c[0]&&c.unshift(0);var f=c.map((e,u)=>{var s=c[u+1]?c[u+1]-e:o+l-e;if(s<=0)return null;var f=u%t.length;return n.createElement("rect",{key:"react-".concat(u),y:e,x:i,height:s,width:a,stroke:"none",fill:t[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function A(e){var{vertical:t=!0,verticalFill:r,fillOpacity:i,x:o,y:a,width:l,height:u,verticalPoints:s}=e;if(!t||!r||!r.length)return null;var c=s.map(e=>Math.round(e+o-o)).sort((e,t)=>e-t);o!==c[0]&&c.unshift(0);var f=c.map((e,t)=>{var s=c[t+1]?c[t+1]-e:o+l-e;if(s<=0)return null;var f=t%r.length;return n.createElement("rect",{key:"react-".concat(t),x:e,y:a,width:s,height:u,stroke:"none",fill:r[f],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var j=(e,t)=>{var{xAxis:r,width:n,height:i,offset:o}=e;return(0,l.PW)((0,u.f)(w(w(w({},s.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.left,o.left+o.width,t)},_=(e,t)=>{var{yAxis:r,width:n,height:i,offset:o}=e;return(0,l.PW)((0,u.f)(w(w(w({},s.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.top,o.top+o.height,t)},k={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function T(e){var t=(0,c.yi)(),r=(0,c.rY)(),a=(0,c.W7)(),l=w(w({},(0,p.e)(e,k)),{},{x:(0,o.Et)(e.x)?e.x:a.left,y:(0,o.Et)(e.y)?e.y:a.top,width:(0,o.Et)(e.width)?e.width:a.width,height:(0,o.Et)(e.height)?e.height:a.height}),{xAxisId:u,yAxisId:s,x:g,y,width:v,height:m,syncWithTicks:b,horizontalValues:O,verticalValues:P}=l,T=(0,h.r)(),D=(0,d.G)(e=>(0,f.ZB)(e,"xAxis",u,T)),I=(0,d.G)(e=>(0,f.ZB)(e,"yAxis",s,T));if(!(0,o.Et)(v)||v<=0||!(0,o.Et)(m)||m<=0||!(0,o.Et)(g)||g!==+g||!(0,o.Et)(y)||y!==+y)return null;var R=l.verticalCoordinatesGenerator||j,N=l.horizontalCoordinatesGenerator||_,{horizontalPoints:z,verticalPoints:F}=l;if((!z||!z.length)&&"function"==typeof N){var L=O&&O.length,B=N({yAxis:I?w(w({},I),{},{ticks:L?O:I.ticks}):void 0,width:t,height:r,offset:a},!!L||b);(0,i.R)(Array.isArray(B),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof B,"]")),Array.isArray(B)&&(z=B)}if((!F||!F.length)&&"function"==typeof R){var V=P&&P.length,U=R({xAxis:D?w(w({},D),{},{ticks:V?P:D.ticks}):void 0,width:t,height:r,offset:a},!!V||b);(0,i.R)(Array.isArray(U),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof U,"]")),Array.isArray(U)&&(F=U)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(S,{fill:l.fill,fillOpacity:l.fillOpacity,x:l.x,y:l.y,width:l.width,height:l.height,ry:l.ry}),n.createElement(E,x({},l,{horizontalPoints:z})),n.createElement(A,x({},l,{verticalPoints:F})),n.createElement(M,x({},l,{offset:a,horizontalPoints:z,xAxis:D,yAxis:I})),n.createElement(C,x({},l,{offset:a,verticalPoints:F,xAxis:D,yAxis:I})))}T.displayName="CartesianGrid"},4804:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8132),i=r(2429);t.matches=function(e){return e=i.cloneDeep(e),t=>n.isMatch(t,e)}},4861:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},4890:(e,t,r)=>{"use strict";r.d(t,{E1:()=>y,En:()=>m,Ix:()=>l,ML:()=>h,Nt:()=>p,RD:()=>c,UF:()=>s,XB:()=>u,jF:()=>g,k_:()=>o,o4:()=>v,oP:()=>f,xS:()=>d});var n=r(5710),i=r(4532),o={active:!1,index:null,dataKey:void 0,coordinate:void 0},a=(0,n.Z0)({name:"tooltip",initialState:{itemInteraction:{click:o,hover:o},axisInteraction:{click:o,hover:o},keyboardInteraction:o,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push((0,i.h4)(t.payload))},removeTooltipEntrySettings(e,t){var r=(0,i.ss)(e).tooltipItemPayloads.indexOf((0,i.h4)(t.payload));r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:l,removeTooltipEntrySettings:u,setTooltipSettingsState:s,setActiveMouseOverItemIndex:c,mouseLeaveItem:f,mouseLeaveChart:d,setActiveClickItemIndex:h,setMouseOverAxisIndex:p,setMouseClickAxisIndex:g,setSyncInteraction:y,setKeyboardInteraction:v}=a.actions,m=a.reducer},4968:(e,t,r)=>{"use strict";r.d(t,{o:()=>n});var n=(e,t,r,n,i,o,a,l)=>{if(null!=o&&null!=l){var u=a[0],s=null==u?void 0:l(u.positions,o);if(null!=s)return s;var c=null==i?void 0:i[Number(o)];if(c)if("horizontal"===r)return{x:c.coordinate,y:(n.top+t)/2};else return{x:(n.left+e)/2,y:c.coordinate}}}},4986:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toArray=function(e){return Array.isArray(e)?e:Array.from(e)}},4993:(e,t,r)=>{"use strict";var n=r(2115);"function"==typeof Object.is&&Object.is,n.useSyncExternalStore,n.useRef,n.useEffect,n.useMemo,n.useDebugValue},5064:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var n=(0,r(2115).createContext)(null)},5115:(e,t,r)=>{"use strict";r.d(t,{$:()=>i,X:()=>o});var n=r(2115),i=(0,n.createContext)(null),o=()=>(0,n.useContext)(i)},5146:(e,t,r)=>{"use strict";r.d(t,{q:()=>n});var n=(e,t,r,n)=>{var i;return"axis"===t?e.tooltipItemPayloads:0===e.tooltipItemPayloads.length?[]:null==(i="hover"===r?e.itemInteraction.hover.dataKey:e.itemInteraction.click.dataKey)&&null!=n?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter(e=>{var t;return(null==(t=e.settings)?void 0:t.dataKey)===i})}},5160:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getTag=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}},5181:(e,t)=>{"use strict";function r(e){return"symbol"==typeof e?1:null===e?2:void 0===e?3:4*(e!=e)}Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.compareValues=(e,t,n)=>{if(e!==t){let i=r(e),o=r(t);if(i===o&&0===i){if(e<t)return"desc"===n?1:-1;if(e>t)return"desc"===n?-1:1}return"desc"===n?o-i:i-o}return 0}},5252:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(2520),i=r(2767);t.isEqual=function(e,t){return n.isEqualWith(e,t,i.noop)}},5306:(e,t,r)=>{"use strict";r.d(t,{CA:()=>g,D4:()=>d,Gc:()=>h,MC:()=>s,QG:()=>p,Vi:()=>u,cU:()=>c,fR:()=>f});var n=r(5710),i=r(4532);function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var l=(0,n.Z0)({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=(0,i.h4)(t.payload)},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=(0,i.h4)(t.payload)},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=(0,i.h4)(t.payload)},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=a(a({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:u,removeXAxis:s,addYAxis:c,removeYAxis:f,addZAxis:d,removeZAxis:h,updateYAxisWidth:p}=l.actions,g=l.reducer},5453:(e,t,r)=>{"use strict";r.d(t,{v:()=>a});var n=r(2115);let i=e=>{let t,r=new Set,n=(e,n)=>{let i="function"==typeof e?e(t):e;if(!Object.is(i,t)){let e=t;t=(null!=n?n:"object"!=typeof i||null===i)?i:Object.assign({},t,i),r.forEach(r=>r(t,e))}},i=()=>t,o={setState:n,getState:i,getInitialState:()=>a,subscribe:e=>(r.add(e),()=>r.delete(e))},a=t=e(n,i,o);return o},o=e=>{let t=(e=>e?i(e):i)(e),r=e=>(function(e,t=e=>e){let r=n.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return n.useDebugValue(r),r})(t,e);return Object.assign(r,t),r},a=e=>e?o(e):o},5641:(e,t,r)=>{"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}r.d(t,{IZ:()=>a,Kg:()=>o,lY:()=>l,yy:()=>u}),r(2115);var o=Math.PI/180,a=(e,t,r,n)=>({x:e+Math.cos(-o*n)*r,y:t+Math.sin(-o*n)*r}),l=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},u=(e,t)=>{var r,{x:n,y:o}=e,{radius:a,angle:l}=((e,t)=>{var{x:r,y:n}=e,{cx:i,cy:o}=t,a=((e,t)=>{var{x:r,y:n}=e,{x:i,y:o}=t;return Math.sqrt((r-i)**2+(n-o)**2)})({x:r,y:n},{x:i,y:o});if(a<=0)return{radius:a,angle:0};var l=Math.acos((r-i)/a);return n>o&&(l=2*Math.PI-l),{radius:a,angle:180*l/Math.PI,angleInRadian:l}})({x:n,y:o},t),{innerRadius:u,outerRadius:s}=t;if(a<u||a>s||0===a)return null;var{startAngle:c,endAngle:f}=(e=>{var{startAngle:t,endAngle:r}=e,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}})(t),d=l;if(c<=f){for(;d>f;)d-=360;for(;d<c;)d+=360;r=d>=c&&d<=f}else{for(;d>c;)d-=360;for(;d<f;)d+=360;r=d>=f&&d<=c}return r?i(i({},t),{},{radius:a,angle:((e,t)=>{var{startAngle:r,endAngle:n}=t;return e+360*Math.min(Math.floor(r/360),Math.floor(n/360))})(d,t)}):null}},5643:(e,t,r)=>{"use strict";e.exports=r(6115)},5654:(e,t,r)=>{"use strict";function n(e){return function(){return e}}r.d(t,{A:()=>n})},5672:(e,t,r)=>{e.exports=r(921).get},5710:(e,t,r)=>{"use strict";r.d(t,{U1:()=>h,VP:()=>u,Nc:()=>W,Z0:()=>v});var n=r(52);function i(e){return({dispatch:t,getState:r})=>n=>i=>"function"==typeof i?i(t,r,e):n(i)}var o=i(),a=r(4532),l=(r(9509),"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?n.Zz:n.Zz.apply(null,arguments)});function u(e,t){function r(...n){if(t){let r=t(...n);if(!r)throw Error(Y(0));return{type:e,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>(0,n.ve)(t)&&t.type===e,r}"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var s=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function c(e){return(0,a.a6)(e)?(0,a.jM)(e,()=>{}):e}function f(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var d=e=>t=>{setTimeout(t,e)};function h(e){let t,r,a,u=function(e){let{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:a=!0}=e??{},l=new s;return t&&("boolean"==typeof t?l.push(o):l.push(i(t.extraArgument))),l},{reducer:c,middleware:f,devTools:h=!0,duplicateMiddlewareCheck:p=!0,preloadedState:g,enhancers:y}=e||{};if("function"==typeof c)t=c;else if((0,n.Qd)(c))t=(0,n.HY)(c);else throw Error(Y(1));r="function"==typeof f?f(u):u();let v=n.Zz;h&&(v=l({trace:!1,..."object"==typeof h&&h}));let m=(a=(0,n.Tw)(...r),function(e){let{autoBatch:t=!0}=e??{},r=new s(a);return t&&r.push(((e={type:"raf"})=>t=>(...r)=>{let n=t(...r),i=!0,o=!1,a=!1,l=new Set,u="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:d(10):"callback"===e.type?e.queueNotification:d(e.timeout),s=()=>{a=!1,o&&(o=!1,l.forEach(e=>e()))};return Object.assign({},n,{subscribe(e){let t=n.subscribe(()=>i&&e());return l.add(e),()=>{t(),l.delete(e)}},dispatch(e){try{return(o=!(i=!e?.meta?.RTK_autoBatch))&&!a&&(a=!0,u(s)),n.dispatch(e)}finally{i=!0}}})})("object"==typeof t?t:void 0)),r}),b=v(..."function"==typeof y?y(m):m());return(0,n.y$)(t,g,b)}function p(e){let t,r={},n=[],i={addCase(e,t){let n="string"==typeof e?e:e.type;if(!n)throw Error(Y(28));if(n in r)throw Error(Y(29));return r[n]=t,i},addMatcher:(e,t)=>(n.push({matcher:e,reducer:t}),i),addDefaultCase:e=>(t=e,i)};return e(i),[r,n,t]}var g=Symbol.for("rtk-slice-createasyncthunk"),y=(e=>(e.reducer="reducer",e.reducerWithPrepare="reducerWithPrepare",e.asyncThunk="asyncThunk",e))(y||{}),v=function({creators:e}={}){let t=e?.asyncThunk?.[g];return function(e){let r,{name:n,reducerPath:i=n}=e;if(!n)throw Error(Y(11));let o=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},l=Object.keys(o),s={},d={},h={},g=[],y={addCase(e,t){let r="string"==typeof e?e:e.type;if(!r)throw Error(Y(12));if(r in d)throw Error(Y(13));return d[r]=t,y},addMatcher:(e,t)=>(g.push({matcher:e,reducer:t}),y),exposeAction:(e,t)=>(h[e]=t,y),exposeCaseReducer:(e,t)=>(s[e]=t,y)};function v(){let[t={},r=[],n]="function"==typeof e.extraReducers?p(e.extraReducers):[e.extraReducers],i={...t,...d};return function(e,t){let r,[n,i,o]=p(t);if("function"==typeof e)r=()=>c(e());else{let t=c(e);r=()=>t}function l(e=r(),t){let u=[n[t.type],...i.filter(({matcher:e})=>e(t)).map(({reducer:e})=>e)];return 0===u.filter(e=>!!e).length&&(u=[o]),u.reduce((e,r)=>{if(r)if((0,a.Qx)(e)){let n=r(e,t);return void 0===n?e:n}else{if((0,a.a6)(e))return(0,a.jM)(e,e=>r(e,t));let n=r(e,t);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}return e},e)}return l.getInitialState=r,l}(e.initialState,e=>{for(let t in i)e.addCase(t,i[t]);for(let t of g)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)})}l.forEach(r=>{let i=o[r],a={reducerName:r,type:`${n}/${r}`,createNotation:"function"==typeof e.reducers};"asyncThunk"===i._reducerDefinitionType?function({type:e,reducerName:t},r,n,i){if(!i)throw Error(Y(18));let{payloadCreator:o,fulfilled:a,pending:l,rejected:u,settled:s,options:c}=r,f=i(e,o,c);n.exposeAction(t,f),a&&n.addCase(f.fulfilled,a),l&&n.addCase(f.pending,l),u&&n.addCase(f.rejected,u),s&&n.addMatcher(f.settled,s),n.exposeCaseReducer(t,{fulfilled:a||m,pending:l||m,rejected:u||m,settled:s||m})}(a,i,y,t):function({type:e,reducerName:t,createNotation:r},n,i){let o,a;if("reducer"in n){if(r&&"reducerWithPrepare"!==n._reducerDefinitionType)throw Error(Y(17));o=n.reducer,a=n.prepare}else o=n;i.addCase(e,o).exposeCaseReducer(t,o).exposeAction(t,a?u(e,a):u(e))}(a,i,y)});let b=e=>e,w=new Map,x=new WeakMap;function O(e,t){return r||(r=v()),r(e,t)}function S(){return r||(r=v()),r.getInitialState()}function P(t,r=!1){function n(e){let i=e[t];return void 0===i&&r&&(i=f(x,n,S)),i}function i(t=b){let n=f(w,r,()=>new WeakMap);return f(n,t,()=>{let n={};for(let[i,o]of Object.entries(e.selectors??{}))n[i]=function(e,t,r,n){function i(o,...a){let l=t(o);return void 0===l&&n&&(l=r()),e(l,...a)}return i.unwrapped=e,i}(o,t,()=>f(x,t,S),r);return n})}return{reducerPath:t,getSelectors:i,get selectors(){return i(n)},selectSlice:n}}let M={name:n,reducer:O,actions:h,caseReducers:s,getInitialState:S,...P(i),injectInto(e,{reducerPath:t,...r}={}){let n=t??i;return e.inject({reducerPath:n,reducer:O},r),{...M,...P(n,!0)}}};return M}}();function m(){}var b="listener",w="completed",x="cancelled",O=`task-${x}`,S=`task-${w}`,P=`${b}-${x}`,M=`${b}-${w}`,C=class{constructor(e){this.code=e,this.message=`task ${x} (reason: ${e})`}name="TaskAbortError";message},E=(e,t)=>{if("function"!=typeof e)throw TypeError(Y(32))},A=()=>{},j=(e,t=A)=>(e.catch(t),e),_=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),k=(e,t)=>{let r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},T=e=>{if(e.aborted){let{reason:t}=e;throw new C(t)}};function D(e,t){let r=A;return new Promise((n,i)=>{let o=()=>i(new C(e.reason));if(e.aborted)return void o();r=_(e,o),t.finally(()=>r()).then(n,i)}).finally(()=>{r=A})}var I=async(e,t)=>{try{await Promise.resolve();let t=await e();return{status:"ok",value:t}}catch(e){return{status:e instanceof C?"cancelled":"rejected",error:e}}finally{t?.()}},R=e=>t=>j(D(e,t).then(t=>(T(e),t))),N=e=>{let t=R(e);return e=>t(new Promise(t=>setTimeout(t,e)))},{assign:z}=Object,F={},L="listenerMiddleware",B=e=>{let{type:t,actionCreator:r,matcher:n,predicate:i,effect:o}=e;if(t)i=u(t).match;else if(r)t=r.type,i=r.match;else if(n)i=n;else if(i);else throw Error(Y(21));return E(o,"options.listener"),{predicate:i,type:t,effect:o}},V=z(e=>{let{type:t,predicate:r,effect:n}=B(e);return{id:((e=21)=>{let t="",r=e;for(;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t})(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw Error(Y(22))}}},{withTypes:()=>V}),U=(e,t)=>{let{type:r,effect:n,predicate:i}=B(t);return Array.from(e.values()).find(e=>("string"==typeof r?e.type===r:e.predicate===i)&&e.effect===n)},H=e=>{e.pending.forEach(e=>{k(e,P)})},G=(e,t,r)=>{try{e(t,r)}catch(e){setTimeout(()=>{throw e},0)}},$=z(u(`${L}/add`),{withTypes:()=>$}),K=u(`${L}/removeAll`),q=z(u(`${L}/remove`),{withTypes:()=>q}),Z=(...e)=>{console.error(`${L}/error`,...e)},W=(e={})=>{let t=new Map,{extra:r,onError:i=Z}=e;E(i,"onError");let o=e=>(e=>(e.unsubscribe=()=>t.delete(e.id),t.set(e.id,e),t=>{e.unsubscribe(),t?.cancelActive&&H(e)}))(U(t,e)??V(e));z(o,{withTypes:()=>o});let a=e=>{let r=U(t,e);return r&&(r.unsubscribe(),e.cancelActive&&H(r)),!!r};z(a,{withTypes:()=>a});let l=async(e,n,a,l)=>{let u=new AbortController,s=((e,t)=>{let r=async(r,n)=>{T(t);let i=()=>{},o=[new Promise((t,n)=>{let o=e({predicate:r,effect:(e,r)=>{r.unsubscribe(),t([e,r.getState(),r.getOriginalState()])}});i=()=>{o(),n()}})];null!=n&&o.push(new Promise(e=>setTimeout(e,n,null)));try{let e=await D(t,Promise.race(o));return T(t),e}finally{i()}};return(e,t)=>j(r(e,t))})(o,u.signal),c=[];try{e.pending.add(u),await Promise.resolve(e.effect(n,z({},a,{getOriginalState:l,condition:(e,t)=>s(e,t).then(Boolean),take:s,delay:N(u.signal),pause:R(u.signal),extra:r,signal:u.signal,fork:((e,t)=>(r,n)=>{E(r,"taskExecutor");let i=new AbortController;_(e,()=>k(i,e.reason));let o=I(async()=>{T(e),T(i.signal);let t=await r({pause:R(i.signal),delay:N(i.signal),signal:i.signal});return T(i.signal),t},()=>k(i,S));return n?.autoJoin&&t.push(o.catch(A)),{result:R(e)(o),cancel(){k(i,O)}}})(u.signal,c),unsubscribe:e.unsubscribe,subscribe:()=>{t.set(e.id,e)},cancelActiveListeners:()=>{e.pending.forEach((e,t,r)=>{e!==u&&(k(e,P),r.delete(e))})},cancel:()=>{k(u,P),e.pending.delete(u)},throwIfCancelled:()=>{T(u.signal)}})))}catch(e){e instanceof C||G(i,e,{raisedBy:"effect"})}finally{await Promise.all(c),k(u,M),e.pending.delete(u)}},u=(e=>()=>{e.forEach(H),e.clear()})(t);return{middleware:e=>r=>s=>{let c;if(!(0,n.ve)(s))return r(s);if($.match(s))return o(s.payload);if(K.match(s))return void u();if(q.match(s))return a(s.payload);let f=e.getState(),d=()=>{if(f===F)throw Error(Y(23));return f};try{if(c=r(s),t.size>0){let r=e.getState();for(let n of Array.from(t.values())){let t=!1;try{t=n.predicate(s,r,f)}catch(e){t=!1,G(i,e,{raisedBy:"predicate"})}t&&l(n,s,e,d)}}}finally{f=F}return c},startListening:o,stopListening:a,clearListeners:u}};function Y(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}Symbol.for("rtk-state-proxy-original")},5714:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var n=e=>e.tooltip},5803:(e,t,r)=>{"use strict";r.d(t,{dc:()=>l,ff:()=>a,g0:()=>u});var n=r(8924),i=r(241),o=r.n(i),a=e=>e.legend.settings,l=e=>e.legend.size,u=(0,n.Mz)([e=>e.legend.payload,a],(e,t)=>{var{itemSorter:r}=t,n=e.flat(1);return r?o()(n,r):n})},5868:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},5998:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.flatten=function(e,t=1){let r=[],n=Math.floor(t),i=(e,t)=>{for(let o=0;o<e.length;o++){let a=e[o];Array.isArray(a)&&t<n?i(a,t+1):r.push(a)}};return i(e,0),r}},6006:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if("object"!=typeof e||null==e)return!1;if(null===Object.getPrototypeOf(e))return!0;if("[object Object]"!==Object.prototype.toString.call(e)){let t=e[Symbol.toStringTag];return null!=t&&!!Object.getOwnPropertyDescriptor(e,Symbol.toStringTag)?.writable&&e.toString()===`[object ${t}]`}let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},6025:(e,t,r)=>{"use strict";r.d(t,{W:()=>b});var n=r(2115),i=r(2596),o=r(9584),a=r(1971),l=r(5306),u=r(2183),s=r(6124),c=r(1807),f=["children"],d=["dangerouslySetInnerHTML","ticks"];function h(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function g(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function y(e){var t=(0,a.j)(),r=(0,n.useMemo)(()=>{var{children:t}=e;return g(e,f)},[e]),i=(0,a.G)(e=>(0,u.Rl)(e,r.id)),o=r===i;return((0,n.useEffect)(()=>(t((0,l.Vi)(r)),()=>{t((0,l.MC)(r))}),[r,t]),o)?e.children:null}var v=e=>{var{xAxisId:t,className:r}=e,l=(0,a.G)(s.c2),f=(0,c.r)(),h="xAxis",y=(0,a.G)(e=>(0,u.iV)(e,h,t,f)),v=(0,a.G)(e=>(0,u.Zi)(e,h,t,f)),m=(0,a.G)(e=>(0,u.Lw)(e,t)),b=(0,a.G)(e=>(0,u.L$)(e,t));if(null==m||null==b)return null;var{dangerouslySetInnerHTML:w,ticks:x}=e,O=g(e,d);return n.createElement(o.u,p({},O,{scale:y,x:b.x,y:b.y,width:m.width,height:m.height,className:(0,i.$)("recharts-".concat(h," ").concat(h),r),viewBox:l,ticks:v}))},m=e=>{var t,r,i,o,a;return n.createElement(y,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(i=e.angle)?i:0,minTickGap:null!=(o=e.minTickGap)?o:5,tick:null==(a=e.tick)||a,tickFormatter:e.tickFormatter},n.createElement(v,e))};class b extends n.Component{render(){return n.createElement(m,this.props)}}h(b,"displayName","XAxis"),h(b,"defaultProps",{allowDataOverflow:u.PU.allowDataOverflow,allowDecimals:u.PU.allowDecimals,allowDuplicatedCategory:u.PU.allowDuplicatedCategory,height:u.PU.height,hide:!1,mirror:u.PU.mirror,orientation:u.PU.orientation,padding:u.PU.padding,reversed:u.PU.reversed,scale:u.PU.scale,tickCount:u.PU.tickCount,type:u.PU.type,xAxisId:0})},6115:(e,t,r)=>{"use strict";var n=r(2115),i=r(9033),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=i.useSyncExternalStore,l=n.useRef,u=n.useEffect,s=n.useMemo,c=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var f=l(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;var h=a(e,(f=s(function(){function e(e){if(!u){if(u=!0,a=e,e=n(e),void 0!==i&&d.hasValue){var t=d.value;if(i(t,e))return l=t}return l=e}if(t=l,o(a,e))return t;var r=n(e);return void 0!==i&&i(t,r)?(a=e,t):(a=e,l=r)}var a,l,u=!1,s=void 0===r?null:r;return[function(){return e(t())},null===s?void 0:function(){return e(s())}]},[t,r,n,i]))[0],f[1]);return u(function(){d.hasValue=!0,d.value=h},[h]),c(h),h}},6124:(e,t,r)=>{"use strict";r.d(t,{Ds:()=>p,HZ:()=>h,c2:()=>g});var n=r(8924),i=r(5672),o=r.n(i),a=r(5803),l=r(9827),u=r(2589),s=r(6908),c=r(4421);function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var h=(0,n.Mz)([u.Lp,u.A$,u.HK,e=>e.brush.height,s.h,s.W,a.ff,a.dc],(e,t,r,n,i,a,u,s)=>{var f=a.reduce((e,t)=>{var{orientation:r}=t;if(!t.mirror&&!t.hide){var n="number"==typeof t.width?t.width:c.tQ;return d(d({},e),{},{[r]:e[r]+n})}return e},{left:r.left||0,right:r.right||0}),h=i.reduce((e,t)=>{var{orientation:r}=t;return t.mirror||t.hide?e:d(d({},e),{},{[r]:o()(e,"".concat(r))+t.height})},{top:r.top||0,bottom:r.bottom||0}),p=d(d({},h),f),g=p.bottom;p.bottom+=n;var y=e-(p=(0,l.s0)(p,u,s)).left-p.right,v=t-p.top-p.bottom;return d(d({brushBottom:g},p),{},{width:Math.max(y,0),height:Math.max(v,0)})}),p=(0,n.Mz)(h,e=>({x:e.left,y:e.top,width:e.width,height:e.height})),g=(0,n.Mz)(u.Lp,u.A$,(e,t)=>({x:0,y:0,width:e,height:t}))},6200:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toKey=function(e){return"string"==typeof e||"symbol"==typeof e?e:Object.is(e?.valueOf?.(),-0)?"-0":String(e)}},6268:(e,t,r)=>{"use strict";r.d(t,{Kv:()=>o,N4:()=>a});var n=r(2115),i=r(1032);function o(e,t){var r,i,o;return e?"function"==typeof(i=r=e)&&(()=>{let e=Object.getPrototypeOf(i);return e.prototype&&e.prototype.isReactComponent})()||"function"==typeof r||"object"==typeof(o=r)&&"symbol"==typeof o.$$typeof&&["react.memo","react.forward_ref"].includes(o.$$typeof.description)?n.createElement(e,t):e:null}function a(e){let t={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[r]=n.useState(()=>({current:(0,i.ZR)(t)})),[o,a]=n.useState(()=>r.current.initialState);return r.current.setOptions(t=>({...t,...e,state:{...o,...e.state},onStateChange:t=>{a(t),null==e.onStateChange||e.onStateChange(t)}})),r.current}},6377:(e,t,r)=>{"use strict";r.d(t,{CG:()=>h,Dj:()=>p,Et:()=>u,F4:()=>d,M8:()=>a,NF:()=>f,Zb:()=>m,_3:()=>l,eP:()=>g,jG:()=>y,sA:()=>o,uy:()=>v,vh:()=>s});var n=r(5672),i=r.n(n),o=e=>0===e?0:e>0?1:-1,a=e=>"number"==typeof e&&e!=+e,l=e=>"string"==typeof e&&e.indexOf("%")===e.length-1,u=e=>("number"==typeof e||e instanceof Number)&&!a(e),s=e=>u(e)||"string"==typeof e,c=0,f=e=>{var t=++c;return"".concat(e||"").concat(t)},d=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!u(e)&&"string"!=typeof e)return n;if(l(e)){if(null==t)return n;var o=e.indexOf("%");r=t*parseFloat(e.slice(0,o))/100}else r=+e;return a(r)&&(r=n),i&&null!=t&&r>t&&(r=t),r},h=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++)if(r[e[n]])return!0;else r[e[n]]=!0;return!1},p=(e,t)=>u(e)&&u(t)?r=>e+r*(t-e):()=>t;function g(e,t,r){if(e&&e.length)return e.find(e=>e&&("function"==typeof t?t(e):i()(e,t))===r)}var y=e=>{if(!e||!e.length)return null;for(var t=e.length,r=0,n=0,i=0,o=0,a=1/0,l=-1/0,u=0,s=0,c=0;c<t;c++)u=e[c].cx||0,s=e[c].cy||0,r+=u,n+=s,i+=u*s,o+=u*u,a=Math.min(a,u),l=Math.max(l,u);var f=t*o!=r*r?(t*i-r*n)/(t*o-r*r):0;return{xmin:a,xmax:l,a:f,b:(n-f*r)/t}},v=e=>null==e,m=e=>v(e)?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1))},6523:(e,t,r)=>{"use strict";r.d(t,{$g:()=>a,Hw:()=>o,Td:()=>u,au:()=>l,xH:()=>i});var n=r(1971),i=e=>e.options.defaultTooltipEventType,o=e=>e.options.validateTooltipEventTypes;function a(e,t,r){if(null==e)return t;var n=e?"axis":"item";return null==r?t:r.includes(n)?n:t}function l(e,t){return a(t,i(e),o(e))}function u(e){return(0,n.G)(t=>l(t,e))}},6605:(e,t,r)=>{"use strict";r.d(t,{P:()=>s});var n=r(1643);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var a={widthCache:{},cacheCount:0},l={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},u="recharts_measurement_span",s=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||n.m.isSsr)return{width:0,height:0};var i=(Object.keys(t=o({},r)).forEach(e=>{t[e]||delete t[e]}),t),s=JSON.stringify({text:e,copyStyle:i});if(a.widthCache[s])return a.widthCache[s];try{var c=document.getElementById(u);c||((c=document.createElement("span")).setAttribute("id",u),c.setAttribute("aria-hidden","true"),document.body.appendChild(c));var f=o(o({},l),i);Object.assign(c.style,f),c.textContent="".concat(e);var d=c.getBoundingClientRect(),h={width:d.width,height:d.height};return a.widthCache[s]=h,++a.cacheCount>2e3&&(a.cacheCount=0,a.widthCache={}),h}catch(e){return{width:0,height:0}}}},6633:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPrimitive=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e}},6641:(e,t,r)=>{"use strict";r.d(t,{dl:()=>u,lJ:()=>l,uN:()=>o});var n=r(5710),i=r(6377);function o(e,t){if(t){var r=Number.parseInt(t,10);if(!(0,i.M8)(r))return null==e?void 0:e[r]}}var a=(0,n.Z0)({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:e=>{null==e.eventEmitter&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),l=a.reducer,{createEventEmitter:u}=a.actions},6670:(e,t,r)=>{"use strict";r.d(t,{x:()=>n});var n=e=>e.options.tooltipPayloadSearcher},6752:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var n=r(1971),i=()=>(0,n.G)(e=>e.rootProps.accessibilityLayer)},6850:(e,t,r)=>{"use strict";r.d(t,{l3:()=>v,m7:()=>m});var n=r(2115),i=r(1971),o=r(8478),a=new(r(2661)),l="recharts.syncEvent.tooltip",u="recharts.syncEvent.brush",s=r(6641),c=r(4890),f=r(4732),d=r(215);function h(e){return e.tooltip.syncInteraction}var p=r(7238),g=r(4487),y=()=>{};function v(){var e,t,r,f,h,v,m,b,w,x,O,S=(0,i.j)();(0,n.useEffect)(()=>{S((0,s.dl)())},[S]),e=(0,i.G)(o.lZ),t=(0,i.G)(o.pH),r=(0,i.j)(),f=(0,i.G)(o.hX),h=(0,i.G)(d.R4),v=(0,p.WX)(),m=(0,p.sk)(),b=(0,i.G)(e=>e.rootProps.className),(0,n.useEffect)(()=>{if(null==e)return y;var n=(n,i,o)=>{if(t!==o&&e===n){if("index"===f)return void r(i);if(null!=h){if("function"==typeof f){var a,l=f(h,{activeTooltipIndex:null==i.payload.index?void 0:Number(i.payload.index),isTooltipActive:i.payload.active,activeIndex:null==i.payload.index?void 0:Number(i.payload.index),activeLabel:i.payload.label,activeDataKey:i.payload.dataKey,activeCoordinate:i.payload.coordinate});a=h[l]}else"value"===f&&(a=h.find(e=>String(e.value)===i.payload.label));var{coordinate:u}=i.payload;if(null==a||!1===i.payload.active||null==u||null==m)return void r((0,c.E1)({active:!1,coordinate:void 0,dataKey:void 0,index:null,label:void 0}));var{x:s,y:d}=u,p=Math.min(s,m.x+m.width),g=Math.min(d,m.y+m.height),y={x:"horizontal"===v?a.coordinate:p,y:"horizontal"===v?g:a.coordinate};r((0,c.E1)({active:i.payload.active,coordinate:y,dataKey:i.payload.dataKey,index:String(a.index),label:i.payload.label}))}}};return a.on(l,n),()=>{a.off(l,n)}},[b,r,t,e,f,h,v,m]),w=(0,i.G)(o.lZ),x=(0,i.G)(o.pH),O=(0,i.j)(),(0,n.useEffect)(()=>{if(null==w)return y;var e=(e,t,r)=>{x!==r&&w===e&&O((0,g.M)(t))};return a.on(u,e),()=>{a.off(u,e)}},[O,x,w])}function m(e,t,r,u,s,d){var p=(0,i.G)(r=>(0,f.dp)(r,e,t)),g=(0,i.G)(o.pH),y=(0,i.G)(o.lZ),v=(0,i.G)(o.hX),m=(0,i.G)(h),b=null==m?void 0:m.active;(0,n.useEffect)(()=>{if(!b&&null!=y&&null!=g){var e=(0,c.E1)({active:d,coordinate:r,dataKey:p,index:s,label:"number"==typeof u?String(u):u});a.emit(l,y,e,g)}},[b,r,p,s,u,g,y,v,d])}},6908:(e,t,r)=>{"use strict";r.d(t,{W:()=>o,h:()=>i});var n=r(8924),i=(0,n.Mz)(e=>e.cartesianAxis.xAxis,e=>Object.values(e)),o=(0,n.Mz)(e=>e.cartesianAxis.yAxis,e=>Object.values(e))},6932:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},7040:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isUnsafeProperty=function(e){return"__proto__"===e}},7062:(e,t,r)=>{"use strict";r.d(t,{Be:()=>y,Cv:()=>O,D0:()=>P,Gl:()=>v,Dc:()=>S});var n=r(8924),i=r(2589),o=r(6124),a=r(5641),l=r(6377),u={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},s={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},c=r(8190),f=r(7238),d={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:u.angleAxisId,includeHidden:!1,name:void 0,reversed:u.reversed,scale:u.scale,tick:u.tick,tickCount:void 0,ticks:void 0,type:u.type,unit:void 0},h={allowDataOverflow:s.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:s.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:s.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:s.scale,tick:s.tick,tickCount:s.tickCount,ticks:void 0,type:s.type,unit:void 0},p={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:u.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:u.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:u.scale,tick:u.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},g={allowDataOverflow:s.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:s.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:s.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:s.scale,tick:s.tick,tickCount:s.tickCount,ticks:void 0,type:"category",unit:void 0},y=(e,t)=>null!=e.polarAxis.angleAxis[t]?e.polarAxis.angleAxis[t]:"radial"===e.layout.layoutType?p:d,v=(e,t)=>null!=e.polarAxis.radiusAxis[t]?e.polarAxis.radiusAxis[t]:"radial"===e.layout.layoutType?g:h,m=e=>e.polarOptions,b=(0,n.Mz)([i.Lp,i.A$,o.HZ],a.lY),w=(0,n.Mz)([m,b],(e,t)=>{if(null!=e)return(0,l.F4)(e.innerRadius,t,0)}),x=(0,n.Mz)([m,b],(e,t)=>{if(null!=e)return(0,l.F4)(e.outerRadius,t,.8*t)}),O=(0,n.Mz)([m],e=>{if(null==e)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]});(0,n.Mz)([y,O],c.I);var S=(0,n.Mz)([b,w,x],(e,t,r)=>{if(null!=e&&null!=t&&null!=r)return[t,r]});(0,n.Mz)([v,S],c.I);var P=(0,n.Mz)([f.fz,m,w,x,i.Lp,i.A$],(e,t,r,n,i,o)=>{if(("centric"===e||"radial"===e)&&null!=t&&null!=r&&null!=n){var{cx:a,cy:u,startAngle:s,endAngle:c}=t;return{cx:(0,l.F4)(a,i,i/2),cy:(0,l.F4)(u,o,o/2),innerRadius:r,outerRadius:n,startAngle:s,endAngle:c,clockWise:!1}}})},7064:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(5181),i=r(1551),o=r(4072);t.orderBy=function(e,t,r,a){if(null==e)return[];r=a?void 0:r,Array.isArray(e)||(e=Object.values(e)),Array.isArray(t)||(t=null==t?[null]:[t]),0===t.length&&(t=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map(e=>String(e));let l=(e,t)=>{let r=e;for(let e=0;e<t.length&&null!=r;++e)r=r[t[e]];return r},u=t.map(e=>(Array.isArray(e)&&1===e.length&&(e=e[0]),null==e||"function"==typeof e||Array.isArray(e)||i.isKey(e))?e:{key:e,path:o.toPath(e)});return e.map(e=>({original:e,criteria:u.map(t=>{var r,n;return r=t,null==(n=e)||null==r?n:"object"==typeof r&&"key"in r?Object.hasOwn(n,r.key)?n[r.key]:l(n,r.path):"function"==typeof r?r(n):Array.isArray(r)?l(n,r):"object"==typeof n?n[r]:n})})).slice().sort((e,t)=>{for(let i=0;i<u.length;i++){let o=n.compareValues(e.criteria[i],t.criteria[i],r[i]);if(0!==o)return o}return 0}).map(e=>e.original)}},7086:(e,t,r)=>{"use strict";r.d(t,{m:()=>en});var n=r(2115),i=r(7650),o=r(241),a=r.n(o),l=r(2596),u=r(6377);function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return Array.isArray(e)&&(0,u.vh)(e[0])&&(0,u.vh)(e[1])?e.join(" ~ "):e}var h=e=>{var{separator:t=" : ",contentStyle:r={},itemStyle:i={},labelStyle:o={},payload:c,formatter:h,itemSorter:p,wrapperClassName:g,labelClassName:y,label:v,labelFormatter:m,accessibilityLayer:b=!1}=e,w=f({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),x=f({margin:0},o),O=!(0,u.uy)(v),S=O?v:"",P=(0,l.$)("recharts-default-tooltip",g),M=(0,l.$)("recharts-tooltip-label",y);return O&&m&&null!=c&&(S=m(v,c)),n.createElement("div",s({className:P,style:w},b?{role:"status","aria-live":"assertive"}:{}),n.createElement("p",{className:M,style:x},n.isValidElement(S)?S:"".concat(S)),(()=>{if(c&&c.length){var e=(p?a()(c,p):c).map((e,r)=>{if("none"===e.type)return null;var o=e.formatter||h||d,{value:a,name:l}=e,s=a,p=l;if(o){var g=o(a,l,e,r,c);if(Array.isArray(g))[s,p]=g;else{if(null==g)return null;s=g}}var y=f({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},i);return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(r),style:y},(0,u.vh)(p)?n.createElement("span",{className:"recharts-tooltip-item-name"},p):null,(0,u.vh)(p)?n.createElement("span",{className:"recharts-tooltip-item-separator"},t):null,n.createElement("span",{className:"recharts-tooltip-item-value"},s),n.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null})())},p="recharts-tooltip-wrapper",g={visibility:"hidden"};function y(e){var{allowEscapeViewBox:t,coordinate:r,key:n,offsetTopLeft:i,position:o,reverseDirection:a,tooltipDimension:l,viewBox:s,viewBoxDimension:c}=e;if(o&&(0,u.Et)(o[n]))return o[n];var f=r[n]-l-(i>0?i:0),d=r[n]+i;if(t[n])return a[n]?f:d;var h=s[n];return null==h?0:a[n]?f<h?Math.max(d,h):Math.max(f,h):null==c?0:d+l>h+c?Math.max(f,h):Math.max(d,h)}function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach(function(t){b(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function b(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class w extends n.PureComponent{componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var e,t;this.state.dismissed&&((null==(e=this.props.coordinate)?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null==(t=this.props.coordinate)?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:e,allowEscapeViewBox:t,animationDuration:r,animationEasing:i,children:o,coordinate:a,hasPayload:s,isAnimationActive:c,offset:f,position:d,reverseDirection:h,useTranslate3d:v,viewBox:b,wrapperStyle:w,lastBoundingBox:x,innerRef:O,hasPortalFromProps:S}=this.props,{cssClasses:P,cssProperties:M}=function(e){var t,r,n,{allowEscapeViewBox:i,coordinate:o,offsetTopLeft:a,position:s,reverseDirection:c,tooltipBox:f,useTranslate3d:d,viewBox:h}=e;return{cssProperties:t=f.height>0&&f.width>0&&o?function(e){var{translateX:t,translateY:r,useTranslate3d:n}=e;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}({translateX:r=y({allowEscapeViewBox:i,coordinate:o,key:"x",offsetTopLeft:a,position:s,reverseDirection:c,tooltipDimension:f.width,viewBox:h,viewBoxDimension:h.width}),translateY:n=y({allowEscapeViewBox:i,coordinate:o,key:"y",offsetTopLeft:a,position:s,reverseDirection:c,tooltipDimension:f.height,viewBox:h,viewBoxDimension:h.height}),useTranslate3d:d}):g,cssClasses:function(e){var{coordinate:t,translateX:r,translateY:n}=e;return(0,l.$)(p,{["".concat(p,"-right")]:(0,u.Et)(r)&&t&&(0,u.Et)(t.x)&&r>=t.x,["".concat(p,"-left")]:(0,u.Et)(r)&&t&&(0,u.Et)(t.x)&&r<t.x,["".concat(p,"-bottom")]:(0,u.Et)(n)&&t&&(0,u.Et)(t.y)&&n>=t.y,["".concat(p,"-top")]:(0,u.Et)(n)&&t&&(0,u.Et)(t.y)&&n<t.y})}({translateX:r,translateY:n,coordinate:o})}}({allowEscapeViewBox:t,coordinate:a,offsetTopLeft:f,position:d,reverseDirection:h,tooltipBox:{height:x.height,width:x.width},useTranslate3d:v,viewBox:b}),C=S?{}:m(m({transition:c&&e?"transform ".concat(r,"ms ").concat(i):void 0},M),{},{pointerEvents:"none",visibility:!this.state.dismissed&&e&&s?"visible":"hidden",position:"absolute",top:0,left:0}),E=m(m({},C),{},{visibility:!this.state.dismissed&&e&&s?"visible":"hidden"},w);return n.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:P,style:E,ref:O},o)}constructor(){super(...arguments),b(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),b(this,"handleKeyDown",e=>{if("Escape"===e.key){var t,r,n,i;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(t=null==(r=this.props.coordinate)?void 0:r.x)?t:0,y:null!=(n=null==(i=this.props.coordinate)?void 0:i.y)?n:0}})}})}}var x=r(1643),O=r(2494),S=r(7238),P=r(6752),M=r(7918),C=r(688),E=r(788),A=["x","y","top","left","width","height","className"];function j(){return(j=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function _(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var k=e=>{var{x:t=0,y:r=0,top:i=0,left:o=0,width:a=0,height:s=0,className:c}=e,f=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:t,y:r,top:i,left:o,width:a,height:s},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,A));return(0,u.Et)(t)&&(0,u.Et)(r)&&(0,u.Et)(a)&&(0,u.Et)(s)&&(0,u.Et)(i)&&(0,u.Et)(o)?n.createElement("path",j({},(0,E.J9)(f,!0),{className:(0,l.$)("recharts-cross",c),d:"M".concat(t,",").concat(i,"v").concat(s,"M").concat(o,",").concat(r,"h").concat(a)})):null},T=r(4538),D=r(5641);function I(e){var{cx:t,cy:r,radius:n,startAngle:i,endAngle:o}=e;return{points:[(0,D.IZ)(t,r,n,i),(0,D.IZ)(t,r,n,o)],cx:t,cy:r,radius:n,startAngle:i,endAngle:o}}var R=r(7283),N=r(1971),z=r(9827),F=r(215);function L(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function B(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?L(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):L(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var V=r(4732);function U(){return(U=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function H(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function G(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?H(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):H(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function $(e){var t,r,i,{coordinate:o,payload:a,index:u,offset:s,tooltipAxisBandSize:c,layout:f,cursor:d,tooltipEventType:h,chartName:p}=e;if(!d||!o||"ScatterChart"!==p&&"axis"!==h)return null;if("ScatterChart"===p)r=o,i=k;else if("BarChart"===p)t=c/2,r={stroke:"none",fill:"#ccc",x:"horizontal"===f?o.x-t:s.left+.5,y:"horizontal"===f?s.top+.5:o.y-t,width:"horizontal"===f?c:s.width-1,height:"horizontal"===f?s.height-1:c},i=T.M;else if("radial"===f){var{cx:g,cy:y,radius:v,startAngle:m,endAngle:b}=I(o);r={cx:g,cy:y,startAngle:m,endAngle:b,innerRadius:v,outerRadius:v},i=R.h}else r={points:function(e,t,r){var n,i,o,a;if("horizontal"===e)o=n=t.x,i=r.top,a=r.top+r.height;else if("vertical"===e)a=i=t.y,n=r.left,o=r.left+r.width;else if(null!=t.cx&&null!=t.cy)if("centric"!==e)return I(t);else{var{cx:l,cy:u,innerRadius:s,outerRadius:c,angle:f}=t,d=(0,D.IZ)(l,u,s,f),h=(0,D.IZ)(l,u,c,f);n=d.x,i=d.y,o=h.x,a=h.y}return[{x:n,y:i},{x:o,y:a}]}(f,o,s)},i=C.I;var w="object"==typeof d&&"className"in d?d.className:void 0,x=G(G(G(G({stroke:"#ccc",pointerEvents:"none"},s),r),(0,E.J9)(d,!1)),{},{payload:a,payloadIndex:u,className:(0,l.$)("recharts-tooltip-cursor",w)});return(0,n.isValidElement)(d)?(0,n.cloneElement)(d,x):(0,n.createElement)(i,x)}function K(e){var t,r,i,o=(t=(0,N.G)(F.Dn),r=(0,N.G)(F.R4),i=(0,N.G)(F.fl),(0,z.Hj)(B(B({},t),{},{scale:i}),r)),a=(0,S.W7)(),l=(0,S.WX)(),u=(0,V.fW)();return n.createElement($,U({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:a,layout:l,tooltipAxisBandSize:o,chartName:u}))}var q=r(5115),Z=r(4890),W=r(6850),Y=r(6523),X=r(3389);function J(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Q(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?J(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):J(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ee(e){return e.dataKey}var et=[],er={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!x.m.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function en(e){var t,r=(0,X.e)(e,er),{active:o,allowEscapeViewBox:a,animationDuration:l,animationEasing:u,content:s,filterNull:c,isAnimationActive:f,offset:d,payloadUniqBy:p,position:g,reverseDirection:y,useTranslate3d:v,wrapperStyle:m,cursor:b,shared:x,trigger:C,defaultIndex:E,portal:A,axisId:j}=r,_=(0,N.j)(),k="number"==typeof E?String(E):E;(0,n.useEffect)(()=>{_((0,Z.UF)({shared:x,trigger:C,axisId:j,active:o,defaultIndex:k}))},[_,x,C,j,o,k]);var T=(0,S.sk)(),D=(0,P.$)(),I=(0,Y.Td)(x),{activeIndex:R,isActive:z}=(0,N.G)(e=>(0,V.yn)(e,I,C,k)),F=(0,N.G)(e=>(0,V.u9)(e,I,C,k)),L=(0,N.G)(e=>(0,V.BZ)(e,I,C,k)),B=(0,N.G)(e=>(0,V.dS)(e,I,C,k)),U=(0,q.X)(),H=null!=o?o:z,[G,$]=(0,M.V)([F,H]),J="axis"===I?L:void 0;(0,W.m7)(I,C,B,J,R,H);var en=null!=A?A:U;if(null==en)return null;var ei=null!=F?F:et;H||(ei=et),c&&ei.length&&(ei=(0,O.s)(F.filter(e=>null!=e.value&&(!0!==e.hide||r.includeHidden)),p,ee));var eo=ei.length>0,ea=n.createElement(w,{allowEscapeViewBox:a,animationDuration:l,animationEasing:u,isAnimationActive:f,active:H,coordinate:B,hasPayload:eo,offset:d,position:g,reverseDirection:y,useTranslate3d:v,viewBox:T,wrapperStyle:m,lastBoundingBox:G,innerRef:$,hasPortalFromProps:!!A},(t=Q(Q({},r),{},{payload:ei,label:J,active:H,coordinate:B,accessibilityLayer:D}),n.isValidElement(s)?n.cloneElement(s,t):"function"==typeof s?n.createElement(s,t):n.createElement(h,t)));return n.createElement(n.Fragment,null,(0,i.createPortal)(ea,en),H&&n.createElement(K,{cursor:b,tooltipEventType:I,coordinate:B,payload:F,index:R}))}},7238:(e,t,r)=>{"use strict";r.d(t,{Kp:()=>p,W7:()=>c,WX:()=>y,fz:()=>g,rY:()=>d,sk:()=>u,yi:()=>f}),r(2115);var n=r(1971),i=r(6124),o=r(2589),a=r(1807),l=r(972),u=()=>{var e,t=(0,a.r)(),r=(0,n.G)(i.Ds),o=(0,n.G)(l.U),u=null==(e=(0,n.G)(l.C))?void 0:e.padding;return t&&o&&u?{width:o.width-u.left-u.right,height:o.height-u.top-u.bottom,x:u.left,y:u.top}:r},s={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},c=()=>{var e;return null!=(e=(0,n.G)(i.HZ))?e:s},f=()=>(0,n.G)(o.Lp),d=()=>(0,n.G)(o.A$),h={top:0,right:0,bottom:0,left:0},p=()=>{var e;return null!=(e=(0,n.G)(e=>e.layout.margin))?e:h},g=e=>e.layout.layoutType,y=()=>(0,n.G)(g)},7258:(e,t,r)=>{"use strict";r.d(t,{X:()=>eF,J:()=>eD});var n=r(2115),i=r(2596),o=r(2348),a=r(8080),l=r.n(a),u=r(379),s=r(788),c=r(9827),f=r(6377),d=["valueAccessor"],h=["data","dataKey","clockWise","id","textBreakAll"];function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function v(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var m=e=>Array.isArray(e.value)?l()(e.value):e.value;function b(e){var{valueAccessor:t=m}=e,r=v(e,d),{data:i,dataKey:a,clockWise:l,id:g,textBreakAll:b}=r,w=v(r,h);return i&&i.length?n.createElement(o.W,{className:"recharts-label-list"},i.map((e,r)=>{var i=(0,f.uy)(a)?t(e,r):(0,c.kr)(e&&e.payload,a),o=(0,f.uy)(g)?{}:{id:"".concat(g,"-").concat(r)};return n.createElement(u.J,p({},(0,s.J9)(e,!0),w,o,{parentViewBox:e.parentViewBox,value:i,textBreakAll:b,viewBox:u.J.parseViewBox((0,f.uy)(l)?e:y(y({},e),{},{clockWise:l})),key:"label-".concat(r),index:r}))})):null}b.displayName="LabelList",b.renderCallByParent=function(e,t){var r,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&i&&!e.label)return null;var{children:o}=e,a=(0,s.aS)(o,b).map((e,r)=>(0,n.cloneElement)(e,{data:t,key:"labelList-".concat(r)}));return i?[(r=e.label,r?!0===r?n.createElement(b,{key:"labelList-implicit",data:t}):n.isValidElement(r)||(0,u.Z)(r)?n.createElement(b,{key:"labelList-implicit",data:t,content:r}):"object"==typeof r?n.createElement(b,p({data:t},r,{key:"labelList-implicit"})):null:null),...a]:a};var w=r(1643),x=r(5306),O=r(1971),S=r(2183);function P(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function M(e){var t=(0,O.j)();return(0,n.useEffect)(()=>(t((0,x.D4)(e)),()=>{t((0,x.Gc)(e))}),[e,t]),null}class C extends n.Component{render(){return n.createElement(M,{domain:this.props.domain,id:this.props.zAxisId,dataKey:this.props.dataKey,name:this.props.name,unit:this.props.unit,range:this.props.range,scale:this.props.scale,type:this.props.type,allowDuplicatedCategory:S.N8.allowDuplicatedCategory,allowDataOverflow:S.N8.allowDataOverflow,reversed:S.N8.reversed,includeHidden:S.N8.includeHidden})}}P(C,"displayName","ZAxis"),P(C,"defaultProps",{zAxisId:0,range:S.N8.range,scale:S.N8.scale,type:S.N8.type});var E=r(688),A=e=>null;A.displayName="Cell";var j=r(3597),_=r(9795),k=r(931),T=r.n(k),D=r(4538),I=r(3389),R=r(4460);function N(){return(N=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var z=(e,t,r,n,i)=>{var o=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-o/2,",").concat(t+i)+"L ".concat(e+r-o/2-n,",").concat(t+i)+"L ".concat(e,",").concat(t," Z")},F={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},L=e=>{var t=(0,I.e)(e,F),r=(0,n.useRef)(),[o,a]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&a(e)}catch(e){}},[]);var{x:l,y:u,upperWidth:c,lowerWidth:f,height:d,className:h}=t,{animationEasing:p,animationDuration:g,animationBegin:y,isUpdateAnimationActive:v}=t;if(l!==+l||u!==+u||c!==+c||f!==+f||d!==+d||0===c&&0===f||0===d)return null;var m=(0,i.$)("recharts-trapezoid",h);return v?n.createElement(R.i,{canBegin:o>0,from:{upperWidth:0,lowerWidth:0,height:d,x:l,y:u},to:{upperWidth:c,lowerWidth:f,height:d,x:l,y:u},duration:g,animationEasing:p,isActive:v},e=>{var{upperWidth:i,lowerWidth:a,height:l,x:u,y:c}=e;return n.createElement(R.i,{canBegin:o>0,from:"0px ".concat(-1===o?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:g,easing:p},n.createElement("path",N({},(0,s.J9)(t,!0),{className:m,d:z(u,c,i,a,l),ref:r})))}):n.createElement("g",null,n.createElement("path",N({},(0,s.J9)(t,!0),{className:m,d:z(l,u,c,f,d)})))},B=r(7283),V=["option","shapeType","propTransformer","activeClassName","isActive"];function U(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function H(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?U(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function G(e,t){return H(H({},t),e)}function $(e){var{shapeType:t,elementProps:r}=e;switch(t){case"rectangle":return n.createElement(D.M,r);case"trapezoid":return n.createElement(L,r);case"sector":return n.createElement(B.h,r);case"symbols":if("symbols"===t)return n.createElement(_.i,r);break;default:return null}}function K(e){var t,{option:r,shapeType:i,propTransformer:a=G,activeClassName:l="recharts-active-shape",isActive:u}=e,s=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,V);if((0,n.isValidElement)(r))t=(0,n.cloneElement)(r,H(H({},s),(0,n.isValidElement)(r)?r.props:r));else if("function"==typeof r)t=r(s);else if(T()(r)&&"boolean"!=typeof r){var c=a(r,s);t=n.createElement($,{shapeType:i,elementProps:c})}else t=n.createElement($,{shapeType:i,elementProps:s});return u?n.createElement(o.W,{className:l},t):t}var q=["option","isActive"];function Z(){return(Z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function W(e){var{option:t,isActive:r}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,q);return"string"==typeof t?n.createElement(K,Z({option:n.createElement(_.i,Z({type:t},i)),isActive:r,shapeType:"symbols"},i)):n.createElement(K,Z({option:t,isActive:r,shapeType:"symbols"},i))}var Y=r(4890),X=r(1807);function J(e){var{fn:t,args:r}=e,i=(0,O.j)(),o=(0,X.r)();return(0,n.useEffect)(()=>{if(!o){var e=t(r);return i((0,Y.Ix)(e)),()=>{i((0,Y.XB)(e))}}},[t,r,i,o]),null}var Q=r(2248);function ee(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function et(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ee(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ee(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function er(e){var t=(0,O.j)(),r=(0,n.useRef)(null);return(0,n.useEffect)(()=>{var n=et(et({},e),{},{stackId:(0,c.$8)(e.stackId)});null===r.current?t((0,Q.g5)(n)):r.current!==n&&t((0,Q.ZF)({prev:r.current,next:n})),r.current=n},[t,e]),(0,n.useEffect)(()=>()=>{r.current&&(t((0,Q.Vi)(r.current)),r.current=null)},[t]),null}var en=["children"],ei=()=>{},eo=(0,n.createContext)({addErrorBar:ei,removeErrorBar:ei}),ea=(0,n.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function el(e){var{children:t}=e,r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,en);return n.createElement(ea.Provider,{value:r},t)}var eu=e=>{var{children:t,xAxisId:r,yAxisId:i,zAxisId:o,dataKey:a,data:l,stackId:u,hide:s,type:c,barSize:f}=e,[d,h]=n.useState([]),p=(0,n.useCallback)(e=>{h(t=>[...t,e])},[h]),g=(0,n.useCallback)(e=>{h(t=>t.filter(t=>t!==e))},[h]),y=(0,X.r)();return n.createElement(eo.Provider,{value:{addErrorBar:p,removeErrorBar:g}},n.createElement(er,{type:c,data:l,xAxisId:r,yAxisId:i,zAxisId:o,dataKey:a,errorBars:d,stackId:u,hide:s,barSize:f,isPanorama:y}),t)},es=r(8234);function ec(e,t){var r,n,i=(0,O.G)(t=>(0,S.Rl)(t,e)),o=(0,O.G)(e=>(0,S.sf)(e,t)),a=null!=(r=null==i?void 0:i.allowDataOverflow)?r:S.PU.allowDataOverflow,l=null!=(n=null==o?void 0:o.allowDataOverflow)?n:S.cd.allowDataOverflow;return{needClip:a||l,needClipX:a,needClipY:l}}function ef(e){var{xAxisId:t,yAxisId:r,clipPathId:i}=e,o=(0,es.oM)(),{needClipX:a,needClipY:l,needClip:u}=ec(t,r);if(!u)return null;var{x:s,y:c,width:f,height:d}=o;return n.createElement("clipPath",{id:"clipPath-".concat(i)},n.createElement("rect",{x:a?s:s-f/2,y:l?c:c-d/2,width:a?f:2*f,height:l?d:2*d}))}var ed=r(8924),eh=r(356),ep=(0,ed.Mz)([S.ld,(e,t,r,n,i)=>i],(e,t)=>{if(e.some(e=>"scatter"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),eg=(0,ed.Mz)([(e,t,r,n,i,o,a)=>(0,eh.HS)(e,t,r,a),(e,t,r,n,i,o,a)=>(0,S.Gx)(e,"xAxis",t,a),(e,t,r,n,i,o,a)=>(0,S.CR)(e,"xAxis",t,a),(e,t,r,n,i,o,a)=>(0,S.Gx)(e,"yAxis",r,a),(e,t,r,n,i,o,a)=>(0,S.CR)(e,"yAxis",r,a),(e,t,r,n)=>(0,S.Y)(e,"zAxis",n,!1),ep,(e,t,r,n,i,o)=>o],(e,t,r,n,i,o,a,l)=>{var u,{chartData:s,dataStartIndex:c,dataEndIndex:f}=e;if(null!=a&&null!=(u=(null==a?void 0:a.data)!=null&&a.data.length>0?a.data:null==s?void 0:s.slice(c,f+1))&&null!=t&&null!=n&&null!=r&&null!=i&&(null==r?void 0:r.length)!==0&&(null==i?void 0:i.length)!==0)return eD({displayedData:u,xAxis:t,yAxis:n,zAxis:o,scatterSettings:a,xAxisTicks:r,yAxisTicks:i,cells:l})}),ey=r(215),ev=r(2634),em=()=>{};function eb(e){var{legendPayload:t}=e,r=(0,O.j)(),i=(0,X.r)();return(0,n.useEffect)(()=>i?em:(r((0,ev.Lx)(t)),()=>{r((0,ev.u3)(t))}),[r,i,t]),null}var ew=r(4421),ex=["onMouseEnter","onClick","onMouseLeave"],eO=["animationBegin","animationDuration","animationEasing","hide","isAnimationActive","legendType","lineJointType","lineType","shape","xAxisId","yAxisId","zAxisId"];function eS(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function eP(){return(eP=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function eM(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eC(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eM(Object(r),!0).forEach(function(t){eE(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eM(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function eE(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eA(e){var t,r,{points:i,props:a}=e,{line:l,lineType:u,lineJointType:c}=a;if(!l)return null;var d=(0,s.J9)(a,!1),h=(0,s.J9)(l,!1);if("joint"===u)t=i.map(e=>({x:e.cx,y:e.cy}));else if("fitting"===u){var{xmin:p,xmax:g,a:y,b:v}=(0,f.jG)(i);t=[{x:p,y:y*p+v},{x:g,y:y*g+v}]}var m=eC(eC(eC({},d),{},{fill:"none",stroke:d&&d.fill},h),{},{points:t});return r=n.isValidElement(l)?n.cloneElement(l,m):"function"==typeof l?l(m):n.createElement(E.I,eP({},m,{type:c})),n.createElement(o.W,{className:"recharts-scatter-line",key:"recharts-scatter-line"},r)}function ej(e){var t,r,i,a,l,{points:u,showLabels:c,allOtherScatterProps:f}=e,{shape:d,activeShape:h,dataKey:p}=f,g=(0,s.J9)(f,!1),y=(0,O.G)(ey.A2),{onMouseEnter:v,onClick:m,onMouseLeave:w}=f,x=eS(f,ex),S=(t=f.dataKey,r=(0,O.j)(),(e,n)=>i=>{null==v||v(e,n,i),r((0,Y.RD)({activeIndex:String(n),activeDataKey:t,activeCoordinate:e.tooltipPosition}))}),P=(i=(0,O.j)(),(e,t)=>r=>{null==w||w(e,t,r),i((0,Y.oP)())}),M=(a=f.dataKey,l=(0,O.j)(),(e,t)=>r=>{null==m||m(e,t,r),l((0,Y.ML)({activeIndex:String(t),activeDataKey:a,activeCoordinate:e.tooltipPosition}))});return null==u?null:n.createElement(n.Fragment,null,n.createElement(eA,{points:u,props:f}),u.map((e,t)=>{var r=h&&y===String(t),i=r?h:d,a=eC(eC(eC({key:"symbol-".concat(t)},g),e),{},{[ew.F0]:t,[ew.um]:String(p)});return n.createElement(o.W,eP({className:"recharts-scatter-symbol"},(0,j.XC)(x,e,t),{onMouseEnter:S(e,t),onMouseLeave:P(e,t),onClick:M(e,t),key:"symbol-".concat(null==e?void 0:e.cx,"-").concat(null==e?void 0:e.cy,"-").concat(null==e?void 0:e.size,"-").concat(t)}),n.createElement(W,eP({option:i,isActive:r},a)))}),c&&b.renderCallByParent(f,u))}function e_(e){var{previousPointsRef:t,props:r}=e,{points:i,isAnimationActive:a,animationBegin:l,animationDuration:u,animationEasing:s}=r,c=t.current,d=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",r=(0,n.useRef)((0,f.NF)(t)),i=(0,n.useRef)(e);return i.current!==e&&(r.current=(0,f.NF)(t),i.current=e),r.current}(r,"recharts-scatter-"),[h,p]=(0,n.useState)(!1),g=(0,n.useCallback)(()=>{p(!1)},[]),y=(0,n.useCallback)(()=>{p(!0)},[]);return n.createElement(R.i,{begin:l,duration:u,isActive:a,easing:s,from:{t:0},to:{t:1},onAnimationEnd:g,onAnimationStart:y,key:d},e=>{var{t:a}=e,l=1===a?i:i.map((e,t)=>{var r=c&&c[t];if(r){var n=(0,f.Dj)(r.cx,e.cx),i=(0,f.Dj)(r.cy,e.cy),o=(0,f.Dj)(r.size,e.size);return eC(eC({},e),{},{cx:n(a),cy:i(a),size:o(a)})}var l=(0,f.Dj)(0,e.size);return eC(eC({},e),{},{size:l(a)})});return a>0&&(t.current=l),n.createElement(o.W,null,n.createElement(ej,{points:l,allOtherScatterProps:r,showLabels:!h}))})}function ek(e){var{points:t,isAnimationActive:r}=e,i=(0,n.useRef)(null),o=i.current;return r&&t&&t.length&&(!o||o!==t)?n.createElement(e_,{props:e,previousPointsRef:i}):n.createElement(ej,{points:t,allOtherScatterProps:e,showLabels:!0})}function eT(e){var{dataKey:t,points:r,stroke:n,strokeWidth:i,fill:o,name:a,hide:l,tooltipType:u}=e;return{dataDefinedOnItem:null==r?void 0:r.map(e=>e.tooltipPayload),positions:null==r?void 0:r.map(e=>e.tooltipPosition),settings:{stroke:n,strokeWidth:i,fill:o,nameKey:void 0,dataKey:t,name:(0,c.uM)(a,t),hide:l,type:u,color:o,unit:""}}}function eD(e){var{displayedData:t,xAxis:r,yAxis:n,zAxis:i,scatterSettings:o,xAxisTicks:a,yAxisTicks:l,cells:u}=e,s=(0,f.uy)(r.dataKey)?o.dataKey:r.dataKey,d=(0,f.uy)(n.dataKey)?o.dataKey:n.dataKey,h=i&&i.dataKey,p=i?i.range:C.defaultProps.range,g=p&&p[0],y=r.scale.bandwidth?r.scale.bandwidth():0,v=n.scale.bandwidth?n.scale.bandwidth():0;return t.map((e,t)=>{var p=(0,c.kr)(e,s),m=(0,c.kr)(e,d),b=!(0,f.uy)(h)&&(0,c.kr)(e,h)||"-",w=[{name:(0,f.uy)(r.dataKey)?o.name:r.name||r.dataKey,unit:r.unit||"",value:p,payload:e,dataKey:s,type:o.tooltipType},{name:(0,f.uy)(n.dataKey)?o.name:n.name||n.dataKey,unit:n.unit||"",value:m,payload:e,dataKey:d,type:o.tooltipType}];"-"!==b&&w.push({name:i.name||i.dataKey,unit:i.unit||"",value:b,payload:e,dataKey:h,type:o.tooltipType});var x=(0,c.nb)({axis:r,ticks:a,bandSize:y,entry:e,index:t,dataKey:s}),O=(0,c.nb)({axis:n,ticks:l,bandSize:v,entry:e,index:t,dataKey:d}),S="-"!==b?i.scale(b):g,P=Math.sqrt(Math.max(S,0)/Math.PI);return eC(eC({},e),{},{cx:x,cy:O,x:x-P,y:O-P,width:2*P,height:2*P,size:S,node:{x:p,y:m,z:b},tooltipPayload:w,tooltipPosition:{x:x,y:O},payload:e},u&&u[t]&&u[t].props)})}var eI=(e,t,r)=>({x:e.cx,y:e.cy,value:"x"===r?+e.node.x:+e.node.y,errorVal:(0,c.kr)(e,t)});function eR(e){var t=(0,n.useRef)((0,f.NF)("recharts-scatter-")),{hide:r,points:a,className:l,needClip:u,xAxisId:s,yAxisId:c,id:d,children:h}=e;if(r)return null;var p=(0,i.$)("recharts-scatter",l),g=(0,f.uy)(d)?t.current:d;return n.createElement(o.W,{className:p,clipPath:u?"url(#clipPath-".concat(g,")"):null},u&&n.createElement("defs",null,n.createElement(ef,{clipPathId:g,xAxisId:s,yAxisId:c})),n.createElement(el,{xAxisId:s,yAxisId:c,data:a,dataPointFormatter:eI,errorBarOffset:0},h),n.createElement(o.W,{key:"recharts-scatter-symbols"},n.createElement(ek,e)))}var eN={xAxisId:0,yAxisId:0,zAxisId:0,legendType:"circle",lineType:"joint",lineJointType:"linear",data:[],shape:"circle",hide:!1,isAnimationActive:!w.m.isSsr,animationBegin:0,animationDuration:400,animationEasing:"linear"};function ez(e){var t=(0,I.e)(e,eN),{animationBegin:r,animationDuration:i,animationEasing:o,hide:a,isAnimationActive:l,legendType:u,lineJointType:c,lineType:f,shape:d,xAxisId:h,yAxisId:p,zAxisId:g}=t,y=eS(t,eO),{needClip:v}=ec(h,p),m=(0,n.useMemo)(()=>(0,s.aS)(e.children,A),[e.children]),b=(0,n.useMemo)(()=>({name:e.name,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey}),[e.data,e.dataKey,e.name,e.tooltipType]),w=(0,X.r)(),x=(0,O.G)(e=>eg(e,h,p,g,b,m,w));return null==v?null:n.createElement(n.Fragment,null,n.createElement(J,{fn:eT,args:eC(eC({},e),{},{points:x})}),n.createElement(eR,eP({},y,{xAxisId:h,yAxisId:p,zAxisId:g,lineType:f,lineJointType:c,legendType:u,shape:d,hide:a,isAnimationActive:l,animationBegin:r,animationDuration:i,animationEasing:o,points:x,needClip:v})))}class eF extends n.Component{render(){return n.createElement(eu,{type:"scatter",data:this.props.data,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:this.props.zAxisId,dataKey:this.props.dataKey,stackId:void 0,hide:this.props.hide,barSize:void 0},n.createElement(eb,{legendPayload:(e=>{var{dataKey:t,name:r,fill:n,legendType:i,hide:o}=e;return[{inactive:o,dataKey:t,type:i,color:n,value:(0,c.uM)(r,t),payload:e}]})(this.props)}),n.createElement(ez,this.props))}}eE(eF,"displayName","Scatter"),eE(eF,"defaultProps",eN)},7283:(e,t,r)=>{"use strict";r.d(t,{h:()=>h});var n=r(2115),i=r(2596),o=r(788),a=r(5641),l=r(6377),u=r(3389);function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var c=e=>{var{cx:t,cy:r,radius:n,angle:i,sign:o,isExternal:l,cornerRadius:u,cornerIsExternal:s}=e,c=u*(l?1:-1)+n,f=Math.asin(u/c)/a.Kg,d=s?i:i+o*f,h=(0,a.IZ)(t,r,c,d);return{center:h,circleTangency:(0,a.IZ)(t,r,n,d),lineTangency:(0,a.IZ)(t,r,c*Math.cos(f*a.Kg),s?i-o*f:i),theta:f}},f=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:o,endAngle:u}=e,s=((e,t)=>(0,l.sA)(t-e)*Math.min(Math.abs(t-e),359.999))(o,u),c=o+s,f=(0,a.IZ)(t,r,i,o),d=(0,a.IZ)(t,r,i,c),h="M ".concat(f.x,",").concat(f.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(s)>180),",").concat(+(o>c),",\n    ").concat(d.x,",").concat(d.y,"\n  ");if(n>0){var p=(0,a.IZ)(t,r,n,o),g=(0,a.IZ)(t,r,n,c);h+="L ".concat(g.x,",").concat(g.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(s)>180),",").concat(+(o<=c),",\n            ").concat(p.x,",").concat(p.y," Z")}else h+="L ".concat(t,",").concat(r," Z");return h},d={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},h=e=>{var t,r=(0,u.e)(e,d),{cx:a,cy:h,innerRadius:p,outerRadius:g,cornerRadius:y,forceCornerRadius:v,cornerIsExternal:m,startAngle:b,endAngle:w,className:x}=r;if(g<p||b===w)return null;var O=(0,i.$)("recharts-sector",x),S=g-p,P=(0,l.F4)(y,S,0,!0);return t=P>0&&360>Math.abs(b-w)?(e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,cornerRadius:o,forceCornerRadius:a,cornerIsExternal:u,startAngle:s,endAngle:d}=e,h=(0,l.sA)(d-s),{circleTangency:p,lineTangency:g,theta:y}=c({cx:t,cy:r,radius:i,angle:s,sign:h,cornerRadius:o,cornerIsExternal:u}),{circleTangency:v,lineTangency:m,theta:b}=c({cx:t,cy:r,radius:i,angle:d,sign:-h,cornerRadius:o,cornerIsExternal:u}),w=u?Math.abs(s-d):Math.abs(s-d)-y-b;if(w<0)return a?"M ".concat(g.x,",").concat(g.y,"\n        a").concat(o,",").concat(o,",0,0,1,").concat(2*o,",0\n        a").concat(o,",").concat(o,",0,0,1,").concat(-(2*o),",0\n      "):f({cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:s,endAngle:d});var x="M ".concat(g.x,",").concat(g.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(h<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(w>180),",").concat(+(h<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(h<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var{circleTangency:O,lineTangency:S,theta:P}=c({cx:t,cy:r,radius:n,angle:s,sign:h,isExternal:!0,cornerRadius:o,cornerIsExternal:u}),{circleTangency:M,lineTangency:C,theta:E}=c({cx:t,cy:r,radius:n,angle:d,sign:-h,isExternal:!0,cornerRadius:o,cornerIsExternal:u}),A=u?Math.abs(s-d):Math.abs(s-d)-P-E;if(A<0&&0===o)return"".concat(x,"L").concat(t,",").concat(r,"Z");x+="L".concat(C.x,",").concat(C.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(h<0),",").concat(M.x,",").concat(M.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(A>180),",").concat(+(h>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(h<0),",").concat(S.x,",").concat(S.y,"Z")}else x+="L".concat(t,",").concat(r,"Z");return x})({cx:a,cy:h,innerRadius:p,outerRadius:g,cornerRadius:Math.min(P,S/2),forceCornerRadius:v,cornerIsExternal:m,startAngle:b,endAngle:w}):f({cx:a,cy:h,innerRadius:p,outerRadius:g,startAngle:b,endAngle:w}),n.createElement("path",s({},(0,o.J9)(r,!0),{className:O,d:t}))}},7298:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(9738);t.cloneDeep=function(e){return n.cloneDeepWith(e)}},7547:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(3676),i=r(2465),o=r(656),a=r(1571);t.uniqBy=function(e,t=i.identity){return o.isArrayLikeObject(e)?n.uniqBy(Array.from(e),a.iteratee(t)):[]}},7918:(e,t,r)=>{"use strict";r.d(t,{V:()=>i});var n=r(2115);function i(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[t,r]=(0,n.useState)({height:0,left:0,top:0,width:0}),i=(0,n.useCallback)(e=>{if(null!=e){var n=e.getBoundingClientRect(),i={height:n.height,left:n.left,top:n.top,width:n.width};(Math.abs(i.height-t.height)>1||Math.abs(i.left-t.left)>1||Math.abs(i.top-t.top)>1||Math.abs(i.width-t.width)>1)&&r({height:i.height,left:i.left,top:i.top,width:i.width})}},[t.width,t.height,t.top,t.left,...e]);return[t,i]}},7924:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8060:(e,t,r)=>{"use strict";r.d(t,{M:()=>o,t:()=>i});var n=r(2115),i=(0,n.createContext)(null),o=()=>(0,n.useContext)(i)},8080:(e,t,r)=>{e.exports=r(8359).last},8132:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(2744);t.isMatch=function(e,t){return n.isMatchWith(e,t,()=>void 0)}},8179:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(9452);t.isArrayLike=function(e){return null!=e&&"function"!=typeof e&&n.isLength(e.length)}},8190:(e,t,r)=>{"use strict";r.d(t,{I:()=>n});var n=(e,t)=>{if(e&&t)return null!=e&&e.reversed?[t[1],t[0]]:t}},8221:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getSymbols=function(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}},8234:(e,t,r)=>{"use strict";r.d(t,{oM:()=>s});var n=r(1971),i=r(8924),o=r(6124),a=(0,i.Mz)([o.HZ],e=>{if(e)return{top:e.top,bottom:e.bottom,left:e.left,right:e.right}}),l=r(2589),u=(0,i.Mz)([a,l.Lp,l.A$],(e,t,r)=>{if(e&&null!=t&&null!=r)return{x:e.left,y:e.top,width:Math.max(0,t-e.left-e.right),height:Math.max(0,r-e.top-e.bottom)}}),s=()=>(0,n.G)(u)},8359:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(220),i=r(4986),o=r(8179);t.last=function(e){if(o.isArrayLike(e))return n.last(i.toArray(e))}},8412:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let r=/^(?:0|[1-9]\d*)$/;t.isIndex=function(e,t=Number.MAX_SAFE_INTEGER){switch(typeof e){case"number":return Number.isInteger(e)&&e>=0&&e<t;case"symbol":return!1;case"string":return r.test(e)}}},8478:(e,t,r)=>{"use strict";r.d(t,{eC:()=>i,gY:()=>n,hX:()=>l,iO:()=>o,lZ:()=>a,pH:()=>u});var n=e=>e.rootProps.barCategoryGap,i=e=>e.rootProps.stackOffset,o=e=>e.options.chartName,a=e=>e.rootProps.syncId,l=e=>e.rootProps.syncMethod,u=e=>e.options.eventEmitter},8564:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},8673:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.debounce=function(e,t=0,r={}){let n;"object"!=typeof r&&(r={});let i=null,o=null,a=null,l=0,u=null,{leading:s=!1,trailing:c=!0,maxWait:f}=r,d="maxWait"in r,h=d?Math.max(Number(f)||0,t):0,p=t=>(null!==i&&(n=e.apply(o,i)),i=o=null,l=t,n),g=e=>(u=null,c&&null!==i)?p(e):n,y=e=>{if(null===a)return!0;let r=e-a,n=d&&e-l>=h;return r>=t||r<0||n},v=()=>{let e=Date.now();if(y(e))return g(e);u=setTimeout(v,(e=>{let r=t-(null===a?0:e-a),n=h-(e-l);return d?Math.min(r,n):r})(e))},m=function(...e){let r=Date.now(),c=y(r);if(i=e,o=this,a=r,c){if(null===u)return(l=r,u=setTimeout(v,t),s&&null!==i)?p(r):n;if(d)return clearTimeout(u),u=setTimeout(v,t),p(r)}return null===u&&(u=setTimeout(v,t)),n};return m.cancel=()=>{null!==u&&clearTimeout(u),l=0,a=i=o=u=null},m.flush=()=>null===u?n:g(Date.now()),m}},8749:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},8870:function(e,t,r){var n;!function(i){"use strict";var o,a={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},l=!0,u="[DecimalError] ",s=u+"Invalid argument: ",c=u+"Exponent out of range: ",f=Math.floor,d=Math.pow,h=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,p=f(1286742750677284.5),g={};function y(e,t){var r,n,i,o,a,u,s,c,f=e.constructor,d=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),l?C(t,d):t;if(s=e.d,c=t.d,a=e.e,i=t.e,s=s.slice(),o=a-i){for(o<0?(n=s,o=-o,u=c.length):(n=c,i=a,u=s.length),o>(u=(a=Math.ceil(d/7))>u?a+1:u+1)&&(o=u,n.length=1),n.reverse();o--;)n.push(0);n.reverse()}for((u=s.length)-(o=c.length)<0&&(o=u,n=c,c=s,s=n),r=0;o;)r=(s[--o]=s[o]+c[o]+r)/1e7|0,s[o]%=1e7;for(r&&(s.unshift(r),++i),u=s.length;0==s[--u];)s.pop();return t.d=s,t.e=i,l?C(t,d):t}function v(e,t,r){if(e!==~~e||e<t||e>r)throw Error(s+e)}function m(e){var t,r,n,i=e.length-1,o="",a=e[0];if(i>0){for(o+=a,t=1;t<i;t++)(r=7-(n=e[t]+"").length)&&(o+=S(r)),o+=n;(r=7-(n=(a=e[t])+"").length)&&(o+=S(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return o+a}g.absoluteValue=g.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},g.comparedTo=g.cmp=function(e){var t,r,n,i;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(i=e.d.length)?n:i;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},g.decimalPlaces=g.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},g.dividedBy=g.div=function(e){return b(this,new this.constructor(e))},g.dividedToIntegerBy=g.idiv=function(e){var t=this.constructor;return C(b(this,new t(e),0,1),t.precision)},g.equals=g.eq=function(e){return!this.cmp(e)},g.exponent=function(){return x(this)},g.greaterThan=g.gt=function(e){return this.cmp(e)>0},g.greaterThanOrEqualTo=g.gte=function(e){return this.cmp(e)>=0},g.isInteger=g.isint=function(){return this.e>this.d.length-2},g.isNegative=g.isneg=function(){return this.s<0},g.isPositive=g.ispos=function(){return this.s>0},g.isZero=function(){return 0===this.s},g.lessThan=g.lt=function(e){return 0>this.cmp(e)},g.lessThanOrEqualTo=g.lte=function(e){return 1>this.cmp(e)},g.logarithm=g.log=function(e){var t,r=this.constructor,n=r.precision,i=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(o))throw Error(u+"NaN");if(this.s<1)throw Error(u+(this.s?"NaN":"-Infinity"));return this.eq(o)?new r(0):(l=!1,t=b(P(this,i),P(e,i),i),l=!0,C(t,n))},g.minus=g.sub=function(e){return e=new this.constructor(e),this.s==e.s?E(this,e):y(this,(e.s=-e.s,e))},g.modulo=g.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(u+"NaN");return this.s?(l=!1,t=b(this,e,0,1).times(e),l=!0,this.minus(t)):C(new r(this),n)},g.naturalExponential=g.exp=function(){return w(this)},g.naturalLogarithm=g.ln=function(){return P(this)},g.negated=g.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},g.plus=g.add=function(e){return e=new this.constructor(e),this.s==e.s?y(this,e):E(this,(e.s=-e.s,e))},g.precision=g.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(s+e);if(t=x(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},g.squareRoot=g.sqrt=function(){var e,t,r,n,i,o,a,s=this.constructor;if(this.s<1){if(!this.s)return new s(0);throw Error(u+"NaN")}for(e=x(this),l=!1,0==(i=Math.sqrt(+this))||i==1/0?(((t=m(this.d)).length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=f((e+1)/2)-(e<0||e%2),n=new s(t=i==1/0?"5e"+e:(t=i.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new s(i.toString()),i=a=(r=s.precision)+3;;)if(n=(o=n).plus(b(this,o,a+2)).times(.5),m(o.d).slice(0,a)===(t=m(n.d)).slice(0,a)){if(t=t.slice(a-3,a+1),i==a&&"4999"==t){if(C(o,r+1,0),o.times(o).eq(this)){n=o;break}}else if("9999"!=t)break;a+=4}return l=!0,C(n,r)},g.times=g.mul=function(e){var t,r,n,i,o,a,u,s,c,f=this.constructor,d=this.d,h=(e=new f(e)).d;if(!this.s||!e.s)return new f(0);for(e.s*=this.s,r=this.e+e.e,(s=d.length)<(c=h.length)&&(o=d,d=h,h=o,a=s,s=c,c=a),o=[],n=a=s+c;n--;)o.push(0);for(n=c;--n>=0;){for(t=0,i=s+n;i>n;)u=o[i]+h[n]*d[i-n-1]+t,o[i--]=u%1e7|0,t=u/1e7|0;o[i]=(o[i]+t)%1e7|0}for(;!o[--a];)o.pop();return t?++r:o.shift(),e.d=o,e.e=r,l?C(e,f.precision):e},g.toDecimalPlaces=g.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(v(e,0,1e9),void 0===t?t=n.rounding:v(t,0,8),C(r,e+x(r)+1,t))},g.toExponential=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=A(n,!0):(v(e,0,1e9),void 0===t?t=i.rounding:v(t,0,8),r=A(n=C(new i(n),e+1,t),!0,e+1)),r},g.toFixed=function(e,t){var r,n,i=this.constructor;return void 0===e?A(this):(v(e,0,1e9),void 0===t?t=i.rounding:v(t,0,8),r=A((n=C(new i(this),e+x(this)+1,t)).abs(),!1,e+x(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},g.toInteger=g.toint=function(){var e=this.constructor;return C(new e(this),x(this)+1,e.rounding)},g.toNumber=function(){return+this},g.toPower=g.pow=function(e){var t,r,n,i,a,s,c=this,d=c.constructor,h=+(e=new d(e));if(!e.s)return new d(o);if(!(c=new d(c)).s){if(e.s<1)throw Error(u+"Infinity");return c}if(c.eq(o))return c;if(n=d.precision,e.eq(o))return C(c,n);if(s=(t=e.e)>=(r=e.d.length-1),a=c.s,s){if((r=h<0?-h:h)<=0x1fffffffffffff){for(i=new d(o),t=Math.ceil(n/7+4),l=!1;r%2&&j((i=i.times(c)).d,t),0!==(r=f(r/2));)j((c=c.times(c)).d,t);return l=!0,e.s<0?new d(o).div(i):C(i,n)}}else if(a<0)throw Error(u+"NaN");return a=a<0&&1&e.d[Math.max(t,r)]?-1:1,c.s=1,l=!1,i=e.times(P(c,n+12)),l=!0,(i=w(i)).s=a,i},g.toPrecision=function(e,t){var r,n,i=this,o=i.constructor;return void 0===e?(r=x(i),n=A(i,r<=o.toExpNeg||r>=o.toExpPos)):(v(e,1,1e9),void 0===t?t=o.rounding:v(t,0,8),r=x(i=C(new o(i),e,t)),n=A(i,e<=r||r<=o.toExpNeg,e)),n},g.toSignificantDigits=g.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(v(e,1,1e9),void 0===t?t=r.rounding:v(t,0,8)),C(new r(this),e,t)},g.toString=g.valueOf=g.val=g.toJSON=function(){var e=x(this),t=this.constructor;return A(this,e<=t.toExpNeg||e>=t.toExpPos)};var b=function(){function e(e,t){var r,n=0,i=e.length;for(e=e.slice();i--;)r=e[i]*t+n,e[i]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var i,o;if(r!=n)o=r>n?1:-1;else for(i=o=0;i<r;i++)if(e[i]!=t[i]){o=e[i]>t[i]?1:-1;break}return o}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=+(e[r]<t[r]),e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,i,o,a){var l,s,c,f,d,h,p,g,y,v,m,b,w,O,S,P,M,E,A=n.constructor,j=n.s==i.s?1:-1,_=n.d,k=i.d;if(!n.s)return new A(n);if(!i.s)throw Error(u+"Division by zero");for(c=0,s=n.e-i.e,M=k.length,S=_.length,g=(p=new A(j)).d=[];k[c]==(_[c]||0);)++c;if(k[c]>(_[c]||0)&&--s,(b=null==o?o=A.precision:a?o+(x(n)-x(i))+1:o)<0)return new A(0);if(b=b/7+2|0,c=0,1==M)for(f=0,k=k[0],b++;(c<S||f)&&b--;c++)w=1e7*f+(_[c]||0),g[c]=w/k|0,f=w%k|0;else{for((f=1e7/(k[0]+1)|0)>1&&(k=e(k,f),_=e(_,f),M=k.length,S=_.length),O=M,v=(y=_.slice(0,M)).length;v<M;)y[v++]=0;(E=k.slice()).unshift(0),P=k[0],k[1]>=1e7/2&&++P;do f=0,(l=t(k,y,M,v))<0?(m=y[0],M!=v&&(m=1e7*m+(y[1]||0)),(f=m/P|0)>1?(f>=1e7&&(f=1e7-1),h=(d=e(k,f)).length,v=y.length,1==(l=t(d,y,h,v))&&(f--,r(d,M<h?E:k,h))):(0==f&&(l=f=1),d=k.slice()),(h=d.length)<v&&d.unshift(0),r(y,d,v),-1==l&&(v=y.length,(l=t(k,y,M,v))<1&&(f++,r(y,M<v?E:k,v))),v=y.length):0===l&&(f++,y=[0]),g[c++]=f,l&&y[0]?y[v++]=_[O]||0:(y=[_[O]],v=1);while((O++<S||void 0!==y[0])&&b--)}return g[0]||g.shift(),p.e=s,C(p,a?o+x(p)+1:o)}}();function w(e,t){var r,n,i,a,u,s=0,f=0,h=e.constructor,p=h.precision;if(x(e)>16)throw Error(c+x(e));if(!e.s)return new h(o);for(null==t?(l=!1,u=p):u=t,a=new h(.03125);e.abs().gte(.1);)e=e.times(a),f+=5;for(u+=Math.log(d(2,f))/Math.LN10*2+5|0,r=n=i=new h(o),h.precision=u;;){if(n=C(n.times(e),u),r=r.times(++s),m((a=i.plus(b(n,r,u))).d).slice(0,u)===m(i.d).slice(0,u)){for(;f--;)i=C(i.times(i),u);return h.precision=p,null==t?(l=!0,C(i,p)):i}i=a}}function x(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function O(e,t,r){if(t>e.LN10.sd())throw l=!0,r&&(e.precision=r),Error(u+"LN10 precision limit exceeded");return C(new e(e.LN10),t)}function S(e){for(var t="";e--;)t+="0";return t}function P(e,t){var r,n,i,a,s,c,f,d,h,p=1,g=e,y=g.d,v=g.constructor,w=v.precision;if(g.s<1)throw Error(u+(g.s?"NaN":"-Infinity"));if(g.eq(o))return new v(0);if(null==t?(l=!1,d=w):d=t,g.eq(10))return null==t&&(l=!0),O(v,d);if(v.precision=d+=10,n=(r=m(y)).charAt(0),!(15e14>Math.abs(a=x(g))))return f=O(v,d+2,w).times(a+""),g=P(new v(n+"."+r.slice(1)),d-10).plus(f),v.precision=w,null==t?(l=!0,C(g,w)):g;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=m((g=g.times(e)).d)).charAt(0),p++;for(a=x(g),n>1?(g=new v("0."+r),a++):g=new v(n+"."+r.slice(1)),c=s=g=b(g.minus(o),g.plus(o),d),h=C(g.times(g),d),i=3;;){if(s=C(s.times(h),d),m((f=c.plus(b(s,new v(i),d))).d).slice(0,d)===m(c.d).slice(0,d))return c=c.times(2),0!==a&&(c=c.plus(O(v,d+2,w).times(a+""))),c=b(c,new v(p),d),v.precision=w,null==t?(l=!0,C(c,w)):c;c=f,i+=2}}function M(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(i=t.length;48===t.charCodeAt(i-1);)--i;if(t=t.slice(n,i)){if(i-=n,e.e=f((r=r-n-1)/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=7;n<i;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),l&&(e.e>p||e.e<-p))throw Error(c+r)}else e.s=0,e.e=0,e.d=[0];return e}function C(e,t,r){var n,i,o,a,u,s,h,g,y=e.d;for(a=1,o=y[0];o>=10;o/=10)a++;if((n=t-a)<0)n+=7,i=t,h=y[g=0];else{if((g=Math.ceil((n+1)/7))>=(o=y.length))return e;for(a=1,h=o=y[g];o>=10;o/=10)a++;n%=7,i=n-7+a}if(void 0!==r&&(u=h/(o=d(10,a-i-1))%10|0,s=t<0||void 0!==y[g+1]||h%o,s=r<4?(u||s)&&(0==r||r==(e.s<0?3:2)):u>5||5==u&&(4==r||s||6==r&&(n>0?i>0?h/d(10,a-i):0:y[g-1])%10&1||r==(e.s<0?8:7))),t<1||!y[0])return s?(o=x(e),y.length=1,t=t-o-1,y[0]=d(10,(7-t%7)%7),e.e=f(-t/7)||0):(y.length=1,y[0]=e.e=e.s=0),e;if(0==n?(y.length=g,o=1,g--):(y.length=g+1,o=d(10,7-n),y[g]=i>0?(h/d(10,a-i)%d(10,i)|0)*o:0),s)for(;;)if(0==g){1e7==(y[0]+=o)&&(y[0]=1,++e.e);break}else{if(y[g]+=o,1e7!=y[g])break;y[g--]=0,o=1}for(n=y.length;0===y[--n];)y.pop();if(l&&(e.e>p||e.e<-p))throw Error(c+x(e));return e}function E(e,t){var r,n,i,o,a,u,s,c,f,d,h=e.constructor,p=h.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new h(e),l?C(t,p):t;if(s=e.d,d=t.d,n=t.e,c=e.e,s=s.slice(),a=c-n){for((f=a<0)?(r=s,a=-a,u=d.length):(r=d,n=c,u=s.length),a>(i=Math.max(Math.ceil(p/7),u)+2)&&(a=i,r.length=1),r.reverse(),i=a;i--;)r.push(0);r.reverse()}else{for((f=(i=s.length)<(u=d.length))&&(u=i),i=0;i<u;i++)if(s[i]!=d[i]){f=s[i]<d[i];break}a=0}for(f&&(r=s,s=d,d=r,t.s=-t.s),u=s.length,i=d.length-u;i>0;--i)s[u++]=0;for(i=d.length;i>a;){if(s[--i]<d[i]){for(o=i;o&&0===s[--o];)s[o]=1e7-1;--s[o],s[i]+=1e7}s[i]-=d[i]}for(;0===s[--u];)s.pop();for(;0===s[0];s.shift())--n;return s[0]?(t.d=s,t.e=n,l?C(t,p):t):new h(0)}function A(e,t,r){var n,i=x(e),o=m(e.d),a=o.length;return t?(r&&(n=r-a)>0?o=o.charAt(0)+"."+o.slice(1)+S(n):a>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(i<0?"e":"e+")+i):i<0?(o="0."+S(-i-1)+o,r&&(n=r-a)>0&&(o+=S(n))):i>=a?(o+=S(i+1-a),r&&(n=r-i-1)>0&&(o=o+"."+S(n))):((n=i+1)<a&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-a)>0&&(i+1===a&&(o+="."),o+=S(n))),e.s<0?"-"+o:o}function j(e,t){if(e.length>t)return e.length=t,!0}function _(e){if(!e||"object"!=typeof e)throw Error(u+"Object expected");var t,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if(void 0!==(n=e[r=i[t]]))if(f(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(s+r+": "+n);if(void 0!==(n=e[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(s+r+": "+n);return this}(a=function e(t){var r,n,i;function o(e){if(!(this instanceof o))return new o(e);if(this.constructor=o,e instanceof o){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(s+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return M(this,e.toString())}if("string"!=typeof e)throw Error(s+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,h.test(e))M(this,e);else throw Error(s+e)}if(o.prototype=g,o.ROUND_UP=0,o.ROUND_DOWN=1,o.ROUND_CEIL=2,o.ROUND_FLOOR=3,o.ROUND_HALF_UP=4,o.ROUND_HALF_DOWN=5,o.ROUND_HALF_EVEN=6,o.ROUND_HALF_CEIL=7,o.ROUND_HALF_FLOOR=8,o.clone=e,o.config=o.set=_,void 0===t&&(t={}),t)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)t.hasOwnProperty(n=i[r++])||(t[n]=this[n]);return o.config(t),o}(a)).default=a.Decimal=a,o=new a(1),void 0===(n=(function(){return a}).call(t,r,t,e))||(e.exports=n)}(0)},8892:(e,t,r)=>{"use strict";function n(e){return Number.isFinite(e)}function i(e){return"number"==typeof e&&e>0&&Number.isFinite(e)}r.d(t,{F:()=>i,H:()=>n})},8924:(e,t,r)=>{"use strict";r.d(t,{Mz:()=>w});var n=e=>Array.isArray(e)?e:[e],i=0,o=class{revision=i;_value;_lastValue;_isEqual=a;constructor(e,t=a){this._value=this._lastValue=e,this._isEqual=t}get value(){return this._value}set value(e){this.value!==e&&(this._value=e,this.revision=++i)}};function a(e,t){return e===t}function l(e){return e instanceof o||console.warn("Not a valid cell! ",e),e.value}var u=(e,t)=>!1;function s(){return function(e,t=a){return new o(null,t)}(0,u)}var c=e=>{let t=e.collectionTag;null===t&&(t=e.collectionTag=s()),l(t)};Symbol();var f=0,d=Object.getPrototypeOf({}),h=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy(this,p);tag=s();tags={};children={};collectionTag=null;id=f++},p={get:(e,t)=>(function(){let{value:r}=e,n=Reflect.get(r,t);if("symbol"==typeof t||t in d)return n;if("object"==typeof n&&null!==n){var i;let r=e.children[t];return void 0===r&&(r=e.children[t]=Array.isArray(i=n)?new g(i):new h(i)),r.tag&&l(r.tag),r.proxy}{let r=e.tags[t];return void 0===r&&((r=e.tags[t]=s()).value=n),l(r),n}})(),ownKeys:e=>(c(e),Reflect.ownKeys(e.value)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e.value,t),has:(e,t)=>Reflect.has(e.value,t)},g=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy([this],y);tag=s();tags={};children={};collectionTag=null;id=f++},y={get:([e],t)=>("length"===t&&c(e),p.get(e,t)),ownKeys:([e])=>p.ownKeys(e),getOwnPropertyDescriptor:([e],t)=>p.getOwnPropertyDescriptor(e,t),has:([e],t)=>p.has(e,t)},v="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function m(){return{s:0,v:void 0,o:null,p:null}}function b(e,t={}){let r,n=m(),{resultEqualityCheck:i}=t,o=0;function a(){let t,a=n,{length:l}=arguments;for(let e=0;e<l;e++){let t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=a.o;null===e&&(a.o=e=new WeakMap);let r=e.get(t);void 0===r?(a=m(),e.set(t,a)):a=r}else{let e=a.p;null===e&&(a.p=e=new Map);let r=e.get(t);void 0===r?(a=m(),e.set(t,a)):a=r}}let u=a;if(1===a.s)t=a.v;else if(t=e.apply(null,arguments),o++,i){let e=r?.deref?.()??r;null!=e&&i(e,t)&&(t=e,0!==o&&o--),r="object"==typeof t&&null!==t||"function"==typeof t?new v(t):t}return u.s=1,u.v=t,t}return a.clearCache=()=>{n=m(),a.resetResultsCount()},a.resultsCount=()=>o,a.resetResultsCount=()=>{o=0},a}var w=function(e,...t){let r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,i=(...e)=>{let t,i=0,o=0,a={},l=e.pop();"object"==typeof l&&(a=l,l=e.pop()),function(e,t=`expected a function, instead received ${typeof e}`){if("function"!=typeof e)throw TypeError(t)}(l,`createSelector expects an output function after the inputs, but received: [${typeof l}]`);let{memoize:u,memoizeOptions:s=[],argsMemoize:c=b,argsMemoizeOptions:f=[],devModeChecks:d={}}={...r,...a},h=n(s),p=n(f),g=function(e){let t=Array.isArray(e[0])?e[0]:e;return!function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){let r=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}(e),y=u(function(){return i++,l.apply(null,arguments)},...h);return Object.assign(c(function(){o++;let e=function(e,t){let r=[],{length:n}=e;for(let i=0;i<n;i++)r.push(e[i].apply(null,t));return r}(g,arguments);return t=y.apply(null,e)},...p),{resultFunc:l,memoizedResultFunc:y,dependencies:g,dependencyRecomputations:()=>o,resetDependencyRecomputations:()=>{o=0},lastResult:()=>t,recomputations:()=>i,resetRecomputations:()=>{i=0},memoize:u,argsMemoize:c})};return Object.assign(i,{withTypes:()=>i}),i}(b),x=Object.assign((e,t=w)=>{!function(e,t=`expected an object, instead received ${typeof e}`){if("object"!=typeof e)throw TypeError(t)}(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);let r=Object.keys(e);return t(r.map(t=>e[t]),(...e)=>e.reduce((e,t,n)=>(e[r[n]]=t,e),{}))},{withTypes:()=>x})},9033:(e,t,r)=>{"use strict";e.exports=r(2436)},9035:(e,t,r)=>{"use strict";r.d(t,{f:()=>d});var n=r(6377),i=r(6605),o=r(1643);class a{static create(e){return new a(e)}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(e){var{bandAware:t,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==e){if(r)switch(r){case"start":default:return this.scale(e);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+n;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(t){var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+o}return this.scale(e)}}isInRange(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}constructor(e){this.scale=e}}!function(e,t,r){var n;(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:1e-4,enumerable:!0,configurable:!0,writable:!0}):e[t]=1e-4}(a,"EPS",1e-4);var l=function(e){var{width:t,height:r}=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,o=Math.atan(r/t);return Math.abs(i>o&&i<Math.PI-o?r/Math.sin(i):t/Math.cos(i))};function u(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],i=0;i<e.length;i+=t)if(void 0!==r&&!0!==r(e[i]))return;else n.push(e[i]);return n}function s(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var o=r();return e*(t-e*o/2-n)>=0&&e*(t+e*o/2-i)<=0}function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e,t,r){var a,{tick:c,ticks:d,viewBox:h,minTickGap:p,orientation:g,interval:y,tickFormatter:v,unit:m,angle:b}=e;if(!d||!d.length||!c)return[];if((0,n.Et)(y)||o.m.isSsr)return null!=(a=u(d,((0,n.Et)(y)?y:0)+1))?a:[];var w="top"===g||"bottom"===g?"width":"height",x=m&&"width"===w?(0,i.P)(m,{fontSize:t,letterSpacing:r}):{width:0,height:0},O=(e,n)=>{var o,a="function"==typeof v?v(e.value,n):e.value;return"width"===w?(o=(0,i.P)(a,{fontSize:t,letterSpacing:r}),l({width:o.width+x.width,height:o.height+x.height},b)):(0,i.P)(a,{fontSize:t,letterSpacing:r})[w]},S=d.length>=2?(0,n.sA)(d[1].coordinate-d[0].coordinate):1,P=function(e,t,r){var n="width"===r,{x:i,y:o,width:a,height:l}=e;return 1===t?{start:n?i:o,end:n?i+a:o+l}:{start:n?i+a:o+l,end:n?i:o}}(h,S,w);return"equidistantPreserveStart"===y?function(e,t,r,n,i){for(var o,a=(n||[]).slice(),{start:l,end:c}=t,f=0,d=1,h=l;d<=a.length;)if(o=function(){var t,o=null==n?void 0:n[f];if(void 0===o)return{v:u(n,d)};var a=f,p=()=>(void 0===t&&(t=r(o,a)),t),g=o.coordinate,y=0===f||s(e,g,p,h,c);y||(f=0,h=l,d+=1),y&&(h=g+e*(p()/2+i),f+=d)}())return o.v;return[]}(S,P,O,d,p):("preserveStart"===y||"preserveStartEnd"===y?function(e,t,r,n,i,o){var a=(n||[]).slice(),l=a.length,{start:u,end:c}=t;if(o){var d=n[l-1],h=r(d,l-1),p=e*(d.coordinate+e*h/2-c);a[l-1]=d=f(f({},d),{},{tickCoord:p>0?d.coordinate-p*e:d.coordinate}),s(e,d.tickCoord,()=>h,u,c)&&(c=d.tickCoord-e*(h/2+i),a[l-1]=f(f({},d),{},{isShow:!0}))}for(var g=o?l-1:l,y=function(t){var n,o=a[t],l=()=>(void 0===n&&(n=r(o,t)),n);if(0===t){var d=e*(o.coordinate-e*l()/2-u);a[t]=o=f(f({},o),{},{tickCoord:d<0?o.coordinate-d*e:o.coordinate})}else a[t]=o=f(f({},o),{},{tickCoord:o.coordinate});s(e,o.tickCoord,l,u,c)&&(u=o.tickCoord+e*(l()/2+i),a[t]=f(f({},o),{},{isShow:!0}))},v=0;v<g;v++)y(v);return a}(S,P,O,d,p,"preserveStartEnd"===y):function(e,t,r,n,i){for(var o=(n||[]).slice(),a=o.length,{start:l}=t,{end:u}=t,c=function(t){var n,c=o[t],d=()=>(void 0===n&&(n=r(c,t)),n);if(t===a-1){var h=e*(c.coordinate+e*d()/2-u);o[t]=c=f(f({},c),{},{tickCoord:h>0?c.coordinate-h*e:c.coordinate})}else o[t]=c=f(f({},c),{},{tickCoord:c.coordinate});s(e,c.tickCoord,d,l,u)&&(u=c.tickCoord-e*(d()/2+i),o[t]=f(f({},c),{},{isShow:!0}))},d=a-1;d>=0;d--)c(d);return o}(S,P,O,d,p)).filter(e=>e.isShow)}},9095:(e,t,r)=>{"use strict";r.d(t,{E:()=>E});var n=r(2115),i=r(2596),o=r(6377),a=r(1643),l=r(788),u=r(6605),s=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,c=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,f=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,d=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,h={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},p=Object.keys(h);class g{static parse(e){var t,[,r,n]=null!=(t=d.exec(e))?t:[];return new g(parseFloat(r),null!=n?n:"")}add(e){return this.unit!==e.unit?new g(NaN,""):new g(this.num+e.num,this.unit)}subtract(e){return this.unit!==e.unit?new g(NaN,""):new g(this.num-e.num,this.unit)}multiply(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new g(NaN,""):new g(this.num*e.num,this.unit||e.unit)}divide(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new g(NaN,""):new g(this.num/e.num,this.unit||e.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return(0,o.M8)(this.num)}constructor(e,t){this.num=e,this.unit=t,this.num=e,this.unit=t,(0,o.M8)(e)&&(this.unit=""),""===t||f.test(t)||(this.num=NaN,this.unit=""),p.includes(t)&&(this.num=e*h[t],this.unit="px")}}function y(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,i,o]=null!=(r=s.exec(t))?r:[],a=g.parse(null!=n?n:""),l=g.parse(null!=o?o:""),u="*"===i?a.multiply(l):a.divide(l);if(u.isNaN())return"NaN";t=t.replace(s,u.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var f,[,d,h,p]=null!=(f=c.exec(t))?f:[],y=g.parse(null!=d?d:""),v=g.parse(null!=p?p:""),m="+"===h?y.add(v):y.subtract(v);if(m.isNaN())return"NaN";t=t.replace(c,m.toString())}return t}var v=/\(([^()]*)\)/;function m(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t,r=e;null!=(t=v.exec(r));){var[,n]=t;r=r.replace(v,y(n))}return r}(t),t=y(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var b=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],w=["dx","dy","angle","className","breakAll"];function x(){return(x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var S=/[ \f\n\r\t\v\u2028\u2029]+/,P=e=>{var{children:t,breakAll:r,style:n}=e;try{var i=[];(0,o.uy)(t)||(i=r?t.toString().split(""):t.toString().split(S));var a=i.map(e=>({word:e,width:(0,u.P)(e,n).width})),l=r?0:(0,u.P)("\xa0",n).width;return{wordsWithComputedWidth:a,spaceWidth:l}}catch(e){return null}},M=e=>[{words:(0,o.uy)(e)?[]:e.toString().split(S)}],C="#808080",E=(0,n.forwardRef)((e,t)=>{var r,{x:u=0,y:s=0,lineHeight:c="1em",capHeight:f="0.71em",scaleToFit:d=!1,textAnchor:h="start",verticalAnchor:p="end",fill:g=C}=e,y=O(e,b),v=(0,n.useMemo)(()=>(e=>{var{width:t,scaleToFit:r,children:n,style:i,breakAll:l,maxLines:u}=e;if((t||r)&&!a.m.isSsr){var s=P({breakAll:l,children:n,style:i});if(!s)return M(n);var{wordsWithComputedWidth:c,spaceWidth:f}=s;return((e,t,r,n,i)=>{var a,{maxLines:l,children:u,style:s,breakAll:c}=e,f=(0,o.Et)(l),d=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce((e,t)=>{var{word:o,width:a}=t,l=e[e.length-1];return l&&(null==n||i||l.width+a+r<Number(n))?(l.words.push(o),l.width+=a+r):e.push({words:[o],width:a}),e},[])},h=d(t),p=e=>e.reduce((e,t)=>e.width>t.width?e:t);if(!f||i||!(h.length>l||p(h).width>Number(n)))return h;for(var g=e=>{var t=d(P({breakAll:c,style:s,children:u.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>l||p(t).width>Number(n),t]},y=0,v=u.length-1,m=0;y<=v&&m<=u.length-1;){var b=Math.floor((y+v)/2),[w,x]=g(b-1),[O]=g(b);if(w||O||(y=b+1),w&&O&&(v=b-1),!w&&O){a=x;break}m++}return a||h})({breakAll:l,children:n,maxLines:u,style:i},c,f,t,r)}return M(n)})({breakAll:y.breakAll,children:y.children,maxLines:y.maxLines,scaleToFit:d,style:y.style,width:y.width}),[y.breakAll,y.children,y.maxLines,d,y.style,y.width]),{dx:S,dy:E,angle:A,className:j,breakAll:_}=y,k=O(y,w);if(!(0,o.vh)(u)||!(0,o.vh)(s))return null;var T=u+((0,o.Et)(S)?S:0),D=s+((0,o.Et)(E)?E:0);switch(p){case"start":r=m("calc(".concat(f,")"));break;case"middle":r=m("calc(".concat((v.length-1)/2," * -").concat(c," + (").concat(f," / 2))"));break;default:r=m("calc(".concat(v.length-1," * -").concat(c,")"))}var I=[];if(d){var R=v[0].width,{width:N}=y;I.push("scale(".concat((0,o.Et)(N)?N/R:1,")"))}return A&&I.push("rotate(".concat(A,", ").concat(T,", ").concat(D,")")),I.length&&(k.transform=I.join(" ")),n.createElement("text",x({},(0,l.J9)(k,!0),{ref:t,x:T,y:D,className:(0,i.$)("recharts-text",j),textAnchor:h,fill:g.includes("url")?C:g}),v.map((e,t)=>{var i=e.words.join(_?"":" ");return n.createElement("tspan",{x:T,dy:0===t?r:c,key:"".concat(i,"-").concat(t)},i)}))});E.displayName="Text"},9279:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObjectLike=function(e){return"object"==typeof e&&null!==e}},9452:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isLength=function(e){return Number.isSafeInteger(e)&&e>=0}},9584:(e,t,r)=>{"use strict";r.d(t,{u:()=>O});var n=r(2115),i=r(5672),o=r.n(i),a=r(2596);function l(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}var u=r(2348),s=r(9095),c=r(379),f=r(6377),d=r(3597),h=r(788),p=r(9035),g=["viewBox"],y=["viewBox"];function v(){return(v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m(Object(r),!0).forEach(function(t){x(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function w(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function x(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class O extends n.Component{shouldComponentUpdate(e,t){var{viewBox:r}=e,n=w(e,g),i=this.props,{viewBox:o}=i,a=w(i,y);return!l(r,o)||!l(n,a)||!l(t,this.state)}getTickLineCoord(e){var t,r,n,i,o,a,{x:l,y:u,width:s,height:c,orientation:d,tickSize:h,mirror:p,tickMargin:g}=this.props,y=p?-1:1,v=e.tickSize||h,m=(0,f.Et)(e.tickCoord)?e.tickCoord:e.coordinate;switch(d){case"top":t=r=e.coordinate,a=(n=(i=u+!p*c)-y*v)-y*g,o=m;break;case"left":n=i=e.coordinate,o=(t=(r=l+!p*s)-y*v)-y*g,a=m;break;case"right":n=i=e.coordinate,o=(t=(r=l+p*s)+y*v)+y*g,a=m;break;default:t=r=e.coordinate,a=(n=(i=u+p*c)+y*v)+y*g,o=m}return{line:{x1:t,y1:n,x2:r,y2:i},tick:{x:o,y:a}}}getTickTextAnchor(){var e,{orientation:t,mirror:r}=this.props;switch(t){case"left":e=r?"start":"end";break;case"right":e=r?"end":"start";break;default:e="middle"}return e}getTickVerticalAnchor(){var{orientation:e,mirror:t}=this.props;switch(e){case"left":case"right":return"middle";case"top":return t?"start":"end";default:return t?"end":"start"}}renderAxisLine(){var{x:e,y:t,width:r,height:i,orientation:l,mirror:u,axisLine:s}=this.props,c=b(b(b({},(0,h.J9)(this.props,!1)),(0,h.J9)(s,!1)),{},{fill:"none"});if("top"===l||"bottom"===l){var f=+("top"===l&&!u||"bottom"===l&&u);c=b(b({},c),{},{x1:e,y1:t+f*i,x2:e+r,y2:t+f*i})}else{var d=+("left"===l&&!u||"right"===l&&u);c=b(b({},c),{},{x1:e+d*r,y1:t,x2:e+d*r,y2:t+i})}return n.createElement("line",v({},c,{className:(0,a.$)("recharts-cartesian-axis-line",o()(s,"className"))}))}static renderTickItem(e,t,r){var i,o=(0,a.$)(t.className,"recharts-cartesian-axis-tick-value");if(n.isValidElement(e))i=n.cloneElement(e,b(b({},t),{},{className:o}));else if("function"==typeof e)i=e(b(b({},t),{},{className:o}));else{var l="recharts-cartesian-axis-tick-value";"boolean"!=typeof e&&(l=(0,a.$)(l,e.className)),i=n.createElement(s.E,v({},t,{className:l}),r)}return i}renderTicks(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:i,stroke:l,tick:s,tickFormatter:c,unit:f}=this.props,g=(0,p.f)(b(b({},this.props),{},{ticks:r}),e,t),y=this.getTickTextAnchor(),m=this.getTickVerticalAnchor(),w=(0,h.J9)(this.props,!1),x=(0,h.J9)(s,!1),S=b(b({},w),{},{fill:"none"},(0,h.J9)(i,!1)),P=g.map((e,t)=>{var{line:r,tick:h}=this.getTickLineCoord(e),p=b(b(b(b({textAnchor:y,verticalAnchor:m},w),{},{stroke:"none",fill:l},x),h),{},{index:t,payload:e,visibleTicksCount:g.length,tickFormatter:c});return n.createElement(u.W,v({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},(0,d.XC)(this.props,e,t)),i&&n.createElement("line",v({},S,r,{className:(0,a.$)("recharts-cartesian-axis-tick-line",o()(i,"className"))})),s&&O.renderTickItem(s,p,"".concat("function"==typeof c?c(e.value,t):e.value).concat(f||"")))});return P.length>0?n.createElement("g",{className:"recharts-cartesian-axis-ticks"},P):null}render(){var{axisLine:e,width:t,height:r,className:i,hide:o}=this.props;if(o)return null;var{ticks:l}=this.props;return null!=t&&t<=0||null!=r&&r<=0?null:n.createElement(u.W,{className:(0,a.$)("recharts-cartesian-axis",i),ref:e=>{if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(t);var r=t[0];if(r){var n=window.getComputedStyle(r).fontSize,i=window.getComputedStyle(r).letterSpacing;(n!==this.state.fontSize||i!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},e&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,l),c.J.renderCallByParent(this.props))}constructor(e){super(e),this.tickRefs=n.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}}x(O,"displayName","CartesianAxis"),x(O,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},9641:e=>{!function(){var t={675:function(e,t){"use strict";t.byteLength=function(e){var t=u(e),r=t[0],n=t[1];return(r+n)*3/4-n},t.toByteArray=function(e){var t,r,o=u(e),a=o[0],l=o[1],s=new i((a+l)*3/4-l),c=0,f=l>0?a-4:a;for(r=0;r<f;r+=4)t=n[e.charCodeAt(r)]<<18|n[e.charCodeAt(r+1)]<<12|n[e.charCodeAt(r+2)]<<6|n[e.charCodeAt(r+3)],s[c++]=t>>16&255,s[c++]=t>>8&255,s[c++]=255&t;return 2===l&&(t=n[e.charCodeAt(r)]<<2|n[e.charCodeAt(r+1)]>>4,s[c++]=255&t),1===l&&(t=n[e.charCodeAt(r)]<<10|n[e.charCodeAt(r+1)]<<4|n[e.charCodeAt(r+2)]>>2,s[c++]=t>>8&255,s[c++]=255&t),s},t.fromByteArray=function(e){for(var t,n=e.length,i=n%3,o=[],a=0,l=n-i;a<l;a+=16383)o.push(function(e,t,n){for(var i,o=[],a=t;a<n;a+=3)i=(e[a]<<16&0xff0000)+(e[a+1]<<8&65280)+(255&e[a+2]),o.push(r[i>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return o.join("")}(e,a,a+16383>l?l:a+16383));return 1===i?o.push(r[(t=e[n-1])>>2]+r[t<<4&63]+"=="):2===i&&o.push(r[(t=(e[n-2]<<8)+e[n-1])>>10]+r[t>>4&63]+r[t<<2&63]+"="),o.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,l=o.length;a<l;++a)r[a]=o[a],n[o.charCodeAt(a)]=a;function u(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var n=r===t?0:4-r%4;return[r,n]}n[45]=62,n[95]=63},72:function(e,t,r){"use strict";var n=r(675),i=r(783),o="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function a(e){if(e>0x7fffffff)throw RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return Object.setPrototypeOf(t,l.prototype),t}function l(e,t,r){if("number"==typeof e){if("string"==typeof t)throw TypeError('The "string" argument must be of type string. Received type number');return c(e)}return u(e,t,r)}function u(e,t,r){if("string"==typeof e){var n=e,i=t;if(("string"!=typeof i||""===i)&&(i="utf8"),!l.isEncoding(i))throw TypeError("Unknown encoding: "+i);var o=0|h(n,i),u=a(o),s=u.write(n,i);return s!==o&&(u=u.slice(0,s)),u}if(ArrayBuffer.isView(e))return f(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(j(e,ArrayBuffer)||e&&j(e.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(j(e,SharedArrayBuffer)||e&&j(e.buffer,SharedArrayBuffer)))return function(e,t,r){var n;if(t<0||e.byteLength<t)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),l.prototype),n}(e,t,r);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');var c=e.valueOf&&e.valueOf();if(null!=c&&c!==e)return l.from(c,t,r);var p=function(e){if(l.isBuffer(e)){var t=0|d(e.length),r=a(t);return 0===r.length||e.copy(r,0,0,t),r}return void 0!==e.length?"number"!=typeof e.length||function(e){return e!=e}(e.length)?a(0):f(e):"Buffer"===e.type&&Array.isArray(e.data)?f(e.data):void 0}(e);if(p)return p;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return l.from(e[Symbol.toPrimitive]("string"),t,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function s(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function c(e){return s(e),a(e<0?0:0|d(e))}function f(e){for(var t=e.length<0?0:0|d(e.length),r=a(t),n=0;n<t;n+=1)r[n]=255&e[n];return r}t.Buffer=l,t.SlowBuffer=function(e){return+e!=e&&(e=0),l.alloc(+e)},t.INSPECT_MAX_BYTES=50,t.kMaxLength=0x7fffffff,l.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),l.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(l.prototype,"parent",{enumerable:!0,get:function(){if(l.isBuffer(this))return this.buffer}}),Object.defineProperty(l.prototype,"offset",{enumerable:!0,get:function(){if(l.isBuffer(this))return this.byteOffset}}),l.poolSize=8192,l.from=function(e,t,r){return u(e,t,r)},Object.setPrototypeOf(l.prototype,Uint8Array.prototype),Object.setPrototypeOf(l,Uint8Array),l.alloc=function(e,t,r){return(s(e),e<=0)?a(e):void 0!==t?"string"==typeof r?a(e).fill(t,r):a(e).fill(t):a(e)},l.allocUnsafe=function(e){return c(e)},l.allocUnsafeSlow=function(e){return c(e)};function d(e){if(e>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function h(e,t){if(l.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||j(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var r=e.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return M(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return E(e).length;default:if(i)return n?-1:M(e).length;t=(""+t).toLowerCase(),i=!0}}function p(e,t,r){var i,o,a,l=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(t>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=t;o<r;++o)i+=_[e[o]];return i}(this,t,r);case"utf8":case"utf-8":return m(this,t,r);case"ascii":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}(this,t,r);case"latin1":case"binary":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}(this,t,r);case"base64":return i=this,o=t,a=r,0===o&&a===i.length?n.fromByteArray(i):n.fromByteArray(i.slice(o,a));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,r){for(var n=e.slice(t,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}(this,t,r);default:if(l)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),l=!0}}function g(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function y(e,t,r,n,i){var o;if(0===e.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(o=r*=1)!=o&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length)if(i)return -1;else r=e.length-1;else if(r<0)if(!i)return -1;else r=0;if("string"==typeof t&&(t=l.from(t,n)),l.isBuffer(t))return 0===t.length?-1:v(e,t,r,n,i);if("number"==typeof t){if(t&=255,"function"==typeof Uint8Array.prototype.indexOf)if(i)return Uint8Array.prototype.indexOf.call(e,t,r);else return Uint8Array.prototype.lastIndexOf.call(e,t,r);return v(e,[t],r,n,i)}throw TypeError("val must be string, number or Buffer")}function v(e,t,r,n,i){var o,a=1,l=e.length,u=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return -1;a=2,l/=2,u/=2,r/=2}function s(e,t){return 1===a?e[t]:e.readUInt16BE(t*a)}if(i){var c=-1;for(o=r;o<l;o++)if(s(e,o)===s(t,-1===c?0:o-c)){if(-1===c&&(c=o),o-c+1===u)return c*a}else -1!==c&&(o-=o-c),c=-1}else for(r+u>l&&(r=l-u),o=r;o>=0;o--){for(var f=!0,d=0;d<u;d++)if(s(e,o+d)!==s(t,d)){f=!1;break}if(f)return o}return -1}l.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==l.prototype},l.compare=function(e,t){if(j(e,Uint8Array)&&(e=l.from(e,e.offset,e.byteLength)),j(t,Uint8Array)&&(t=l.from(t,t.offset,t.byteLength)),!l.isBuffer(e)||!l.isBuffer(t))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var r=e.length,n=t.length,i=0,o=Math.min(r,n);i<o;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:+(n<r)},l.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},l.concat=function(e,t){if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return l.alloc(0);if(void 0===t)for(r=0,t=0;r<e.length;++r)t+=e[r].length;var r,n=l.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var o=e[r];if(j(o,Uint8Array)&&(o=l.from(o)),!l.isBuffer(o))throw TypeError('"list" argument must be an Array of Buffers');o.copy(n,i),i+=o.length}return n},l.byteLength=h,l.prototype._isBuffer=!0,l.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)g(this,t,t+1);return this},l.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)g(this,t,t+3),g(this,t+1,t+2);return this},l.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)g(this,t,t+7),g(this,t+1,t+6),g(this,t+2,t+5),g(this,t+3,t+4);return this},l.prototype.toString=function(){var e=this.length;return 0===e?"":0==arguments.length?m(this,0,e):p.apply(this,arguments)},l.prototype.toLocaleString=l.prototype.toString,l.prototype.equals=function(e){if(!l.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===l.compare(this,e)},l.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return e=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(e+=" ... "),"<Buffer "+e+">"},o&&(l.prototype[o]=l.prototype.inspect),l.prototype.compare=function(e,t,r,n,i){if(j(e,Uint8Array)&&(e=l.from(e,e.offset,e.byteLength)),!l.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,i>>>=0,this===e)return 0;for(var o=i-n,a=r-t,u=Math.min(o,a),s=this.slice(n,i),c=e.slice(t,r),f=0;f<u;++f)if(s[f]!==c[f]){o=s[f],a=c[f];break}return o<a?-1:+(a<o)},l.prototype.includes=function(e,t,r){return -1!==this.indexOf(e,t,r)},l.prototype.indexOf=function(e,t,r){return y(this,e,t,r,!0)},l.prototype.lastIndexOf=function(e,t,r){return y(this,e,t,r,!1)};function m(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var o,a,l,u,s=e[i],c=null,f=s>239?4:s>223?3:s>191?2:1;if(i+f<=r)switch(f){case 1:s<128&&(c=s);break;case 2:(192&(o=e[i+1]))==128&&(u=(31&s)<<6|63&o)>127&&(c=u);break;case 3:o=e[i+1],a=e[i+2],(192&o)==128&&(192&a)==128&&(u=(15&s)<<12|(63&o)<<6|63&a)>2047&&(u<55296||u>57343)&&(c=u);break;case 4:o=e[i+1],a=e[i+2],l=e[i+3],(192&o)==128&&(192&a)==128&&(192&l)==128&&(u=(15&s)<<18|(63&o)<<12|(63&a)<<6|63&l)>65535&&u<1114112&&(c=u)}null===c?(c=65533,f=1):c>65535&&(c-=65536,n.push(c>>>10&1023|55296),c=56320|1023&c),n.push(c),i+=f}var d=n,h=d.length;if(h<=4096)return String.fromCharCode.apply(String,d);for(var p="",g=0;g<h;)p+=String.fromCharCode.apply(String,d.slice(g,g+=4096));return p}function b(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function w(e,t,r,n,i,o){if(!l.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw RangeError('"value" argument is out of bounds');if(r+n>e.length)throw RangeError("Index out of range")}function x(e,t,r,n,i,o){if(r+n>e.length||r<0)throw RangeError("Index out of range")}function O(e,t,r,n,o){return t*=1,r>>>=0,o||x(e,t,r,4,34028234663852886e22,-34028234663852886e22),i.write(e,t,r,n,23,4),r+4}function S(e,t,r,n,o){return t*=1,r>>>=0,o||x(e,t,r,8,17976931348623157e292,-17976931348623157e292),i.write(e,t,r,n,52,8),r+8}l.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else if(isFinite(t))t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,o,a,l,u,s,c,f,d=this.length-t;if((void 0===r||r>d)&&(r=d),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var h=!1;;)switch(n){case"hex":return function(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=t.length;n>o/2&&(n=o/2);for(var a=0;a<n;++a){var l,u=parseInt(t.substr(2*a,2),16);if((l=u)!=l)break;e[r+a]=u}return a}(this,e,t,r);case"utf8":case"utf-8":return i=t,o=r,A(M(e,this.length-i),this,i,o);case"ascii":return a=t,l=r,A(C(e),this,a,l);case"latin1":case"binary":return function(e,t,r,n){return A(C(t),e,r,n)}(this,e,t,r);case"base64":return u=t,s=r,A(E(e),this,u,s);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return c=t,f=r,A(function(e,t){for(var r,n,i=[],o=0;o<e.length&&!((t-=2)<0);++o)n=(r=e.charCodeAt(o))>>8,i.push(r%256),i.push(n);return i}(e,this.length-c),this,c,f);default:if(h)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),h=!0}},l.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},l.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);var n=this.subarray(e,t);return Object.setPrototypeOf(n,l.prototype),n},l.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n},l.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},l.prototype.readUInt8=function(e,t){return e>>>=0,t||b(e,1,this.length),this[e]},l.prototype.readUInt16LE=function(e,t){return e>>>=0,t||b(e,2,this.length),this[e]|this[e+1]<<8},l.prototype.readUInt16BE=function(e,t){return e>>>=0,t||b(e,2,this.length),this[e]<<8|this[e+1]},l.prototype.readUInt32LE=function(e,t){return e>>>=0,t||b(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+0x1000000*this[e+3]},l.prototype.readUInt32BE=function(e,t){return e>>>=0,t||b(e,4,this.length),0x1000000*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},l.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},l.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=t,i=1,o=this[e+--n];n>0&&(i*=256);)o+=this[e+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*t)),o},l.prototype.readInt8=function(e,t){return(e>>>=0,t||b(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},l.prototype.readInt16LE=function(e,t){e>>>=0,t||b(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?0xffff0000|r:r},l.prototype.readInt16BE=function(e,t){e>>>=0,t||b(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?0xffff0000|r:r},l.prototype.readInt32LE=function(e,t){return e>>>=0,t||b(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},l.prototype.readInt32BE=function(e,t){return e>>>=0,t||b(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},l.prototype.readFloatLE=function(e,t){return e>>>=0,t||b(e,4,this.length),i.read(this,e,!0,23,4)},l.prototype.readFloatBE=function(e,t){return e>>>=0,t||b(e,4,this.length),i.read(this,e,!1,23,4)},l.prototype.readDoubleLE=function(e,t){return e>>>=0,t||b(e,8,this.length),i.read(this,e,!0,52,8)},l.prototype.readDoubleBE=function(e,t){return e>>>=0,t||b(e,8,this.length),i.read(this,e,!1,52,8)},l.prototype.writeUIntLE=function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;w(this,e,t,r,i,0)}var o=1,a=0;for(this[t]=255&e;++a<r&&(o*=256);)this[t+a]=e/o&255;return t+r},l.prototype.writeUIntBE=function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;w(this,e,t,r,i,0)}var o=r-1,a=1;for(this[t+o]=255&e;--o>=0&&(a*=256);)this[t+o]=e/a&255;return t+r},l.prototype.writeUInt8=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,1,255,0),this[t]=255&e,t+1},l.prototype.writeUInt16LE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},l.prototype.writeUInt16BE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},l.prototype.writeUInt32LE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,4,0xffffffff,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},l.prototype.writeUInt32BE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,4,0xffffffff,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},l.prototype.writeIntLE=function(e,t,r,n){if(e*=1,t>>>=0,!n){var i=Math.pow(2,8*r-1);w(this,e,t,r,i-1,-i)}var o=0,a=1,l=0;for(this[t]=255&e;++o<r&&(a*=256);)e<0&&0===l&&0!==this[t+o-1]&&(l=1),this[t+o]=(e/a|0)-l&255;return t+r},l.prototype.writeIntBE=function(e,t,r,n){if(e*=1,t>>>=0,!n){var i=Math.pow(2,8*r-1);w(this,e,t,r,i-1,-i)}var o=r-1,a=1,l=0;for(this[t+o]=255&e;--o>=0&&(a*=256);)e<0&&0===l&&0!==this[t+o+1]&&(l=1),this[t+o]=(e/a|0)-l&255;return t+r},l.prototype.writeInt8=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},l.prototype.writeInt16LE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},l.prototype.writeInt16BE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},l.prototype.writeInt32LE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,4,0x7fffffff,-0x80000000),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},l.prototype.writeInt32BE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,4,0x7fffffff,-0x80000000),e<0&&(e=0xffffffff+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},l.prototype.writeFloatLE=function(e,t,r){return O(this,e,t,!0,r)},l.prototype.writeFloatBE=function(e,t,r){return O(this,e,t,!1,r)},l.prototype.writeDoubleLE=function(e,t,r){return S(this,e,t,!0,r)},l.prototype.writeDoubleBE=function(e,t,r){return S(this,e,t,!1,r)},l.prototype.copy=function(e,t,r,n){if(!l.isBuffer(e))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i=n-r;if(this===e&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(t,r,n);else if(this===e&&r<t&&t<n)for(var o=i-1;o>=0;--o)e[o+t]=this[o+r];else Uint8Array.prototype.set.call(e,this.subarray(r,n),t);return i},l.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!l.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===e.length){var i,o=e.charCodeAt(0);("utf8"===n&&o<128||"latin1"===n)&&(e=o)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(i=t;i<r;++i)this[i]=e;else{var a=l.isBuffer(e)?e:l.from(e,n),u=a.length;if(0===u)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(i=0;i<r-t;++i)this[i+t]=a[i%u]}return this};var P=/[^+/0-9A-Za-z-_]/g;function M(e,t){t=t||1/0;for(var r,n=e.length,i=null,o=[],a=0;a<n;++a){if((r=e.charCodeAt(a))>55295&&r<57344){if(!i){if(r>56319||a+1===n){(t-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&o.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;o.push(r)}else if(r<2048){if((t-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return o}function C(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function E(e){return n.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(P,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function A(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length)&&!(i>=e.length);++i)t[i+r]=e[i];return i}function j(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}var _=function(){for(var e="0123456789abcdef",t=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)t[n+i]=e[r]+e[i];return t}()},783:function(e,t){t.read=function(e,t,r,n,i){var o,a,l=8*i-n-1,u=(1<<l)-1,s=u>>1,c=-7,f=r?i-1:0,d=r?-1:1,h=e[t+f];for(f+=d,o=h&(1<<-c)-1,h>>=-c,c+=l;c>0;o=256*o+e[t+f],f+=d,c-=8);for(a=o&(1<<-c)-1,o>>=-c,c+=n;c>0;a=256*a+e[t+f],f+=d,c-=8);if(0===o)o=1-s;else{if(o===u)return a?NaN:1/0*(h?-1:1);a+=Math.pow(2,n),o-=s}return(h?-1:1)*a*Math.pow(2,o-n)},t.write=function(e,t,r,n,i,o){var a,l,u,s=8*o-i-1,c=(1<<s)-1,f=c>>1,d=5960464477539062e-23*(23===i),h=n?0:o-1,p=n?1:-1,g=+(t<0||0===t&&1/t<0);for(isNaN(t=Math.abs(t))||t===1/0?(l=+!!isNaN(t),a=c):(a=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-a))<1&&(a--,u*=2),a+f>=1?t+=d/u:t+=d*Math.pow(2,1-f),t*u>=2&&(a++,u/=2),a+f>=c?(l=0,a=c):a+f>=1?(l=(t*u-1)*Math.pow(2,i),a+=f):(l=t*Math.pow(2,f-1)*Math.pow(2,i),a=0));i>=8;e[r+h]=255&l,h+=p,l/=256,i-=8);for(a=a<<i|l,s+=i;s>0;e[r+h]=255&a,h+=p,a/=256,s-=8);e[r+h-p]|=128*g}}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var o=r[e]={exports:{}},a=!0;try{t[e](o,o.exports,n),a=!1}finally{a&&delete r[e]}return o.exports}n.ab="//",e.exports=n(72)}()},9738:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(4117),i=r(2721);t.cloneDeepWith=function(e,t){return n.cloneDeepWith(e,(r,o,a,l)=>{let u=t?.(r,o,a,l);if(null!=u)return u;if("object"==typeof e)switch(Object.prototype.toString.call(e)){case i.numberTag:case i.stringTag:case i.booleanTag:{let t=new e.constructor(e?.valueOf());return n.copyProperties(t,e),t}case i.argumentsTag:{let t={};return n.copyProperties(t,e),t.length=e.length,t[Symbol.iterator]=e[Symbol.iterator],t}default:return}})}},9795:(e,t,r)=>{"use strict";r.d(t,{i:()=>_});var n=r(2115);let i=Math.cos,o=Math.sin,a=Math.sqrt,l=Math.PI,u=2*l,s={draw(e,t){let r=a(t/l);e.moveTo(r,0),e.arc(0,0,r,0,u)}},c=a(1/3),f=2*c,d=o(l/10)/o(7*l/10),h=o(u/10)*d,p=-i(u/10)*d,g=a(3),y=a(3)/2,v=1/a(12),m=(v/2+1)*3;var b=r(5654),w=r(1847);a(3),a(3);var x=r(2596),O=r(788),S=r(6377),P=["type","size","sizeType"];function M(){return(M=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function C(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function E(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?C(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):C(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var A={symbolCircle:s,symbolCross:{draw(e,t){let r=a(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=a(t/f),n=r*c;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=a(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=a(.8908130915292852*t),n=h*r,l=p*r;e.moveTo(0,-r),e.lineTo(n,l);for(let t=1;t<5;++t){let a=u*t/5,s=i(a),c=o(a);e.lineTo(c*r,-s*r),e.lineTo(s*n-c*l,c*n+s*l)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-a(t/(3*g));e.moveTo(0,2*r),e.lineTo(-g*r,-r),e.lineTo(g*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=a(t/m),n=r/2,i=r*v,o=r*v+r,l=-n;e.moveTo(n,i),e.lineTo(n,o),e.lineTo(l,o),e.lineTo(-.5*n-y*i,y*n+-.5*i),e.lineTo(-.5*n-y*o,y*n+-.5*o),e.lineTo(-.5*l-y*o,y*l+-.5*o),e.lineTo(-.5*n+y*i,-.5*i-y*n),e.lineTo(-.5*n+y*o,-.5*o-y*n),e.lineTo(-.5*l+y*o,-.5*o-y*l),e.closePath()}}},j=Math.PI/180,_=e=>{var{type:t="circle",size:r=64,sizeType:i="area"}=e,o=E(E({},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,P)),{},{type:t,size:r,sizeType:i}),{className:a,cx:l,cy:u}=o,c=(0,O.J9)(o,!0);return l===+l&&u===+u&&r===+r?n.createElement("path",M({},c,{className:(0,x.$)("recharts-symbols",a),transform:"translate(".concat(l,", ").concat(u,")"),d:(()=>{var e=A["symbol".concat((0,S.Zb)(t))]||s;return(function(e,t){let r=null,n=(0,w.i)(i);function i(){let i;if(r||(r=i=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),i)return r=null,i+""||null}return e="function"==typeof e?e:(0,b.A)(e||s),t="function"==typeof t?t:(0,b.A)(void 0===t?64:+t),i.type=function(t){return arguments.length?(e="function"==typeof t?t:(0,b.A)(t),i):e},i.size=function(e){return arguments.length?(t="function"==typeof e?e:(0,b.A)(+e),i):t},i.context=function(e){return arguments.length?(r=null==e?null:e,i):r},i})().type(e).size(((e,t,r)=>{if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*j;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.tan(n)**2);case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}})(r,i,t))()})()})):null};_.registerSymbol=(e,t)=>{A["symbol".concat((0,S.Zb)(e))]=t}},9819:(e,t,r)=>{"use strict";function n(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}r.d(t,{A:()=>n}),Array.prototype.slice},9827:(e,t,r)=>{"use strict";r.d(t,{qx:()=>_,IH:()=>j,s0:()=>b,gH:()=>m,SW:()=>N,YB:()=>S,bk:()=>R,Hj:()=>k,nb:()=>E,PW:()=>x,Mk:()=>A,$8:()=>C,yy:()=>M,Rh:()=>O,GF:()=>T,uM:()=>D,kr:()=>v,r4:()=>I,_L:()=>w});var n=r(241),i=r.n(n),o=r(5672),a=r.n(o);function l(e,t){if((i=e.length)>1)for(var r,n,i,o=1,a=e[t[0]],l=a.length;o<i;++o)for(n=a,a=e[t[o]],r=0;r<l;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}var u=r(9819),s=r(5654);function c(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function f(e,t){return e[t]}function d(e){let t=[];return t.key=e,t}var h=r(6377),p=r(5641);function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){var n,i,o;n=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function v(e,t,r){return(0,h.uy)(e)||(0,h.uy)(t)?r:(0,h.vh)(t)?a()(e,t,r):"function"==typeof t?t(e):r}var m=(e,t,r,n,i)=>{var o,a=-1,l=null!=(o=null==t?void 0:t.length)?o:0;if(l<=1||null==e)return 0;if("angleAxis"===n&&null!=i&&1e-6>=Math.abs(Math.abs(i[1]-i[0])-360))for(var u=0;u<l;u++){var s=u>0?r[u-1].coordinate:r[l-1].coordinate,c=r[u].coordinate,f=u>=l-1?r[0].coordinate:r[u+1].coordinate,d=void 0;if((0,h.sA)(c-s)!==(0,h.sA)(f-c)){var p=[];if((0,h.sA)(f-c)===(0,h.sA)(i[1]-i[0])){d=f;var g=c+i[1]-i[0];p[0]=Math.min(g,(g+s)/2),p[1]=Math.max(g,(g+s)/2)}else{d=s;var y=f+i[1]-i[0];p[0]=Math.min(c,(y+c)/2),p[1]=Math.max(c,(y+c)/2)}var v=[Math.min(c,(d+c)/2),Math.max(c,(d+c)/2)];if(e>v[0]&&e<=v[1]||e>=p[0]&&e<=p[1]){({index:a}=r[u]);break}}else{var m=Math.min(s,f),b=Math.max(s,f);if(e>(m+c)/2&&e<=(b+c)/2){({index:a}=r[u]);break}}}else if(t){for(var w=0;w<l;w++)if(0===w&&e<=(t[w].coordinate+t[w+1].coordinate)/2||w>0&&w<l-1&&e>(t[w].coordinate+t[w-1].coordinate)/2&&e<=(t[w].coordinate+t[w+1].coordinate)/2||w===l-1&&e>(t[w].coordinate+t[w-1].coordinate)/2){({index:a}=t[w]);break}}return a},b=(e,t,r)=>{if(t&&r){var{width:n,height:i}=r,{align:o,verticalAlign:a,layout:l}=t;if(("vertical"===l||"horizontal"===l&&"middle"===a)&&"center"!==o&&(0,h.Et)(e[o]))return y(y({},e),{},{[o]:e[o]+(n||0)});if(("horizontal"===l||"vertical"===l&&"center"===o)&&"middle"!==a&&(0,h.Et)(e[a]))return y(y({},e),{},{[a]:e[a]+(i||0)})}return e},w=(e,t)=>"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t,x=(e,t,r,n)=>{if(n)return e.map(e=>e.coordinate);var i,o,a=e.map(e=>(e.coordinate===t&&(i=!0),e.coordinate===r&&(o=!0),e.coordinate));return i||a.push(t),o||a.push(r),a},O=(e,t,r)=>{if(!e)return null;var{duplicateDomain:n,type:i,range:o,scale:a,realScaleType:l,isCategorical:u,categoricalDomain:s,tickCount:c,ticks:f,niceTicks:d,axisType:p}=e;if(!a)return null;var g="scaleBand"===l&&a.bandwidth?a.bandwidth()/2:2,y=(t||r)&&"category"===i&&a.bandwidth?a.bandwidth()/g:0;return(y="angleAxis"===p&&o&&o.length>=2?2*(0,h.sA)(o[0]-o[1])*y:y,t&&(f||d))?(f||d||[]).map((e,t)=>({coordinate:a(n?n.indexOf(e):e)+y,value:e,offset:y,index:t})).filter(e=>!(0,h.M8)(e.coordinate)):u&&s?s.map((e,t)=>({coordinate:a(e)+y,value:e,index:t,offset:y})):a.ticks&&!r&&null!=c?a.ticks(c).map((e,t)=>({coordinate:a(e)+y,value:e,offset:y,index:t})):a.domain().map((e,t)=>({coordinate:a(e)+y,value:n?n[e]:e,index:t,offset:y}))},S=e=>{var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),i=Math.min(n[0],n[1])-1e-4,o=Math.max(n[0],n[1])+1e-4,a=e(t[0]),l=e(t[r-1]);(a<i||a>o||l<i||l>o)&&e.domain([t[0],t[r-1]])}},P={sign:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,o=0,a=0;a<t;++a){var l=(0,h.M8)(e[a][r][1])?e[a][r][0]:e[a][r][1];l>=0?(e[a][r][0]=i,e[a][r][1]=i+l,i=e[a][r][1]):(e[a][r][0]=o,e[a][r][1]=o+l,o=e[a][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,i,o=0,a=e[0].length;o<a;++o){for(i=r=0;r<n;++r)i+=e[r][o][1]||0;if(i)for(r=0;r<n;++r)e[r][o][1]/=i}l(e,t)}},none:l,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,i=e[t[0]],o=i.length;n<o;++n){for(var a=0,u=0;a<r;++a)u+=e[a][n][1]||0;i[n][1]+=i[n][0]=-u/2}l(e,t)}},wiggle:function(e,t){if((i=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,i,o=0,a=1;a<n;++a){for(var u=0,s=0,c=0;u<i;++u){for(var f=e[t[u]],d=f[a][1]||0,h=(d-(f[a-1][1]||0))/2,p=0;p<u;++p){var g=e[t[p]];h+=(g[a][1]||0)-(g[a-1][1]||0)}s+=d,c+=h*d}r[a-1][1]+=r[a-1][0]=o,s&&(o-=c/s)}r[a-1][1]+=r[a-1][0]=o,l(e,t)}},positive:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,o=0;o<t;++o){var a=(0,h.M8)(e[o][r][1])?e[o][r][0]:e[o][r][1];a>=0?(e[o][r][0]=i,e[o][r][1]=i+a,i=e[o][r][1]):(e[o][r][0]=0,e[o][r][1]=0)}}},M=(e,t,r)=>{var n=P[r];return(function(){var e=(0,s.A)([]),t=c,r=l,n=f;function i(i){var o,a,l=Array.from(e.apply(this,arguments),d),s=l.length,c=-1;for(let e of i)for(o=0,++c;o<s;++o)(l[o][c]=[0,+n(e,l[o].key,c,i)]).data=e;for(o=0,a=(0,u.A)(t(l));o<s;++o)l[a[o]].index=o;return r(l,a),l}return i.keys=function(t){return arguments.length?(e="function"==typeof t?t:(0,s.A)(Array.from(t)),i):e},i.value=function(e){return arguments.length?(n="function"==typeof e?e:(0,s.A)(+e),i):n},i.order=function(e){return arguments.length?(t=null==e?c:"function"==typeof e?e:(0,s.A)(Array.from(e)),i):t},i.offset=function(e){return arguments.length?(r=null==e?l:e,i):r},i})().keys(t).value((e,t)=>+v(e,t,0)).order(c).offset(n)(e)};function C(e){return null==e?void 0:String(e)}function E(e){var{axis:t,ticks:r,bandSize:n,entry:i,index:o,dataKey:a}=e;if("category"===t.type){if(!t.allowDuplicatedCategory&&t.dataKey&&!(0,h.uy)(i[t.dataKey])){var l=(0,h.eP)(r,"value",i[t.dataKey]);if(l)return l.coordinate+n/2}return r[o]?r[o].coordinate+n/2:null}var u=v(i,(0,h.uy)(a)?t.dataKey:a);return(0,h.uy)(u)?null:t.scale(u)}var A=(e,t,r)=>{if(null!=e)return(e=>[e[0]===1/0?0:e[0],e[1]===-1/0?0:e[1]])(Object.keys(e).reduce((n,i)=>{var{stackedData:o}=e[i],a=o.reduce((e,n)=>{var i=(e=>{var t=e.flat(2).filter(h.Et);return[Math.min(...t),Math.max(...t)]})(n.slice(t,r+1));return[Math.min(e[0],i[0]),Math.max(e[1],i[1])]},[1/0,-1/0]);return[Math.min(a[0],n[0]),Math.max(a[1],n[1])]},[1/0,-1/0]))},j=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,_=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,k=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var o=i()(t,e=>e.coordinate),a=1/0,l=1,u=o.length;l<u;l++){var s=o[l],c=o[l-1];a=Math.min((s.coordinate||0)-(c.coordinate||0),a)}return a===1/0?0:a}return r?void 0:0};function T(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:i,name:o}=e;return y(y({},t),{},{dataKey:r,payload:n,value:i,name:o})}function D(e,t){return e?String(e):"string"==typeof t?t:void 0}function I(e,t,r,n,i){return"horizontal"===r||"vertical"===r?e>=i.left&&e<=i.left+i.width&&t>=i.top&&t<=i.top+i.height?{x:e,y:t}:null:n?(0,p.yy)({x:e,y:t},n):null}var R=(e,t,r,n)=>{var i=t.find(e=>e&&e.index===r);if(i){if("horizontal"===e)return{x:i.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:i.coordinate};if("centric"===e){var o=i.coordinate,{radius:a}=n;return y(y(y({},n),(0,p.IZ)(n.cx,n.cy,a,o)),{},{angle:o,radius:a})}var l=i.coordinate,{angle:u}=n;return y(y(y({},n),(0,p.IZ)(n.cx,n.cy,l,u)),{},{angle:u,radius:l})}return{x:0,y:0}},N=(e,t)=>"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius},9901:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(4373),i=r(4664);t.range=function(e,t,r){r&&"number"!=typeof r&&n.isIterateeCall(e,t,r)&&(t=r=void 0),e=i.toFinite(e),void 0===t?(t=e,e=0):t=i.toFinite(t),r=void 0===r?e<t?1:-1:i.toFinite(r);let o=Math.max(Math.ceil((t-e)/(r||1)),0),a=Array(o);for(let t=0;t<o;t++)a[t]=e,e+=r;return a}},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(2115);let i=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},o=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:i=24,strokeWidth:l=2,absoluteStrokeWidth:u,className:s="",children:c,iconNode:f,...d}=e;return(0,n.createElement)("svg",{ref:t,...a,width:i,height:i,stroke:r,strokeWidth:u?24*Number(l)/Number(i):l,className:o("lucide",s),...!c&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(d)&&{"aria-hidden":"true"},...d},[...f.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),u=(e,t)=>{let r=(0,n.forwardRef)((r,a)=>{let{className:u,...s}=r;return(0,n.createElement)(l,{ref:a,iconNode:t,className:o("lucide-".concat(i(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),u),...s})});return r.displayName=i(e),r}}}]);