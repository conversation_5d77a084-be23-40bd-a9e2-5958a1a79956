{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/OpenPub/journal-transparency/src/app/store.ts"], "sourcesContent": ["/**\n * Zustand store for Journal Choice Transparency application.\n */\n\nimport { create } from 'zustand';\nimport { Journal, FilterState, AppState } from './types';\n\ninterface StoreActions {\n  setJournals: (journals: Journal[]) => void;\n  setFilters: (filters: Partial<FilterState>) => void;\n  setSelectedJournals: (selected: Set<string>) => void;\n  addSelectedJournal: (issn: string) => void;\n  removeSelectedJournal: (issn: string) => void;\n  clearSelectedJournals: () => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  applyFilters: () => void;\n}\n\ntype Store = AppState & StoreActions;\n\nconst initialFilters: FilterState = {\n  subjects: new Set(),\n  oaTypes: new Set(),\n  apcRange: [0, 10000],\n  impactRange: [0, 200],\n  searchTerm: '',\n};\n\nexport const useStore = create<Store>((set, get) => ({\n  // State\n  journals: [],\n  filteredJournals: [],\n  selectedJournals: new Set(),\n  filters: initialFilters,\n  isLoading: false,\n  error: null,\n\n  // Actions\n  setJournals: (journals) => {\n    set({ journals });\n    get().applyFilters();\n  },\n\n  setFilters: (newFilters) => {\n    const currentFilters = get().filters;\n    const updatedFilters = { ...currentFilters, ...newFilters };\n    set({ filters: updatedFilters });\n    get().applyFilters();\n  },\n\n  setSelectedJournals: (selected) => set({ selectedJournals: selected }),\n\n  addSelectedJournal: (issn) => {\n    const current = get().selectedJournals;\n    const updated = new Set(current);\n    updated.add(issn);\n    set({ selectedJournals: updated });\n  },\n\n  removeSelectedJournal: (issn) => {\n    const current = get().selectedJournals;\n    const updated = new Set(current);\n    updated.delete(issn);\n    set({ selectedJournals: updated });\n  },\n\n  clearSelectedJournals: () => set({ selectedJournals: new Set() }),\n\n  setLoading: (isLoading) => set({ isLoading }),\n\n  setError: (error) => set({ error }),\n\n  applyFilters: () => {\n    const { journals, filters } = get();\n    \n    const filtered = journals.filter((journal) => {\n      // Subject filter\n      if (filters.subjects.size > 0 && !filters.subjects.has(journal.subject)) {\n        return false;\n      }\n\n      // OA Type filter\n      if (filters.oaTypes.size > 0 && !filters.oaTypes.has(journal.oa_type)) {\n        return false;\n      }\n\n      // APC range filter - handle null values\n      if (journal.apc_usd != null) {\n        if (journal.apc_usd < filters.apcRange[0] || journal.apc_usd > filters.apcRange[1]) {\n          return false;\n        }\n      }\n\n      // Impact range filter\n      if (journal.impact_proxy < filters.impactRange[0] || journal.impact_proxy > filters.impactRange[1]) {\n        return false;\n      }\n\n      // Search term filter\n      if (filters.searchTerm && !journal.title.toLowerCase().includes(filters.searchTerm.toLowerCase())) {\n        return false;\n      }\n\n      return true;\n    });\n\n    set({ filteredJournals: filtered });\n  },\n}));\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;;AAiBA,MAAM,iBAA8B;IAClC,UAAU,IAAI;IACd,SAAS,IAAI;IACb,UAAU;QAAC;QAAG;KAAM;IACpB,aAAa;QAAC;QAAG;KAAI;IACrB,YAAY;AACd;AAEO,MAAM,WAAW,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAS,CAAC,KAAK,MAAQ,CAAC;QACnD,QAAQ;QACR,UAAU,EAAE;QACZ,kBAAkB,EAAE;QACpB,kBAAkB,IAAI;QACtB,SAAS;QACT,WAAW;QACX,OAAO;QAEP,UAAU;QACV,aAAa,CAAC;YACZ,IAAI;gBAAE;YAAS;YACf,MAAM,YAAY;QACpB;QAEA,YAAY,CAAC;YACX,MAAM,iBAAiB,MAAM,OAAO;YACpC,MAAM,iBAAiB;gBAAE,GAAG,cAAc;gBAAE,GAAG,UAAU;YAAC;YAC1D,IAAI;gBAAE,SAAS;YAAe;YAC9B,MAAM,YAAY;QACpB;QAEA,qBAAqB,CAAC,WAAa,IAAI;gBAAE,kBAAkB;YAAS;QAEpE,oBAAoB,CAAC;YACnB,MAAM,UAAU,MAAM,gBAAgB;YACtC,MAAM,UAAU,IAAI,IAAI;YACxB,QAAQ,GAAG,CAAC;YACZ,IAAI;gBAAE,kBAAkB;YAAQ;QAClC;QAEA,uBAAuB,CAAC;YACtB,MAAM,UAAU,MAAM,gBAAgB;YACtC,MAAM,UAAU,IAAI,IAAI;YACxB,QAAQ,MAAM,CAAC;YACf,IAAI;gBAAE,kBAAkB;YAAQ;QAClC;QAEA,uBAAuB,IAAM,IAAI;gBAAE,kBAAkB,IAAI;YAAM;QAE/D,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAE3C,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QAEjC,cAAc;YACZ,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;YAE9B,MAAM,WAAW,SAAS,MAAM,CAAC,CAAC;gBAChC,iBAAiB;gBACjB,IAAI,QAAQ,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC,QAAQ,QAAQ,CAAC,GAAG,CAAC,QAAQ,OAAO,GAAG;oBACvE,OAAO;gBACT;gBAEA,iBAAiB;gBACjB,IAAI,QAAQ,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,QAAQ,OAAO,CAAC,GAAG,CAAC,QAAQ,OAAO,GAAG;oBACrE,OAAO;gBACT;gBAEA,wCAAwC;gBACxC,IAAI,QAAQ,OAAO,IAAI,MAAM;oBAC3B,IAAI,QAAQ,OAAO,GAAG,QAAQ,QAAQ,CAAC,EAAE,IAAI,QAAQ,OAAO,GAAG,QAAQ,QAAQ,CAAC,EAAE,EAAE;wBAClF,OAAO;oBACT;gBACF;gBAEA,sBAAsB;gBACtB,IAAI,QAAQ,YAAY,GAAG,QAAQ,WAAW,CAAC,EAAE,IAAI,QAAQ,YAAY,GAAG,QAAQ,WAAW,CAAC,EAAE,EAAE;oBAClG,OAAO;gBACT;gBAEA,qBAAqB;gBACrB,IAAI,QAAQ,UAAU,IAAI,CAAC,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,UAAU,CAAC,WAAW,KAAK;oBACjG,OAAO;gBACT;gBAEA,OAAO;YACT;YAEA,IAAI;gBAAE,kBAAkB;YAAS;QACnC;IACF,CAAC", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/FilterPanel.tsx"], "sourcesContent": ["/**\n * Filter panel component for Journal Choice Transparency application.\n */\n\n'use client';\n\nimport React, { useMemo } from 'react';\nimport { useStore } from '../store';\nimport { Search, Filter } from 'lucide-react';\n\nconst FilterPanel: React.FC = () => {\n  const {\n    journals,\n    filteredJournals,\n    filters,\n    setFilters,\n    clearSelectedJournals\n  } = useStore();\n\n  // Get unique values for filter options\n  const filterOptions = useMemo(() => {\n    const subjects = new Set(journals.map(j => j.subject).filter(Boolean));\n    const oaTypes = new Set(journals.map(j => j.oa_type).filter(Boolean));\n    \n    const maxApc = Math.max(...journals.map(j => j.apc_usd));\n    const maxImpact = Math.max(...journals.map(j => j.impact_proxy));\n    \n    return {\n      subjects: Array.from(subjects).sort(),\n      oaTypes: Array.from(oaTypes).sort(),\n      maxApc: Math.ceil(maxApc / 1000) * 1000, // Round up to nearest 1000\n      maxImpact: Math.ceil(maxImpact / 10) * 10, // Round up to nearest 10\n    };\n  }, [journals]);\n\n  const handleSubjectChange = (subject: string, checked: boolean) => {\n    const newSubjects = new Set(filters.subjects);\n    if (checked) {\n      newSubjects.add(subject);\n    } else {\n      newSubjects.delete(subject);\n    }\n    setFilters({ subjects: newSubjects });\n    clearSelectedJournals();\n  };\n\n  const handleOATypeChange = (oaType: string, checked: boolean) => {\n    const newOATypes = new Set(filters.oaTypes);\n    if (checked) {\n      newOATypes.add(oaType);\n    } else {\n      newOATypes.delete(oaType);\n    }\n    setFilters({ oaTypes: newOATypes });\n    clearSelectedJournals();\n  };\n\n  const handleSearchChange = (searchTerm: string) => {\n    setFilters({ searchTerm });\n    clearSelectedJournals();\n  };\n\n  const handleAPCRangeChange = (value: number[]) => {\n    setFilters({ apcRange: [value[0], value[1]] as [number, number] });\n    clearSelectedJournals();\n  };\n\n  const handleImpactRangeChange = (value: number[]) => {\n    setFilters({ impactRange: [value[0], value[1]] as [number, number] });\n    clearSelectedJournals();\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border p-4 max-h-[800px] lg:h-full overflow-y-auto\">\n      <div className=\"flex items-center gap-2 mb-4\">\n        <Filter className=\"w-5 h-5 text-gray-600\" />\n        <h2 className=\"text-lg font-semibold text-gray-900\">Filters</h2>\n        <span className=\"ml-auto text-sm text-gray-500\">\n          {filteredJournals.length} results\n        </span>\n      </div>\n\n      {/* Search */}\n      <div className=\"mb-6\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Search Journals\n        </label>\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n          <input\n            type=\"text\"\n            placeholder=\"Search by journal title...\"\n            value={filters.searchTerm}\n            onChange={(e) => handleSearchChange(e.target.value)}\n            className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n        </div>\n      </div>\n\n      {/* Subject Filter */}\n      <div className=\"mb-6\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Subject Area\n        </label>\n        <div className=\"max-h-40 overflow-y-auto space-y-2\">\n          {filterOptions.subjects.slice(0, 20).map((subject) => (\n            <label key={subject} className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={filters.subjects.has(subject)}\n                onChange={(e) => handleSubjectChange(subject, e.target.checked)}\n                className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n              />\n              <span className=\"ml-2 text-sm text-gray-700 truncate\">{subject}</span>\n            </label>\n          ))}\n        </div>\n      </div>\n\n      {/* Open Access Type Filter */}\n      <div className=\"mb-6\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Open Access Type\n        </label>\n        <div className=\"space-y-2\">\n          {filterOptions.oaTypes.map((oaType) => (\n            <label key={oaType} className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={filters.oaTypes.has(oaType)}\n                onChange={(e) => handleOATypeChange(oaType, e.target.checked)}\n                className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n              />\n              <span className=\"ml-2 text-sm text-gray-700\">{oaType}</span>\n            </label>\n          ))}\n        </div>\n      </div>\n\n      {/* APC Range */}\n      <div className=\"mb-6\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          APC Range (USD): ${filters.apcRange[0]} - ${filters.apcRange[1]}\n        </label>\n        <input\n          type=\"range\"\n          min=\"0\"\n          max={filterOptions.maxApc}\n          step=\"100\"\n          value={filters.apcRange[1]}\n          onChange={(e) => handleAPCRangeChange([filters.apcRange[0], parseInt(e.target.value)])}\n          className=\"w-full\"\n        />\n      </div>\n\n      {/* Impact Range */}\n      <div className=\"mb-6\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Min Impact: {filters.impactRange[0].toFixed(1)}\n        </label>\n        <input\n          type=\"range\"\n          min=\"0\"\n          max={filterOptions.maxImpact}\n          step=\"0.1\"\n          value={filters.impactRange[0]}\n          onChange={(e) => handleImpactRangeChange([parseFloat(e.target.value), filters.impactRange[1]])}\n          className=\"w-full\"\n        />\n      </div>\n\n      {/* Clear Filters */}\n      <button\n        onClick={() => {\n          setFilters({\n            subjects: new Set(),\n            oaTypes: new Set(),\n            apcRange: [0, filterOptions.maxApc],\n            impactRange: [0, filterOptions.maxImpact],\n            searchTerm: '',\n          });\n          clearSelectedJournals();\n        }}\n        className=\"w-full px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n      >\n        Clear All Filters\n      </button>\n    </div>\n  );\n};\n\nexport default FilterPanel;\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AACA;AACA;AAAA;;;AAJA;;;;AAMA,MAAM,cAAwB;;IAC5B,MAAM,EACJ,QAAQ,EACR,gBAAgB,EAChB,OAAO,EACP,UAAU,EACV,qBAAqB,EACtB,GAAG,CAAA,GAAA,sHAAA,CAAA,WAAQ,AAAD;IAEX,uCAAuC;IACvC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CAAE;YAC5B,MAAM,WAAW,IAAI,IAAI,SAAS,GAAG;sDAAC,CAAA,IAAK,EAAE,OAAO;qDAAE,MAAM,CAAC;YAC7D,MAAM,UAAU,IAAI,IAAI,SAAS,GAAG;sDAAC,CAAA,IAAK,EAAE,OAAO;qDAAE,MAAM,CAAC;YAE5D,MAAM,SAAS,KAAK,GAAG,IAAI,SAAS,GAAG;6DAAC,CAAA,IAAK,EAAE,OAAO;;YACtD,MAAM,YAAY,KAAK,GAAG,IAAI,SAAS,GAAG;gEAAC,CAAA,IAAK,EAAE,YAAY;;YAE9D,OAAO;gBACL,UAAU,MAAM,IAAI,CAAC,UAAU,IAAI;gBACnC,SAAS,MAAM,IAAI,CAAC,SAAS,IAAI;gBACjC,QAAQ,KAAK,IAAI,CAAC,SAAS,QAAQ;gBACnC,WAAW,KAAK,IAAI,CAAC,YAAY,MAAM;YACzC;QACF;6CAAG;QAAC;KAAS;IAEb,MAAM,sBAAsB,CAAC,SAAiB;QAC5C,MAAM,cAAc,IAAI,IAAI,QAAQ,QAAQ;QAC5C,IAAI,SAAS;YACX,YAAY,GAAG,CAAC;QAClB,OAAO;YACL,YAAY,MAAM,CAAC;QACrB;QACA,WAAW;YAAE,UAAU;QAAY;QACnC;IACF;IAEA,MAAM,qBAAqB,CAAC,QAAgB;QAC1C,MAAM,aAAa,IAAI,IAAI,QAAQ,OAAO;QAC1C,IAAI,SAAS;YACX,WAAW,GAAG,CAAC;QACjB,OAAO;YACL,WAAW,MAAM,CAAC;QACpB;QACA,WAAW;YAAE,SAAS;QAAW;QACjC;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,WAAW;YAAE;QAAW;QACxB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,WAAW;YAAE,UAAU;gBAAC,KAAK,CAAC,EAAE;gBAAE,KAAK,CAAC,EAAE;aAAC;QAAqB;QAChE;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,WAAW;YAAE,aAAa;gBAAC,KAAK,CAAC,EAAE;gBAAE,KAAK,CAAC,EAAE;aAAC;QAAqB;QACnE;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAK,WAAU;;4BACb,iBAAiB,MAAM;4BAAC;;;;;;;;;;;;;0BAK7B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO,QAAQ,UAAU;gCACzB,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gCAClD,WAAU;;;;;;;;;;;;;;;;;;0BAMhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBAAI,WAAU;kCACZ,cAAc,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,wBACxC,6LAAC;gCAAoB,WAAU;;kDAC7B,6LAAC;wCACC,MAAK;wCACL,SAAS,QAAQ,QAAQ,CAAC,GAAG,CAAC;wCAC9B,UAAU,CAAC,IAAM,oBAAoB,SAAS,EAAE,MAAM,CAAC,OAAO;wCAC9D,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;kDAAuC;;;;;;;+BAP7C;;;;;;;;;;;;;;;;0BAclB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBAAI,WAAU;kCACZ,cAAc,OAAO,CAAC,GAAG,CAAC,CAAC,uBAC1B,6LAAC;gCAAmB,WAAU;;kDAC5B,6LAAC;wCACC,MAAK;wCACL,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;wCAC7B,UAAU,CAAC,IAAM,mBAAmB,QAAQ,EAAE,MAAM,CAAC,OAAO;wCAC5D,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;kDAA8B;;;;;;;+BAPpC;;;;;;;;;;;;;;;;0BAclB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;;4BAA+C;4BAC3C,QAAQ,QAAQ,CAAC,EAAE;4BAAC;4BAAK,QAAQ,QAAQ,CAAC,EAAE;;;;;;;kCAEjE,6LAAC;wBACC,MAAK;wBACL,KAAI;wBACJ,KAAK,cAAc,MAAM;wBACzB,MAAK;wBACL,OAAO,QAAQ,QAAQ,CAAC,EAAE;wBAC1B,UAAU,CAAC,IAAM,qBAAqB;gCAAC,QAAQ,QAAQ,CAAC,EAAE;gCAAE,SAAS,EAAE,MAAM,CAAC,KAAK;6BAAE;wBACrF,WAAU;;;;;;;;;;;;0BAKd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;;4BAA+C;4BACjD,QAAQ,WAAW,CAAC,EAAE,CAAC,OAAO,CAAC;;;;;;;kCAE9C,6LAAC;wBACC,MAAK;wBACL,KAAI;wBACJ,KAAK,cAAc,SAAS;wBAC5B,MAAK;wBACL,OAAO,QAAQ,WAAW,CAAC,EAAE;wBAC7B,UAAU,CAAC,IAAM,wBAAwB;gCAAC,WAAW,EAAE,MAAM,CAAC,KAAK;gCAAG,QAAQ,WAAW,CAAC,EAAE;6BAAC;wBAC7F,WAAU;;;;;;;;;;;;0BAKd,6LAAC;gBACC,SAAS;oBACP,WAAW;wBACT,UAAU,IAAI;wBACd,SAAS,IAAI;wBACb,UAAU;4BAAC;4BAAG,cAAc,MAAM;yBAAC;wBACnC,aAAa;4BAAC;4BAAG,cAAc,SAAS;yBAAC;wBACzC,YAAY;oBACd;oBACA;gBACF;gBACA,WAAU;0BACX;;;;;;;;;;;;AAKP;GAnLM;;QAOA,sHAAA,CAAA,WAAQ;;;KAPR;uCAqLS", "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/ScatterPlot.tsx"], "sourcesContent": ["/**\n * Scatter plot component for Journal Choice Transparency application.\n */\n\n'use client';\n\nimport React, { useMemo } from 'react';\nimport {\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  ResponsiveContainer,\n  Legend,\n} from 'recharts';\nimport { useStore } from '../store';\n\nconst OA_TYPE_COLORS = {\n  'Fully OA': '#10B981', // Green\n  'Hybrid': '#F59E0B',   // Amber\n  'Diamond': '#8B5CF6',  // Purple\n  'Subscription': '#EF4444', // Red\n  'Unknown': '#6B7280',  // Gray\n};\n\nconst ScatterPlot: React.FC = () => {\n  const { filteredJournals, selectedJournals, addSelectedJournal, removeSelectedJournal } = useStore();\n\n  // Prepare data for scatter plot - sample for performance if too many points\n  const scatterData = useMemo(() => {\n    let journals = filteredJournals;\n\n    // If we have too many journals, sample them for better performance\n    if (journals.length > 5000) {\n      // Sort by impact and take top journals plus random sample\n      const sorted = [...journals].sort((a, b) => b.impact_proxy - a.impact_proxy);\n      const topJournals = sorted.slice(0, 2000);\n      const remaining = sorted.slice(2000);\n      const randomSample = remaining\n        .sort(() => 0.5 - Math.random())\n        .slice(0, 3000);\n      journals = [...topJournals, ...randomSample];\n    }\n\n    // Filter out journals with null APC for scatter plot\n    const journalsWithAPC = journals.filter(j => j.apc_usd != null && !isNaN(j.apc_usd));\n\n    return journalsWithAPC.map((journal) => ({\n      x: journal.impact_proxy,\n      y: journal.apc_usd,\n      title: journal.title,\n      oa_type: journal.oa_type,\n      cost_efficiency: typeof journal.cost_efficiency === 'number' && journal.cost_efficiency === 999999 ? Infinity : journal.cost_efficiency,\n      issn_l: journal.issn_l,\n      publisher: journal.publisher,\n      selected: selectedJournals.has(journal.issn_l),\n    }));\n  }, [filteredJournals, selectedJournals]);\n\n  // Group data by OA type for multiple scatter series\n  const dataByOAType = useMemo(() => {\n    const grouped: Record<string, typeof scatterData> = {};\n    scatterData.forEach((point) => {\n      if (!grouped[point.oa_type]) {\n        grouped[point.oa_type] = [];\n      }\n      grouped[point.oa_type].push(point);\n    });\n    return grouped;\n  }, [scatterData]);\n\n  const CustomTooltip = ({ active, payload }: any) => {\n    if (active && payload && payload.length) {\n      const data = payload[0].payload;\n      return (\n        <div className=\"bg-white p-3 border border-gray-300 rounded-lg shadow-lg\">\n          <p className=\"font-semibold text-gray-900 mb-1\">{data.title}</p>\n          <p className=\"text-sm text-gray-600\">Impact: {data.x.toFixed(2)}</p>\n          <p className=\"text-sm text-gray-600\">APC: {data.y != null ? `$${data.y.toLocaleString()}` : 'N/A'}</p>\n          <p className=\"text-sm text-gray-600\">OA Type: {data.oa_type}</p>\n          <p className=\"text-sm text-gray-600\">Publisher: {data.publisher}</p>\n          <p className=\"text-sm text-gray-600\">\n            Cost Efficiency: {\n              data.cost_efficiency === Infinity \n                ? '∞' \n                : data.cost_efficiency.toFixed(4)\n            }\n          </p>\n        </div>\n      );\n    }\n    return null;\n  };\n\n  const handlePointClick = (data: any) => {\n    if (selectedJournals.has(data.issn_l)) {\n      removeSelectedJournal(data.issn_l);\n    } else {\n      addSelectedJournal(data.issn_l);\n    }\n  };\n\n  return (\n    <div className=\"h-full\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">\n          Impact vs APC Cost\n        </h3>\n        <div className=\"text-sm text-gray-600\">\n          {scatterData.length} journals with APC data (of {filteredJournals.length} total)\n        </div>\n      </div>\n\n      <ResponsiveContainer width=\"100%\" height=\"90%\">\n        <ScatterChart\n          margin={{\n            top: 20,\n            right: 20,\n            bottom: 60,\n            left: 60,\n          }}\n        >\n          <CartesianGrid strokeDasharray=\"3 3\" />\n          <XAxis\n            type=\"number\"\n            dataKey=\"x\"\n            name=\"Impact Proxy\"\n            domain={['dataMin', 'dataMax']}\n            label={{ value: 'Impact Proxy (Citations per Doc)', position: 'insideBottom', offset: -10 }}\n          />\n          <YAxis\n            type=\"number\"\n            dataKey=\"y\"\n            name=\"APC (USD)\"\n            domain={['dataMin', 'dataMax']}\n            label={{ value: 'APC (USD)', angle: -90, position: 'insideLeft' }}\n          />\n          <Tooltip content={<CustomTooltip />} />\n          <Legend />\n          \n          {Object.entries(dataByOAType).map(([oaType, data]) => (\n            <Scatter\n              key={oaType}\n              name={oaType}\n              data={data}\n              fill={OA_TYPE_COLORS[oaType as keyof typeof OA_TYPE_COLORS] || '#6B7280'}\n              onClick={handlePointClick}\n              style={{ cursor: 'pointer' }}\n            />\n          ))}\n        </ScatterChart>\n      </ResponsiveContainer>\n\n      <div className=\"mt-4 text-xs text-gray-500\">\n        Click points to select journals. Selected journals will be highlighted in the table below.\n      </div>\n    </div>\n  );\n};\n\nexport default ScatterPlot;\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;;;AAbA;;;;AAeA,MAAM,iBAAiB;IACrB,YAAY;IACZ,UAAU;IACV,WAAW;IACX,gBAAgB;IAChB,WAAW;AACb;AAEA,MAAM,cAAwB;;IAC5B,MAAM,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,qBAAqB,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,WAAQ,AAAD;IAEjG,4EAA4E;IAC5E,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4CAAE;YAC1B,IAAI,WAAW;YAEf,mEAAmE;YACnE,IAAI,SAAS,MAAM,GAAG,MAAM;gBAC1B,0DAA0D;gBAC1D,MAAM,SAAS;uBAAI;iBAAS,CAAC,IAAI;+DAAC,CAAC,GAAG,IAAM,EAAE,YAAY,GAAG,EAAE,YAAY;;gBAC3E,MAAM,cAAc,OAAO,KAAK,CAAC,GAAG;gBACpC,MAAM,YAAY,OAAO,KAAK,CAAC;gBAC/B,MAAM,eAAe,UAClB,IAAI;qEAAC,IAAM,MAAM,KAAK,MAAM;oEAC5B,KAAK,CAAC,GAAG;gBACZ,WAAW;uBAAI;uBAAgB;iBAAa;YAC9C;YAEA,qDAAqD;YACrD,MAAM,kBAAkB,SAAS,MAAM;oEAAC,CAAA,IAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,MAAM,EAAE,OAAO;;YAElF,OAAO,gBAAgB,GAAG;oDAAC,CAAC,UAAY,CAAC;wBACvC,GAAG,QAAQ,YAAY;wBACvB,GAAG,QAAQ,OAAO;wBAClB,OAAO,QAAQ,KAAK;wBACpB,SAAS,QAAQ,OAAO;wBACxB,iBAAiB,OAAO,QAAQ,eAAe,KAAK,YAAY,QAAQ,eAAe,KAAK,SAAS,WAAW,QAAQ,eAAe;wBACvI,QAAQ,QAAQ,MAAM;wBACtB,WAAW,QAAQ,SAAS;wBAC5B,UAAU,iBAAiB,GAAG,CAAC,QAAQ,MAAM;oBAC/C,CAAC;;QACH;2CAAG;QAAC;QAAkB;KAAiB;IAEvC,oDAAoD;IACpD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;6CAAE;YAC3B,MAAM,UAA8C,CAAC;YACrD,YAAY,OAAO;qDAAC,CAAC;oBACnB,IAAI,CAAC,OAAO,CAAC,MAAM,OAAO,CAAC,EAAE;wBAC3B,OAAO,CAAC,MAAM,OAAO,CAAC,GAAG,EAAE;oBAC7B;oBACA,OAAO,CAAC,MAAM,OAAO,CAAC,CAAC,IAAI,CAAC;gBAC9B;;YACA,OAAO;QACT;4CAAG;QAAC;KAAY;IAEhB,MAAM,gBAAgB;YAAC,EAAE,MAAM,EAAE,OAAO,EAAO;QAC7C,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;YACvC,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,OAAO;YAC/B,qBACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAoC,KAAK,KAAK;;;;;;kCAC3D,6LAAC;wBAAE,WAAU;;4BAAwB;4BAAS,KAAK,CAAC,CAAC,OAAO,CAAC;;;;;;;kCAC7D,6LAAC;wBAAE,WAAU;;4BAAwB;4BAAM,KAAK,CAAC,IAAI,OAAO,AAAC,IAA2B,OAAxB,KAAK,CAAC,CAAC,cAAc,MAAO;;;;;;;kCAC5F,6LAAC;wBAAE,WAAU;;4BAAwB;4BAAU,KAAK,OAAO;;;;;;;kCAC3D,6LAAC;wBAAE,WAAU;;4BAAwB;4BAAY,KAAK,SAAS;;;;;;;kCAC/D,6LAAC;wBAAE,WAAU;;4BAAwB;4BAEjC,KAAK,eAAe,KAAK,WACrB,MACA,KAAK,eAAe,CAAC,OAAO,CAAC;;;;;;;;;;;;;QAK3C;QACA,OAAO;IACT;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,iBAAiB,GAAG,CAAC,KAAK,MAAM,GAAG;YACrC,sBAAsB,KAAK,MAAM;QACnC,OAAO;YACL,mBAAmB,KAAK,MAAM;QAChC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAGpD,6LAAC;wBAAI,WAAU;;4BACZ,YAAY,MAAM;4BAAC;4BAA6B,iBAAiB,MAAM;4BAAC;;;;;;;;;;;;;0BAI7E,6LAAC,sKAAA,CAAA,sBAAmB;gBAAC,OAAM;gBAAO,QAAO;0BACvC,cAAA,6LAAC,2JAAA,CAAA,eAAY;oBACX,QAAQ;wBACN,KAAK;wBACL,OAAO;wBACP,QAAQ;wBACR,MAAM;oBACR;;sCAEA,6LAAC,gKAAA,CAAA,gBAAa;4BAAC,iBAAgB;;;;;;sCAC/B,6LAAC,wJAAA,CAAA,QAAK;4BACJ,MAAK;4BACL,SAAQ;4BACR,MAAK;4BACL,QAAQ;gCAAC;gCAAW;6BAAU;4BAC9B,OAAO;gCAAE,OAAO;gCAAoC,UAAU;gCAAgB,QAAQ,CAAC;4BAAG;;;;;;sCAE5F,6LAAC,wJAAA,CAAA,QAAK;4BACJ,MAAK;4BACL,SAAQ;4BACR,MAAK;4BACL,QAAQ;gCAAC;gCAAW;6BAAU;4BAC9B,OAAO;gCAAE,OAAO;gCAAa,OAAO,CAAC;gCAAI,UAAU;4BAAa;;;;;;sCAElE,6LAAC,0JAAA,CAAA,UAAO;4BAAC,uBAAS,6LAAC;;;;;;;;;;sCACnB,6LAAC,yJAAA,CAAA,SAAM;;;;;wBAEN,OAAO,OAAO,CAAC,cAAc,GAAG,CAAC;gCAAC,CAAC,QAAQ,KAAK;iDAC/C,6LAAC,0JAAA,CAAA,UAAO;gCAEN,MAAM;gCACN,MAAM;gCACN,MAAM,cAAc,CAAC,OAAsC,IAAI;gCAC/D,SAAS;gCACT,OAAO;oCAAE,QAAQ;gCAAU;+BALtB;;;;;;;;;;;;;;;;;0BAWb,6LAAC;gBAAI,WAAU;0BAA6B;;;;;;;;;;;;AAKlD;GArIM;;QACsF,sHAAA,CAAA,WAAQ;;;KAD9F;uCAuIS", "debugId": null}}, {"offset": {"line": 862, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/ExportButton.tsx"], "sourcesContent": ["/**\n * Export button component for Journal Choice Transparency application.\n */\n\n'use client';\n\nimport React from 'react';\nimport { useStore } from '../store';\nimport { Download } from 'lucide-react';\n\nconst ExportButton: React.FC = () => {\n  const { filteredJournals, selectedJournals } = useStore();\n\n  const exportToCSV = (journals: any[], filename: string) => {\n    if (journals.length === 0) return;\n\n    // Define CSV headers\n    const headers = [\n      'Title',\n      'ISSN-L',\n      'Subject',\n      'Impact Proxy',\n      'APC (USD)',\n      'OA Type',\n      'License',\n      'Cost Efficiency',\n      'Publisher',\n      'Last Verified',\n    ];\n\n    // Convert journals to CSV rows\n    const csvRows = [\n      headers.join(','),\n      ...journals.map(journal => [\n        `\"${journal.title.replace(/\"/g, '\"\"')}\"`,\n        journal.issn_l,\n        `\"${journal.subject.replace(/\"/g, '\"\"')}\"`,\n        journal.impact_proxy.toFixed(2),\n        journal.apc_usd != null ? journal.apc_usd : 'N/A',\n        `\"${journal.oa_type}\"`,\n        `\"${journal.license}\"`,\n        journal.cost_efficiency == null ? 'N/A' : journal.cost_efficiency === Infinity ? 'Infinity' : journal.cost_efficiency.toFixed(4),\n        `\"${journal.publisher.replace(/\"/g, '\"\"')}\"`,\n        journal.last_verified,\n      ].join(','))\n    ];\n\n    // Create and download CSV file\n    const csvContent = csvRows.join('\\n');\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\n    const link = document.createElement('a');\n    \n    if (link.download !== undefined) {\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', filename);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n  };\n\n  const handleExportSelected = () => {\n    const selectedJournalsList = filteredJournals.filter(journal => \n      selectedJournals.has(journal.issn_l)\n    );\n    exportToCSV(selectedJournalsList, 'selected_journals.csv');\n  };\n\n  const handleExportFiltered = () => {\n    exportToCSV(filteredJournals, 'filtered_journals.csv');\n  };\n\n  return (\n    <div className=\"flex items-center gap-2\">\n      <button\n        onClick={handleExportFiltered}\n        disabled={filteredJournals.length === 0}\n        className=\"flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n      >\n        <Download className=\"w-4 h-4\" />\n        Export Filtered ({filteredJournals.length})\n      </button>\n      \n      {selectedJournals.size > 0 && (\n        <button\n          onClick={handleExportSelected}\n          className=\"flex items-center gap-2 px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n        >\n          <Download className=\"w-4 h-4\" />\n          Export Selected ({selectedJournals.size})\n        </button>\n      )}\n    </div>\n  );\n};\n\nexport default ExportButton;\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAKD;AACA;;;AAJA;;;AAMA,MAAM,eAAyB;;IAC7B,MAAM,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,WAAQ,AAAD;IAEtD,MAAM,cAAc,CAAC,UAAiB;QACpC,IAAI,SAAS,MAAM,KAAK,GAAG;QAE3B,qBAAqB;QACrB,MAAM,UAAU;YACd;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,+BAA+B;QAC/B,MAAM,UAAU;YACd,QAAQ,IAAI,CAAC;eACV,SAAS,GAAG,CAAC,CAAA,UAAW;oBACxB,IAAqC,OAAlC,QAAQ,KAAK,CAAC,OAAO,CAAC,MAAM,OAAM;oBACtC,QAAQ,MAAM;oBACb,IAAuC,OAApC,QAAQ,OAAO,CAAC,OAAO,CAAC,MAAM,OAAM;oBACxC,QAAQ,YAAY,CAAC,OAAO,CAAC;oBAC7B,QAAQ,OAAO,IAAI,OAAO,QAAQ,OAAO,GAAG;oBAC3C,IAAmB,OAAhB,QAAQ,OAAO,EAAC;oBACnB,IAAmB,OAAhB,QAAQ,OAAO,EAAC;oBACpB,QAAQ,eAAe,IAAI,OAAO,QAAQ,QAAQ,eAAe,KAAK,WAAW,aAAa,QAAQ,eAAe,CAAC,OAAO,CAAC;oBAC7H,IAAyC,OAAtC,QAAQ,SAAS,CAAC,OAAO,CAAC,MAAM,OAAM;oBAC1C,QAAQ,aAAa;iBACtB,CAAC,IAAI,CAAC;SACR;QAED,+BAA+B;QAC/B,MAAM,aAAa,QAAQ,IAAI,CAAC;QAChC,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAA0B;QACtE,MAAM,OAAO,SAAS,aAAa,CAAC;QAEpC,IAAI,KAAK,QAAQ,KAAK,WAAW;YAC/B,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,KAAK,YAAY,CAAC,QAAQ;YAC1B,KAAK,YAAY,CAAC,YAAY;YAC9B,KAAK,KAAK,CAAC,UAAU,GAAG;YACxB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;IACF;IAEA,MAAM,uBAAuB;QAC3B,MAAM,uBAAuB,iBAAiB,MAAM,CAAC,CAAA,UACnD,iBAAiB,GAAG,CAAC,QAAQ,MAAM;QAErC,YAAY,sBAAsB;IACpC;IAEA,MAAM,uBAAuB;QAC3B,YAAY,kBAAkB;IAChC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,SAAS;gBACT,UAAU,iBAAiB,MAAM,KAAK;gBACtC,WAAU;;kCAEV,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAAY;oBACd,iBAAiB,MAAM;oBAAC;;;;;;;YAG3C,iBAAiB,IAAI,GAAG,mBACvB,6LAAC;gBACC,SAAS;gBACT,WAAU;;kCAEV,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAAY;oBACd,iBAAiB,IAAI;oBAAC;;;;;;;;;;;;;AAKlD;GAtFM;;QAC2C,sHAAA,CAAA,WAAQ;;;KADnD;uCAwFS", "debugId": null}}, {"offset": {"line": 1003, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/DataTable.tsx"], "sourcesContent": ["/**\n * Data table component for Journal Choice Transparency application.\n */\n\n'use client';\n\nimport React, { useMemo } from 'react';\nimport {\n  useReactTable,\n  getCoreRowModel,\n  getSortedRowModel,\n  flexRender,\n  createColumnHelper,\n  SortingState,\n} from '@tanstack/react-table';\nimport { useStore } from '../store';\nimport { Journal } from '../types';\nimport { ExternalLink, ArrowUpDown } from 'lucide-react';\nimport ExportButton from './ExportButton';\n\nconst columnHelper = createColumnHelper<Journal>();\n\nconst DataTable: React.FC = () => {\n  const { \n    filteredJournals, \n    selectedJournals, \n    addSelectedJournal, \n    removeSelectedJournal \n  } = useStore();\n\n  const [sorting, setSorting] = React.useState<SortingState>([\n    { id: 'impact_proxy', desc: true }\n  ]);\n\n  const columns = useMemo(\n    () => [\n      columnHelper.accessor('title', {\n        header: 'Journal Title',\n        cell: (info) => (\n          <div className=\"flex items-center gap-2\">\n            <span className=\"font-medium text-gray-900 truncate max-w-xs\">\n              {info.getValue()}\n            </span>\n            {info.row.original.journal_url && (\n              <a\n                href={info.row.original.journal_url}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-blue-600 hover:text-blue-800\"\n              >\n                <ExternalLink className=\"w-4 h-4\" />\n              </a>\n            )}\n          </div>\n        ),\n        size: 300,\n      }),\n      columnHelper.accessor('subject', {\n        header: 'Subject',\n        cell: (info) => (\n          <span className=\"text-sm text-gray-700 truncate\">\n            {info.getValue()}\n          </span>\n        ),\n        size: 150,\n      }),\n      columnHelper.accessor('impact_proxy', {\n        header: ({ column }) => (\n          <button\n            className=\"flex items-center gap-1 hover:text-blue-600\"\n            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\n          >\n            Impact\n            <ArrowUpDown className=\"w-4 h-4\" />\n          </button>\n        ),\n        cell: (info) => (\n          <span className=\"font-mono text-sm\">\n            {info.getValue().toFixed(2)}\n          </span>\n        ),\n        size: 100,\n      }),\n      columnHelper.accessor('apc_usd', {\n        header: ({ column }) => (\n          <button\n            className=\"flex items-center gap-1 hover:text-blue-600\"\n            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\n          >\n            APC (USD)\n            <ArrowUpDown className=\"w-4 h-4\" />\n          </button>\n        ),\n        cell: (info) => {\n          const value = info.getValue();\n          return (\n            <span className=\"font-mono text-sm\">\n              {value != null ? `$${value.toLocaleString()}` : 'N/A'}\n            </span>\n          );\n        },\n        size: 120,\n      }),\n      columnHelper.accessor('cost_efficiency', {\n        header: ({ column }) => (\n          <button\n            className=\"flex items-center gap-1 hover:text-blue-600\"\n            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\n          >\n            Cost Efficiency\n            <ArrowUpDown className=\"w-4 h-4\" />\n          </button>\n        ),\n        cell: (info) => {\n          const value = info.getValue();\n          return (\n            <span className=\"font-mono text-sm\">\n              {value == null ? 'N/A' : value === Infinity ? '∞' : value.toFixed(4)}\n            </span>\n          );\n        },\n        size: 130,\n      }),\n      columnHelper.accessor('oa_type', {\n        header: 'OA Type',\n        cell: (info) => {\n          const oaType = info.getValue();\n          const colorClass = {\n            'Fully OA': 'bg-green-100 text-green-800',\n            'Hybrid': 'bg-yellow-100 text-yellow-800',\n            'Diamond': 'bg-purple-100 text-purple-800',\n            'Subscription': 'bg-red-100 text-red-800',\n            'Unknown': 'bg-gray-100 text-gray-800',\n          }[oaType] || 'bg-gray-100 text-gray-800';\n          \n          return (\n            <span className={`px-2 py-1 rounded-full text-xs font-medium ${colorClass}`}>\n              {oaType}\n            </span>\n          );\n        },\n        size: 120,\n      }),\n      columnHelper.accessor('publisher', {\n        header: 'Publisher',\n        cell: (info) => (\n          <span className=\"text-sm text-gray-700 truncate max-w-xs\">\n            {info.getValue()}\n          </span>\n        ),\n        size: 200,\n      }),\n    ],\n    []\n  );\n\n  const table = useReactTable({\n    data: filteredJournals,\n    columns,\n    state: {\n      sorting,\n    },\n    onSortingChange: setSorting,\n    getCoreRowModel: getCoreRowModel(),\n    getSortedRowModel: getSortedRowModel(),\n  });\n\n  const handleRowClick = (journal: Journal) => {\n    if (selectedJournals.has(journal.issn_l)) {\n      removeSelectedJournal(journal.issn_l);\n    } else {\n      addSelectedJournal(journal.issn_l);\n    }\n  };\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">\n          Journal Data\n        </h3>\n        <div className=\"flex items-center gap-4\">\n          <div className=\"flex items-center gap-4 text-sm text-gray-600\">\n            <span>{filteredJournals.length} journals</span>\n            {selectedJournals.size > 0 && (\n              <span className=\"text-blue-600 font-medium\">\n                {selectedJournals.size} selected\n              </span>\n            )}\n          </div>\n          <ExportButton />\n        </div>\n      </div>\n\n      <div className=\"flex-1 overflow-auto border border-gray-200 rounded-lg\">\n        <table className=\"min-w-full divide-y divide-gray-200\">\n          <thead className=\"bg-gray-50 sticky top-0\">\n            {table.getHeaderGroups().map((headerGroup) => (\n              <tr key={headerGroup.id}>\n                {headerGroup.headers.map((header) => (\n                  <th\n                    key={header.id}\n                    className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n                    style={{ width: header.getSize() }}\n                  >\n                    {header.isPlaceholder\n                      ? null\n                      : flexRender(\n                          header.column.columnDef.header,\n                          header.getContext()\n                        )}\n                  </th>\n                ))}\n              </tr>\n            ))}\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {table.getRowModel().rows.map((row) => {\n              const isSelected = selectedJournals.has(row.original.issn_l);\n              return (\n                <tr\n                  key={row.id}\n                  onClick={() => handleRowClick(row.original)}\n                  className={`cursor-pointer hover:bg-gray-50 ${\n                    isSelected ? 'bg-blue-50 border-l-4 border-blue-500' : ''\n                  }`}\n                >\n                  {row.getVisibleCells().map((cell) => (\n                    <td\n                      key={cell.id}\n                      className=\"px-4 py-3 whitespace-nowrap text-sm\"\n                    >\n                      {flexRender(\n                        cell.column.columnDef.cell,\n                        cell.getContext()\n                      )}\n                    </td>\n                  ))}\n                </tr>\n              );\n            })}\n          </tbody>\n        </table>\n      </div>\n\n      <div className=\"mt-4 text-xs text-gray-500\">\n        Click rows to select journals. Selected journals will be highlighted in the scatter plot above.\n      </div>\n    </div>\n  );\n};\n\nexport default DataTable;\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AACA;AAAA;AAQA;AAEA;AAAA;AACA;;;AAdA;;;;;;AAgBA,MAAM,eAAe,CAAA,GAAA,wKAAA,CAAA,qBAAkB,AAAD;AAEtC,MAAM,YAAsB;;IAC1B,MAAM,EACJ,gBAAgB,EAChB,gBAAgB,EAChB,kBAAkB,EAClB,qBAAqB,EACtB,GAAG,CAAA,GAAA,sHAAA,CAAA,WAAQ,AAAD;IAEX,MAAM,CAAC,SAAS,WAAW,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAe;QACzD;YAAE,IAAI;YAAgB,MAAM;QAAK;KAClC;IAED,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;sCACpB,IAAM;gBACJ,aAAa,QAAQ,CAAC,SAAS;oBAC7B,QAAQ;oBACR,IAAI;sDAAE,CAAC,qBACL,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDACb,KAAK,QAAQ;;;;;;oCAEf,KAAK,GAAG,CAAC,QAAQ,CAAC,WAAW,kBAC5B,6LAAC;wCACC,MAAM,KAAK,GAAG,CAAC,QAAQ,CAAC,WAAW;wCACnC,QAAO;wCACP,KAAI;wCACJ,WAAU;kDAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;;oBAKhC,MAAM;gBACR;gBACA,aAAa,QAAQ,CAAC,WAAW;oBAC/B,QAAQ;oBACR,IAAI;sDAAE,CAAC,qBACL,6LAAC;gCAAK,WAAU;0CACb,KAAK,QAAQ;;;;;;;oBAGlB,MAAM;gBACR;gBACA,aAAa,QAAQ,CAAC,gBAAgB;oBACpC,MAAM;sDAAE;gCAAC,EAAE,MAAM,EAAE;iDACjB,6LAAC;gCACC,WAAU;gCACV,OAAO;kEAAE,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;;;oCAC9D;kDAEC,6LAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;oBAG3B,IAAI;sDAAE,CAAC,qBACL,6LAAC;gCAAK,WAAU;0CACb,KAAK,QAAQ,GAAG,OAAO,CAAC;;;;;;;oBAG7B,MAAM;gBACR;gBACA,aAAa,QAAQ,CAAC,WAAW;oBAC/B,MAAM;sDAAE;gCAAC,EAAE,MAAM,EAAE;iDACjB,6LAAC;gCACC,WAAU;gCACV,OAAO;kEAAE,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;;;oCAC9D;kDAEC,6LAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;oBAG3B,IAAI;sDAAE,CAAC;4BACL,MAAM,QAAQ,KAAK,QAAQ;4BAC3B,qBACE,6LAAC;gCAAK,WAAU;0CACb,SAAS,OAAO,AAAC,IAA0B,OAAvB,MAAM,cAAc,MAAO;;;;;;wBAGtD;;oBACA,MAAM;gBACR;gBACA,aAAa,QAAQ,CAAC,mBAAmB;oBACvC,MAAM;sDAAE;gCAAC,EAAE,MAAM,EAAE;iDACjB,6LAAC;gCACC,WAAU;gCACV,OAAO;kEAAE,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;;;oCAC9D;kDAEC,6LAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;oBAG3B,IAAI;sDAAE,CAAC;4BACL,MAAM,QAAQ,KAAK,QAAQ;4BAC3B,qBACE,6LAAC;gCAAK,WAAU;0CACb,SAAS,OAAO,QAAQ,UAAU,WAAW,MAAM,MAAM,OAAO,CAAC;;;;;;wBAGxE;;oBACA,MAAM;gBACR;gBACA,aAAa,QAAQ,CAAC,WAAW;oBAC/B,QAAQ;oBACR,IAAI;sDAAE,CAAC;4BACL,MAAM,SAAS,KAAK,QAAQ;4BAC5B,MAAM,aAAa;gCACjB,YAAY;gCACZ,UAAU;gCACV,WAAW;gCACX,gBAAgB;gCAChB,WAAW;4BACb,CAAC,CAAC,OAAO,IAAI;4BAEb,qBACE,6LAAC;gCAAK,WAAW,AAAC,8CAAwD,OAAX;0CAC5D;;;;;;wBAGP;;oBACA,MAAM;gBACR;gBACA,aAAa,QAAQ,CAAC,aAAa;oBACjC,QAAQ;oBACR,IAAI;sDAAE,CAAC,qBACL,6LAAC;gCAAK,WAAU;0CACb,KAAK,QAAQ;;;;;;;oBAGlB,MAAM;gBACR;aACD;qCACD,EAAE;IAGJ,MAAM,QAAQ,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B,MAAM;QACN;QACA,OAAO;YACL;QACF;QACA,iBAAiB;QACjB,iBAAiB,CAAA,GAAA,wKAAA,CAAA,kBAAe,AAAD;QAC/B,mBAAmB,CAAA,GAAA,wKAAA,CAAA,oBAAiB,AAAD;IACrC;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,iBAAiB,GAAG,CAAC,QAAQ,MAAM,GAAG;YACxC,sBAAsB,QAAQ,MAAM;QACtC,OAAO;YACL,mBAAmB,QAAQ,MAAM;QACnC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAGpD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAM,iBAAiB,MAAM;4CAAC;;;;;;;oCAC9B,iBAAiB,IAAI,GAAG,mBACvB,6LAAC;wCAAK,WAAU;;4CACb,iBAAiB,IAAI;4CAAC;;;;;;;;;;;;;0CAI7B,6LAAC,4IAAA,CAAA,UAAY;;;;;;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;4BAAM,WAAU;sCACd,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAC5B,6LAAC;8CACE,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,uBACxB,6LAAC;4CAEC,WAAU;4CACV,OAAO;gDAAE,OAAO,OAAO,OAAO;4CAAG;sDAEhC,OAAO,aAAa,GACjB,OACA,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACP,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;2CARlB,OAAO,EAAE;;;;;mCAHX,YAAY,EAAE;;;;;;;;;;sCAkB3B,6LAAC;4BAAM,WAAU;sCACd,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;gCAC7B,MAAM,aAAa,iBAAiB,GAAG,CAAC,IAAI,QAAQ,CAAC,MAAM;gCAC3D,qBACE,6LAAC;oCAEC,SAAS,IAAM,eAAe,IAAI,QAAQ;oCAC1C,WAAW,AAAC,mCAEX,OADC,aAAa,0CAA0C;8CAGxD,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,qBAC1B,6LAAC;4CAEC,WAAU;sDAET,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACR,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;2CALZ,KAAK,EAAE;;;;;mCARX,IAAI,EAAE;;;;;4BAmBjB;;;;;;;;;;;;;;;;;0BAKN,6LAAC;gBAAI,WAAU;0BAA6B;;;;;;;;;;;;AAKlD;GApOM;;QAMA,sHAAA,CAAA,WAAQ;QAgIE,yLAAA,CAAA,gBAAa;;;KAtIvB;uCAsOS", "debugId": null}}, {"offset": {"line": 1431, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/Header.tsx"], "sourcesContent": ["/**\n * Header component for Journal Choice Transparency application.\n */\n\nimport React from 'react';\n\nconst Header: React.FC = () => {\n  return (\n    <header className=\"bg-white shadow-sm border-b\">\n      <div className=\"container mx-auto px-4 py-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">\n              Journal Choice Transparency\n            </h1>\n            <p className=\"text-gray-600 mt-2\">\n              Find the right journal for your research based on impact, cost, and openness\n            </p>\n          </div>\n          \n          <div className=\"flex items-center space-x-4\">\n            <button className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium\">\n              Subscribe for Updates\n            </button>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAID,MAAM,SAAmB;IACvB,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAKpC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAO,WAAU;sCAA4E;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1G;KAvBM;uCAyBS", "debugId": null}}, {"offset": {"line": 1515, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/Footer.tsx"], "sourcesContent": ["/**\n * Footer component for Journal Choice Transparency application.\n */\n\nimport React from 'react';\n\nconst Footer: React.FC = () => {\n  return (\n    <footer className=\"bg-white border-t mt-8\">\n      <div className=\"container mx-auto px-4 py-6\">\n        <div className=\"flex flex-col md:flex-row justify-between items-center\">\n          <div className=\"text-sm text-gray-600 mb-4 md:mb-0\">\n            <p>\n              Data sources: SciMago Journal Rankings, MDPI, OpenAPC, DOAJ\n            </p>\n            <p className=\"mt-1\">\n              Last updated: July 19, 2025\n            </p>\n          </div>\n          \n          <div className=\"text-sm text-gray-600\">\n            © 2025 Journal Choice Transparency\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAID,MAAM,SAAmB;IACvB,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAE;;;;;;0CAGH,6LAAC;gCAAE,WAAU;0CAAO;;;;;;;;;;;;kCAKtB,6LAAC;wBAAI,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAOjD;KArBM;uCAuBS", "debugId": null}}, {"offset": {"line": 1592, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/SummaryStats.tsx"], "sourcesContent": ["/**\n * Summary statistics component for Journal Choice Transparency application.\n */\n\n'use client';\n\nimport React, { useMemo } from 'react';\nimport { useStore } from '../store';\nimport { BarChart3, DollarSign, Target, Zap } from 'lucide-react';\n\nconst SummaryStats: React.FC = () => {\n  const { filteredJournals } = useStore();\n\n  const stats = useMemo(() => {\n    if (filteredJournals.length === 0) {\n      return {\n        totalJournals: 0,\n        avgImpact: 0,\n        avgAPC: 0,\n        fullyOACount: 0,\n        fullyOAPercentage: 0,\n      };\n    }\n\n    const totalJournals = filteredJournals.length;\n    const avgImpact = filteredJournals.reduce((sum, j) => sum + (j.impact_proxy || 0), 0) / totalJournals;\n\n    // Calculate average APC only for journals with APC data (not null/undefined)\n    const journalsWithAPC = filteredJournals.filter(j => j.apc_usd != null && !isNaN(j.apc_usd));\n    const avgAPC = journalsWithAPC.length > 0\n      ? journalsWithAPC.reduce((sum, j) => sum + j.apc_usd, 0) / journalsWithAPC.length\n      : 0;\n\n    const fullyOACount = filteredJournals.filter(j => j.oa_type === 'Fully OA').length;\n    const fullyOAPercentage = totalJournals > 0 ? (fullyOACount / totalJournals) * 100 : 0;\n\n    return {\n      totalJournals,\n      avgImpact: isNaN(avgImpact) ? 0 : avgImpact,\n      avgAPC: isNaN(avgAPC) ? 0 : avgAPC,\n      fullyOACount,\n      fullyOAPercentage: isNaN(fullyOAPercentage) ? 0 : fullyOAPercentage,\n    };\n  }, [filteredJournals]);\n\n  const statCards = [\n    {\n      title: 'Total Journals',\n      value: stats.totalJournals.toLocaleString(),\n      icon: BarChart3,\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-50',\n    },\n    {\n      title: 'Avg Impact',\n      value: stats.avgImpact.toFixed(2),\n      icon: Target,\n      color: 'text-green-600',\n      bgColor: 'bg-green-50',\n    },\n    {\n      title: 'Avg APC',\n      value: `$${Math.round(stats.avgAPC).toLocaleString()}`,\n      icon: DollarSign,\n      color: 'text-yellow-600',\n      bgColor: 'bg-yellow-50',\n    },\n    {\n      title: 'Fully Open Access',\n      value: `${stats.fullyOACount} (${stats.fullyOAPercentage.toFixed(1)}%)`,\n      icon: Zap,\n      color: 'text-purple-600',\n      bgColor: 'bg-purple-50',\n    },\n  ];\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\">\n      {statCards.map((stat, index) => {\n        const Icon = stat.icon;\n        return (\n          <div\n            key={index}\n            className={`${stat.bgColor} rounded-lg p-4 border border-gray-200`}\n          >\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">{stat.title}</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{stat.value}</p>\n              </div>\n              <Icon className={`w-8 h-8 ${stat.color}`} />\n            </div>\n          </div>\n        );\n      })}\n    </div>\n  );\n};\n\nexport default SummaryStats;\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AACA;AACA;AAAA;AAAA;AAAA;;;AAJA;;;;AAMA,MAAM,eAAyB;;IAC7B,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,WAAQ,AAAD;IAEpC,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;uCAAE;YACpB,IAAI,iBAAiB,MAAM,KAAK,GAAG;gBACjC,OAAO;oBACL,eAAe;oBACf,WAAW;oBACX,QAAQ;oBACR,cAAc;oBACd,mBAAmB;gBACrB;YACF;YAEA,MAAM,gBAAgB,iBAAiB,MAAM;YAC7C,MAAM,YAAY,iBAAiB,MAAM;+CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,YAAY,IAAI,CAAC;8CAAG,KAAK;YAExF,6EAA6E;YAC7E,MAAM,kBAAkB,iBAAiB,MAAM;+DAAC,CAAA,IAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,MAAM,EAAE,OAAO;;YAC1F,MAAM,SAAS,gBAAgB,MAAM,GAAG,IACpC,gBAAgB,MAAM;+CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,OAAO;8CAAE,KAAK,gBAAgB,MAAM,GAC/E;YAEJ,MAAM,eAAe,iBAAiB,MAAM;+CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;8CAAY,MAAM;YAClF,MAAM,oBAAoB,gBAAgB,IAAI,AAAC,eAAe,gBAAiB,MAAM;YAErF,OAAO;gBACL;gBACA,WAAW,MAAM,aAAa,IAAI;gBAClC,QAAQ,MAAM,UAAU,IAAI;gBAC5B;gBACA,mBAAmB,MAAM,qBAAqB,IAAI;YACpD;QACF;sCAAG;QAAC;KAAiB;IAErB,MAAM,YAAY;QAChB;YACE,OAAO;YACP,OAAO,MAAM,aAAa,CAAC,cAAc;YACzC,MAAM,qNAAA,CAAA,YAAS;YACf,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,MAAM,SAAS,CAAC,OAAO,CAAC;YAC/B,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,AAAC,IAA6C,OAA1C,KAAK,KAAK,CAAC,MAAM,MAAM,EAAE,cAAc;YAClD,MAAM,qNAAA,CAAA,aAAU;YAChB,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,AAAC,GAAyB,OAAvB,MAAM,YAAY,EAAC,MAAuC,OAAnC,MAAM,iBAAiB,CAAC,OAAO,CAAC,IAAG;YACpE,MAAM,mMAAA,CAAA,MAAG;YACT,OAAO;YACP,SAAS;QACX;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACZ,UAAU,GAAG,CAAC,CAAC,MAAM;YACpB,MAAM,OAAO,KAAK,IAAI;YACtB,qBACE,6LAAC;gBAEC,WAAW,AAAC,GAAe,OAAb,KAAK,OAAO,EAAC;0BAE3B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAAqC,KAAK,KAAK;;;;;;8CAC5D,6LAAC;oCAAE,WAAU;8CAAoC,KAAK,KAAK;;;;;;;;;;;;sCAE7D,6LAAC;4BAAK,WAAW,AAAC,WAAqB,OAAX,KAAK,KAAK;;;;;;;;;;;;eARnC;;;;;QAYX;;;;;;AAGN;GAvFM;;QACyB,sHAAA,CAAA,WAAQ;;;KADjC;uCAyFS", "debugId": null}}, {"offset": {"line": 1756, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/Recommendations.tsx"], "sourcesContent": ["/**\n * Recommendations component for Journal Choice Transparency application.\n */\n\n'use client';\n\nimport React, { useMemo } from 'react';\nimport { useStore } from '../store';\nimport { Journal } from '../types';\nimport { Star, TrendingUp, DollarSign } from 'lucide-react';\n\nconst Recommendations: React.FC = () => {\n  const { filteredJournals, addSelectedJournal } = useStore();\n\n  const recommendations = useMemo(() => {\n    if (filteredJournals.length === 0) return { highImpact: [], bestValue: [], diamond: [] };\n\n    // High impact journals (top 10% by impact)\n    const sortedByImpact = [...filteredJournals]\n      .sort((a, b) => b.impact_proxy - a.impact_proxy)\n      .slice(0, Math.max(5, Math.floor(filteredJournals.length * 0.1)));\n\n    // Best value journals (high cost efficiency, excluding free journals)\n    const paidJournals = filteredJournals.filter(j => j.apc_usd != null && j.apc_usd > 0 && j.cost_efficiency != null);\n    const bestValue = [...paidJournals]\n      .sort((a, b) => {\n        const aEff = a.cost_efficiency === 999999 ? Infinity : a.cost_efficiency;\n        const bEff = b.cost_efficiency === 999999 ? Infinity : b.cost_efficiency;\n        return bEff - aEff;\n      })\n      .slice(0, 5);\n\n    // Diamond/Free OA journals with good impact (including those with null APC)\n    const diamond = filteredJournals\n      .filter(j => (j.apc_usd === 0 || j.apc_usd == null) && j.impact_proxy > 1)\n      .sort((a, b) => b.impact_proxy - a.impact_proxy)\n      .slice(0, 5);\n\n    return {\n      highImpact: sortedByImpact.slice(0, 5),\n      bestValue,\n      diamond,\n    };\n  }, [filteredJournals]);\n\n  const RecommendationCard: React.FC<{\n    journal: Journal;\n    reason: string;\n    icon: React.ComponentType<any>;\n    iconColor: string;\n  }> = ({ journal, reason, icon: Icon, iconColor }) => (\n    <div className=\"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow\">\n      <div className=\"flex items-start gap-3\">\n        <Icon className={`w-5 h-5 mt-1 ${iconColor}`} />\n        <div className=\"flex-1 min-w-0\">\n          <h4 className=\"font-medium text-gray-900 truncate\">{journal.title}</h4>\n          <p className=\"text-sm text-gray-600 mt-1\">{reason}</p>\n          <div className=\"flex items-center gap-4 mt-2 text-xs text-gray-500\">\n            <span>Impact: {journal.impact_proxy.toFixed(2)}</span>\n            <span>APC: {journal.apc_usd != null ? `$${journal.apc_usd.toLocaleString()}` : 'N/A'}</span>\n            <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n              journal.oa_type === 'Fully OA' ? 'bg-green-100 text-green-800' :\n              journal.oa_type === 'Hybrid' ? 'bg-yellow-100 text-yellow-800' :\n              journal.oa_type === 'Diamond' ? 'bg-purple-100 text-purple-800' :\n              'bg-gray-100 text-gray-800'\n            }`}>\n              {journal.oa_type}\n            </span>\n          </div>\n          <button\n            onClick={() => addSelectedJournal(journal.issn_l)}\n            className=\"mt-2 text-xs text-blue-600 hover:text-blue-800 font-medium\"\n          >\n            Add to selection\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n\n  if (filteredJournals.length === 0) {\n    return null;\n  }\n\n  return (\n    <div className=\"bg-gray-50 rounded-lg p-4 mb-6\">\n      <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n        Recommendations Based on Your Filters\n      </h3>\n      \n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* High Impact */}\n        {recommendations.highImpact.length > 0 && (\n          <div>\n            <h4 className=\"font-medium text-gray-900 mb-3 flex items-center gap-2\">\n              <TrendingUp className=\"w-4 h-4 text-green-600\" />\n              High Impact\n            </h4>\n            <div className=\"space-y-3\">\n              {recommendations.highImpact.map((journal) => (\n                <RecommendationCard\n                  key={journal.issn_l}\n                  journal={journal}\n                  reason={`Top ${Math.round((journal.impact_proxy / Math.max(...filteredJournals.map(j => j.impact_proxy))) * 100)}% impact in your selection`}\n                  icon={TrendingUp}\n                  iconColor=\"text-green-600\"\n                />\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Best Value */}\n        {recommendations.bestValue.length > 0 && (\n          <div>\n            <h4 className=\"font-medium text-gray-900 mb-3 flex items-center gap-2\">\n              <Star className=\"w-4 h-4 text-blue-600\" />\n              Best Value\n            </h4>\n            <div className=\"space-y-3\">\n              {recommendations.bestValue.map((journal) => (\n                <RecommendationCard\n                  key={journal.issn_l}\n                  journal={journal}\n                  reason=\"High impact per dollar spent\"\n                  icon={Star}\n                  iconColor=\"text-blue-600\"\n                />\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Diamond/Free OA */}\n        {recommendations.diamond.length > 0 && (\n          <div>\n            <h4 className=\"font-medium text-gray-900 mb-3 flex items-center gap-2\">\n              <DollarSign className=\"w-4 h-4 text-purple-600\" />\n              Free & High Quality\n            </h4>\n            <div className=\"space-y-3\">\n              {recommendations.diamond.map((journal) => (\n                <RecommendationCard\n                  key={journal.issn_l}\n                  journal={journal}\n                  reason=\"No APC with good impact\"\n                  icon={DollarSign}\n                  iconColor=\"text-purple-600\"\n                />\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Recommendations;\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AACA;AAEA;AAAA;AAAA;;;AALA;;;;AAOA,MAAM,kBAA4B;;IAChC,MAAM,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,WAAQ,AAAD;IAExD,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oDAAE;YAC9B,IAAI,iBAAiB,MAAM,KAAK,GAAG,OAAO;gBAAE,YAAY,EAAE;gBAAE,WAAW,EAAE;gBAAE,SAAS,EAAE;YAAC;YAEvF,2CAA2C;YAC3C,MAAM,iBAAiB;mBAAI;aAAiB,CACzC,IAAI;2EAAC,CAAC,GAAG,IAAM,EAAE,YAAY,GAAG,EAAE,YAAY;0EAC9C,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,iBAAiB,MAAM,GAAG;YAE7D,sEAAsE;YACtE,MAAM,eAAe,iBAAiB,MAAM;yEAAC,CAAA,IAAK,EAAE,OAAO,IAAI,QAAQ,EAAE,OAAO,GAAG,KAAK,EAAE,eAAe,IAAI;;YAC7G,MAAM,YAAY;mBAAI;aAAa,CAChC,IAAI;sEAAC,CAAC,GAAG;oBACR,MAAM,OAAO,EAAE,eAAe,KAAK,SAAS,WAAW,EAAE,eAAe;oBACxE,MAAM,OAAO,EAAE,eAAe,KAAK,SAAS,WAAW,EAAE,eAAe;oBACxE,OAAO,OAAO;gBAChB;qEACC,KAAK,CAAC,GAAG;YAEZ,4EAA4E;YAC5E,MAAM,UAAU,iBACb,MAAM;oEAAC,CAAA,IAAK,CAAC,EAAE,OAAO,KAAK,KAAK,EAAE,OAAO,IAAI,IAAI,KAAK,EAAE,YAAY,GAAG;mEACvE,IAAI;oEAAC,CAAC,GAAG,IAAM,EAAE,YAAY,GAAG,EAAE,YAAY;mEAC9C,KAAK,CAAC,GAAG;YAEZ,OAAO;gBACL,YAAY,eAAe,KAAK,CAAC,GAAG;gBACpC;gBACA;YACF;QACF;mDAAG;QAAC;KAAiB;IAErB,MAAM,qBAKD;YAAC,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,IAAI,EAAE,SAAS,EAAE;6BAC9C,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAW,AAAC,gBAAyB,OAAV;;;;;;kCACjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC,QAAQ,KAAK;;;;;;0CACjE,6LAAC;gCAAE,WAAU;0CAA8B;;;;;;0CAC3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAK;4CAAS,QAAQ,YAAY,CAAC,OAAO,CAAC;;;;;;;kDAC5C,6LAAC;;4CAAK;4CAAM,QAAQ,OAAO,IAAI,OAAO,AAAC,IAAoC,OAAjC,QAAQ,OAAO,CAAC,cAAc,MAAO;;;;;;;kDAC/E,6LAAC;wCAAK,WAAW,AAAC,8CAKjB,OAJC,QAAQ,OAAO,KAAK,aAAa,gCACjC,QAAQ,OAAO,KAAK,WAAW,kCAC/B,QAAQ,OAAO,KAAK,YAAY,kCAChC;kDAEC,QAAQ,OAAO;;;;;;;;;;;;0CAGpB,6LAAC;gCACC,SAAS,IAAM,mBAAmB,QAAQ,MAAM;gCAChD,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;IAQT,IAAI,iBAAiB,MAAM,KAAK,GAAG;QACjC,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA2C;;;;;;0BAIzD,6LAAC;gBAAI,WAAU;;oBAEZ,gBAAgB,UAAU,CAAC,MAAM,GAAG,mBACnC,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAA2B;;;;;;;0CAGnD,6LAAC;gCAAI,WAAU;0CACZ,gBAAgB,UAAU,CAAC,GAAG,CAAC,CAAC,wBAC/B,6LAAC;wCAEC,SAAS;wCACT,QAAQ,AAAC,OAAwG,OAAlG,KAAK,KAAK,CAAC,AAAC,QAAQ,YAAY,GAAG,KAAK,GAAG,IAAI,iBAAiB,GAAG,CAAC,CAAA,IAAK,EAAE,YAAY,KAAM,MAAK;wCACjH,MAAM,qNAAA,CAAA,aAAU;wCAChB,WAAU;uCAJL,QAAQ,MAAM;;;;;;;;;;;;;;;;oBAY5B,gBAAgB,SAAS,CAAC,MAAM,GAAG,mBAClC,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;0CAG5C,6LAAC;gCAAI,WAAU;0CACZ,gBAAgB,SAAS,CAAC,GAAG,CAAC,CAAC,wBAC9B,6LAAC;wCAEC,SAAS;wCACT,QAAO;wCACP,MAAM,qMAAA,CAAA,OAAI;wCACV,WAAU;uCAJL,QAAQ,MAAM;;;;;;;;;;;;;;;;oBAY5B,gBAAgB,OAAO,CAAC,MAAM,GAAG,mBAChC,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAA4B;;;;;;;0CAGpD,6LAAC;gCAAI,WAAU;0CACZ,gBAAgB,OAAO,CAAC,GAAG,CAAC,CAAC,wBAC5B,6LAAC;wCAEC,SAAS;wCACT,QAAO;wCACP,MAAM,qNAAA,CAAA,aAAU;wCAChB,WAAU;uCAJL,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAarC;GAjJM;;QAC6C,sHAAA,CAAA,WAAQ;;;KADrD;uCAmJS", "debugId": null}}, {"offset": {"line": 2088, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/OpenPub/journal-transparency/src/app/components/LoadingSkeleton.tsx"], "sourcesContent": ["/**\n * Loading skeleton component for Journal Choice Transparency application.\n */\n\nimport React from 'react';\n\nconst LoadingSkeleton: React.FC = () => {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header Skeleton */}\n      <div className=\"bg-white shadow-sm border-b\">\n        <div className=\"container mx-auto px-4 py-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <div className=\"h-8 bg-gray-200 rounded w-80 mb-2 animate-pulse\"></div>\n              <div className=\"h-4 bg-gray-200 rounded w-96 animate-pulse\"></div>\n            </div>\n            <div className=\"flex space-x-4\">\n              <div className=\"h-6 bg-gray-200 rounded w-16 animate-pulse\"></div>\n              <div className=\"h-6 bg-gray-200 rounded w-24 animate-pulse\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <main className=\"container mx-auto px-4 py-6\">\n        {/* Summary Stats Skeleton */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\">\n          {[...Array(4)].map((_, i) => (\n            <div key={i} className=\"bg-white rounded-lg p-4 border border-gray-200\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <div className=\"h-4 bg-gray-200 rounded w-20 mb-2 animate-pulse\"></div>\n                  <div className=\"h-8 bg-gray-200 rounded w-16 animate-pulse\"></div>\n                </div>\n                <div className=\"h-8 w-8 bg-gray-200 rounded animate-pulse\"></div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Recommendations Skeleton */}\n        <div className=\"bg-gray-50 rounded-lg p-4 mb-6\">\n          <div className=\"h-6 bg-gray-200 rounded w-64 mb-4 animate-pulse\"></div>\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n            {[...Array(3)].map((_, i) => (\n              <div key={i}>\n                <div className=\"h-5 bg-gray-200 rounded w-32 mb-3 animate-pulse\"></div>\n                <div className=\"space-y-3\">\n                  {[...Array(2)].map((_, j) => (\n                    <div key={j} className=\"bg-white border border-gray-200 rounded-lg p-4\">\n                      <div className=\"flex items-start gap-3\">\n                        <div className=\"h-5 w-5 bg-gray-200 rounded animate-pulse\"></div>\n                        <div className=\"flex-1\">\n                          <div className=\"h-4 bg-gray-200 rounded w-full mb-2 animate-pulse\"></div>\n                          <div className=\"h-3 bg-gray-200 rounded w-3/4 mb-2 animate-pulse\"></div>\n                          <div className=\"flex gap-2\">\n                            <div className=\"h-3 bg-gray-200 rounded w-16 animate-pulse\"></div>\n                            <div className=\"h-3 bg-gray-200 rounded w-16 animate-pulse\"></div>\n                            <div className=\"h-3 bg-gray-200 rounded w-16 animate-pulse\"></div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Main Content Skeleton */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-400px)]\">\n          {/* Filter Panel Skeleton */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-white rounded-lg shadow-sm border p-4 h-full\">\n              <div className=\"h-6 bg-gray-200 rounded w-24 mb-4 animate-pulse\"></div>\n              <div className=\"space-y-4\">\n                <div className=\"h-10 bg-gray-200 rounded animate-pulse\"></div>\n                <div className=\"space-y-2\">\n                  {[...Array(5)].map((_, i) => (\n                    <div key={i} className=\"flex items-center gap-2\">\n                      <div className=\"h-4 w-4 bg-gray-200 rounded animate-pulse\"></div>\n                      <div className=\"h-4 bg-gray-200 rounded w-32 animate-pulse\"></div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Visualization Area Skeleton */}\n          <div className=\"lg:col-span-3 flex flex-col gap-6\">\n            {/* Scatter Plot Skeleton */}\n            <div className=\"bg-white rounded-lg shadow-sm border p-4 h-1/2\">\n              <div className=\"h-6 bg-gray-200 rounded w-40 mb-4 animate-pulse\"></div>\n              <div className=\"h-full bg-gray-100 rounded animate-pulse\"></div>\n            </div>\n\n            {/* Data Table Skeleton */}\n            <div className=\"bg-white rounded-lg shadow-sm border p-4 h-1/2\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <div className=\"h-6 bg-gray-200 rounded w-32 animate-pulse\"></div>\n                <div className=\"h-8 bg-gray-200 rounded w-40 animate-pulse\"></div>\n              </div>\n              <div className=\"space-y-2\">\n                {[...Array(8)].map((_, i) => (\n                  <div key={i} className=\"h-12 bg-gray-100 rounded animate-pulse\"></div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Footer Skeleton */}\n      <div className=\"bg-white border-t mt-8\">\n        <div className=\"container mx-auto px-4 py-6\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <div className=\"mb-4 md:mb-0\">\n              <div className=\"h-4 bg-gray-200 rounded w-80 mb-1 animate-pulse\"></div>\n              <div className=\"h-4 bg-gray-200 rounded w-40 animate-pulse\"></div>\n            </div>\n            <div className=\"flex space-x-6\">\n              {[...Array(3)].map((_, i) => (\n                <div key={i} className=\"h-4 bg-gray-200 rounded w-16 animate-pulse\"></div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LoadingSkeleton;\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAID,MAAM,kBAA4B;IAChC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;0CAEjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMvB,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;gCAAY,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;sDAEjB,6LAAC;4CAAI,WAAU;;;;;;;;;;;;+BANT;;;;;;;;;;kCAad,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wDAAY,WAAU;kEACrB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;;;;;;8FACf,6LAAC;oFAAI,WAAU;;;;;;8FACf,6LAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;uDATb;;;;;;;;;;;uCAJN;;;;;;;;;;;;;;;;kCA0BhB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;8DACZ;2DAAI,MAAM;qDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;4DAAY,WAAU;;8EACrB,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;;;;;;2DAFP;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAWpB,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAIjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;;;;;;;;;;;0DAEjB,6LAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wDAAY,WAAU;uDAAb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;0CAEjB,6LAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wCAAY,WAAU;uCAAb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1B;KA/HM;uCAiIS", "debugId": null}}, {"offset": {"line": 2597, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/OpenPub/journal-transparency/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useStore } from './store';\nimport { Journal } from './types';\nimport FilterPanel from './components/FilterPanel';\nimport ScatterPlot from './components/ScatterPlot';\nimport DataTable from './components/DataTable';\nimport Header from './components/Header';\nimport Footer from './components/Footer';\nimport SummaryStats from './components/SummaryStats';\nimport Recommendations from './components/Recommendations';\nimport LoadingSkeleton from './components/LoadingSkeleton';\n\nexport default function Home() {\n  const { setJournals, setLoading, setError, isLoading, error, journals } = useStore();\n\n  useEffect(() => {\n    const loadData = async () => {\n      setLoading(true);\n      try {\n        const response = await fetch('/journals_mvp.json');\n        if (!response.ok) {\n          throw new Error(`Failed to load journal data: ${response.status}`);\n        }\n        const data: Journal[] = await response.json();\n        setJournals(data);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'Failed to load data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadData();\n  }, [setJournals, setLoading, setError]);\n\n  if (isLoading) {\n    return <LoadingSkeleton />;\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center text-red-600\">\n          <p className=\"text-xl font-semibold mb-2\">Error loading data</p>\n          <p>{error}</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n\n      <main className=\"container mx-auto px-4 py-6\">\n        <SummaryStats />\n        <Recommendations />\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\n          {/* Filter Panel */}\n          <div className=\"lg:col-span-1 order-2 lg:order-1\">\n            <div className=\"lg:sticky lg:top-6\">\n              <FilterPanel />\n            </div>\n          </div>\n\n          {/* Visualization Area */}\n          <div className=\"lg:col-span-3 order-1 lg:order-2 flex flex-col gap-6\">\n            {/* Scatter Plot */}\n            <div className=\"bg-white rounded-lg shadow-sm border p-4 h-[400px] lg:h-[500px]\">\n              <ScatterPlot />\n            </div>\n\n            {/* Data Table */}\n            <div className=\"bg-white rounded-lg shadow-sm border p-4 h-[600px] lg:h-[700px]\">\n              <DataTable />\n            </div>\n          </div>\n        </div>\n      </main>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;AAce,SAAS;;IACtB,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,WAAQ,AAAD;IAEjF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM;2CAAW;oBACf,WAAW;oBACX,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM;wBAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;4BAChB,MAAM,IAAI,MAAM,AAAC,gCAA+C,OAAhB,SAAS,MAAM;wBACjE;wBACA,MAAM,OAAkB,MAAM,SAAS,IAAI;wBAC3C,YAAY;oBACd,EAAE,OAAO,KAAK;wBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;oBAChD,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;yBAAG;QAAC;QAAa;QAAY;KAAS;IAEtC,IAAI,WAAW;QACb,qBAAO,6LAAC,+IAAA,CAAA,UAAe;;;;;IACzB;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAC1C,6LAAC;kCAAG;;;;;;;;;;;;;;;;;IAIZ;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,sIAAA,CAAA,UAAM;;;;;0BAEP,6LAAC;gBAAK,WAAU;;kCACd,6LAAC,4IAAA,CAAA,UAAY;;;;;kCACb,6LAAC,+IAAA,CAAA,UAAe;;;;;kCAEhB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2IAAA,CAAA,UAAW;;;;;;;;;;;;;;;0CAKhB,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,2IAAA,CAAA,UAAW;;;;;;;;;;kDAId,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yIAAA,CAAA,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMlB,6LAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;GAxEwB;;QACoD,sHAAA,CAAA,WAAQ;;;KAD5D", "debugId": null}}]}