# Journal Choice Transparency

A web application that helps scientists choose the right journal for their research based on impact metrics, Article Processing Charges (APC), and open access status.

## Features

- **Interactive Scatter Plot**: Visualize the relationship between journal impact and APC costs
- **Advanced Filtering**: Filter journals by subject area, open access type, APC range, and impact metrics
- **Smart Recommendations**: Get personalized journal recommendations based on your criteria
- **Data Export**: Export filtered or selected journals to CSV
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## Data Sources

- **SciMago Journal Rankings**: Impact metrics and journal metadata
- **MDPI**: Article Processing Charges for MDPI journals
- **OpenAPC**: Historical APC data from various publishers
- **DOAJ**: Directory of Open Access Journals

## Technology Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS
- **Charts**: Recharts
- **Tables**: TanStack Table
- **State Management**: Zustand
- **Data Processing**: Python with pandas

## Getting Started

### Prerequisites

- Node.js 18+
- Python 3.8+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd journal-transparency
```

2. Install dependencies:
```bash
npm install
```

3. Process the data (if needed):
```bash
cd ..
python data_processor.py
cp public/journals_mvp.json journal-transparency/public/
```

4. Run the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Usage

1. **Browse Journals**: View all journals in the scatter plot and table
2. **Apply Filters**: Use the filter panel to narrow down journals by:
   - Subject area
   - Open access type
   - APC price range
   - Minimum impact threshold
   - Journal title search
3. **Get Recommendations**: View personalized recommendations based on your filters
4. **Select Journals**: Click on points in the scatter plot or rows in the table to select journals
5. **Export Data**: Export your filtered or selected journals to CSV

## Project Structure

```
journal-transparency/
├── src/app/
│   ├── components/          # React components
│   │   ├── DataTable.tsx    # Journal data table
│   │   ├── FilterPanel.tsx  # Filter controls
│   │   ├── ScatterPlot.tsx  # Interactive scatter plot
│   │   ├── Header.tsx       # Page header
│   │   ├── Footer.tsx       # Page footer
│   │   ├── SummaryStats.tsx # Statistics cards
│   │   ├── Recommendations.tsx # Smart recommendations
│   │   ├── ExportButton.tsx # CSV export functionality
│   │   └── LoadingSkeleton.tsx # Loading state
│   ├── store.ts             # Zustand state management
│   ├── types.ts             # TypeScript type definitions
│   └── page.tsx             # Main page component
├── public/
│   └── journals_mvp.json    # Processed journal data
└── data_processor.py        # Python data processing script
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Acknowledgments

- SciMago for journal ranking data
- MDPI for APC transparency
- OpenAPC initiative for open APC data
- DOAJ for open access journal directory
