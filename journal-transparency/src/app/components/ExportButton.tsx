/**
 * Export button component for Journal Choice Transparency application.
 */

'use client';

import React from 'react';
import { useStore } from '../store';
import { Download } from 'lucide-react';

const ExportButton: React.FC = () => {
  const { filteredJournals, selectedJournals } = useStore();

  const exportToCSV = (journals: any[], filename: string) => {
    if (journals.length === 0) return;

    // Define CSV headers
    const headers = [
      'Title',
      'ISSN-L',
      'Subject',
      'Impact Proxy',
      'APC (USD)',
      'OA Type',
      'License',
      'Cost Efficiency',
      'Publisher',
      'Last Verified',
    ];

    // Convert journals to CSV rows
    const csvRows = [
      headers.join(','),
      ...journals.map(journal => [
        `"${journal.title.replace(/"/g, '""')}"`,
        journal.issn_l,
        `"${journal.subject.replace(/"/g, '""')}"`,
        journal.impact_proxy.toFixed(2),
        journal.apc_usd != null ? journal.apc_usd : 'N/A',
        `"${journal.oa_type}"`,
        `"${journal.license}"`,
        journal.cost_efficiency == null ? 'N/A' : journal.cost_efficiency === Infinity ? 'Infinity' : journal.cost_efficiency.toFixed(4),
        `"${journal.publisher.replace(/"/g, '""')}"`,
        journal.last_verified,
      ].join(','))
    ];

    // Create and download CSV file
    const csvContent = csvRows.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleExportSelected = () => {
    const selectedJournalsList = filteredJournals.filter(journal => 
      selectedJournals.has(journal.issn_l)
    );
    exportToCSV(selectedJournalsList, 'selected_journals.csv');
  };

  const handleExportFiltered = () => {
    exportToCSV(filteredJournals, 'filtered_journals.csv');
  };

  return (
    <div className="flex items-center gap-2">
      <button
        onClick={handleExportFiltered}
        disabled={filteredJournals.length === 0}
        className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <Download className="w-4 h-4" />
        Export Filtered ({filteredJournals.length})
      </button>
      
      {selectedJournals.size > 0 && (
        <button
          onClick={handleExportSelected}
          className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <Download className="w-4 h-4" />
          Export Selected ({selectedJournals.size})
        </button>
      )}
    </div>
  );
};

export default ExportButton;
