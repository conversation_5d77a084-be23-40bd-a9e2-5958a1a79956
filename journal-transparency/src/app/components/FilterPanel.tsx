/**
 * Filter panel component for Journal Choice Transparency application.
 */

'use client';

import React, { useMemo } from 'react';
import { useStore } from '../store';
import { Search, Filter } from 'lucide-react';

const FilterPanel: React.FC = () => {
  const {
    journals,
    filteredJournals,
    filters,
    setFilters,
    clearSelectedJournals
  } = useStore();

  // Get unique values for filter options
  const filterOptions = useMemo(() => {
    const subjects = new Set(journals.map(j => j.subject).filter(Boolean));
    const oaTypes = new Set(journals.map(j => j.oa_type).filter(Boolean));
    
    const maxApc = Math.max(...journals.map(j => j.apc_usd));
    const maxImpact = Math.max(...journals.map(j => j.impact_proxy));
    
    return {
      subjects: Array.from(subjects).sort(),
      oaTypes: Array.from(oaTypes).sort(),
      maxApc: Math.ceil(maxApc / 1000) * 1000, // Round up to nearest 1000
      maxImpact: Math.ceil(maxImpact), // Round up to nearest integer
    };
  }, [journals]);

  const handleSubjectChange = (subject: string, checked: boolean) => {
    const newSubjects = new Set(filters.subjects);
    if (checked) {
      newSubjects.add(subject);
    } else {
      newSubjects.delete(subject);
    }
    setFilters({ subjects: newSubjects });
    clearSelectedJournals();
  };

  const handleOATypeChange = (oaType: string, checked: boolean) => {
    const newOATypes = new Set(filters.oaTypes);
    if (checked) {
      newOATypes.add(oaType);
    } else {
      newOATypes.delete(oaType);
    }
    setFilters({ oaTypes: newOATypes });
    clearSelectedJournals();
  };

  const handleSearchChange = (searchTerm: string) => {
    setFilters({ searchTerm });
    clearSelectedJournals();
  };

  const handleIncludeNaApcChange = (includeNaApc: boolean) => {
    setFilters({ includeNaApc });
    clearSelectedJournals();
  };

  const handleAPCRangeChange = (value: number[]) => {
    setFilters({ apcRange: [value[0], value[1]] as [number, number] });
    clearSelectedJournals();
  };

  const handleImpactRangeChange = (value: number[]) => {
    setFilters({ impactRange: [value[0], value[1]] as [number, number] });
    clearSelectedJournals();
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border p-4 max-h-[800px] lg:h-full overflow-y-auto">
      <div className="flex items-center gap-2 mb-4">
        <Filter className="w-5 h-5 text-gray-600" />
        <h2 className="text-lg font-semibold text-gray-900">Filters</h2>
        <span className="ml-auto text-sm text-gray-500">
          {filteredJournals.length} results
        </span>
      </div>

      {/* Search */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Search Journals
        </label>
        <div className="relative mb-2">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search by journal title..."
            value={filters.searchTerm}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        {/* Quick Search Buttons */}
        <div className="flex flex-wrap gap-1">
          <button
            onClick={() => handleSearchChange('Cell')}
            className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
          >
            Cell
          </button>
          <button
            onClick={() => handleSearchChange('Nature')}
            className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
          >
            Nature
          </button>
          <button
            onClick={() => handleSearchChange('Science')}
            className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
          >
            Science
          </button>
        </div>
      </div>

      {/* Quick Filters */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Quick Filters
        </label>
        <div className="space-y-2">
          <button
            onClick={() => {
              setFilters({ impactRange: [15, filterOptions.maxImpact] });
              clearSelectedJournals();
            }}
            className="w-full px-3 py-2 text-sm bg-purple-100 text-purple-700 rounded hover:bg-purple-200 transition-colors"
          >
            High Impact (≥15)
          </button>
          <button
            onClick={() => {
              setFilters({ impactRange: [10, 15] });
              clearSelectedJournals();
            }}
            className="w-full px-3 py-2 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
          >
            Medium Impact (10-15)
          </button>
          <button
            onClick={() => {
              setFilters({ apcRange: [0, 0] });
              clearSelectedJournals();
            }}
            className="w-full px-3 py-2 text-sm bg-yellow-100 text-yellow-700 rounded hover:bg-yellow-200 transition-colors"
          >
            Free Journals (No APC)
          </button>
        </div>
      </div>

      {/* Subject Filter */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Subject Area
        </label>
        <div className="max-h-40 overflow-y-auto space-y-2">
          {filterOptions.subjects.slice(0, 20).map((subject) => (
            <label key={subject} className="flex items-center">
              <input
                type="checkbox"
                checked={filters.subjects.has(subject)}
                onChange={(e) => handleSubjectChange(subject, e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700 truncate">{subject}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Open Access Type Filter */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Open Access Type
        </label>
        <div className="space-y-2">
          {filterOptions.oaTypes.map((oaType) => (
            <label key={oaType} className="flex items-center">
              <input
                type="checkbox"
                checked={filters.oaTypes.has(oaType)}
                onChange={(e) => handleOATypeChange(oaType, e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">{oaType}</span>
            </label>
          ))}
        </div>
      </div>

      {/* APC Range */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          APC Range (USD): ${filters.apcRange[0]} - ${filters.apcRange[1]}
        </label>
        <input
          type="range"
          min="0"
          max={filterOptions.maxApc}
          step="100"
          value={filters.apcRange[1]}
          onChange={(e) => handleAPCRangeChange([filters.apcRange[0], parseInt(e.target.value)])}
          className="w-full"
        />
        <div className="mt-2">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={filters.includeNaApc}
              onChange={(e) => handleIncludeNaApcChange(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700">Include journals with N/A APC (Note: N/A ≠ Free)</span>
          </label>
        </div>
      </div>

      {/* Impact Range */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Min Impact: {filters.impactRange[0].toFixed(1)}
        </label>
        <input
          type="range"
          min="0"
          max={filterOptions.maxImpact}
          step="0.1"
          value={filters.impactRange[0]}
          onChange={(e) => handleImpactRangeChange([parseFloat(e.target.value), filters.impactRange[1]])}
          className="w-full"
        />
      </div>

      {/* Clear Filters */}
      <button
        onClick={() => {
          setFilters({
            subjects: new Set(),
            oaTypes: new Set(),
            apcRange: [0, filterOptions.maxApc],
            impactRange: [0, filterOptions.maxImpact],
            searchTerm: '',
            includeNaApc: false,
          });
          clearSelectedJournals();
        }}
        className="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        Clear All Filters
      </button>
    </div>
  );
};

export default FilterPanel;
