/**
 * Footer component for Journal Choice Transparency application.
 */

import React from 'react';

const Footer: React.FC = () => {
  return (
    <footer className="bg-white border-t mt-8">
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="text-sm text-gray-600 mb-4 md:mb-0">
            <p>
              Data sources: SciMago Journal Rankings, MDPI, OpenAPC, DOAJ
            </p>
            <p className="mt-1">
              Last updated: July 19, 2025
            </p>
          </div>
          
          <div className="text-sm text-gray-600">
            © 2025 Journal Watch
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
