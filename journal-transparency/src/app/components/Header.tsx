/**
 * Header component for Journal Choice Transparency application.
 */

'use client';

import React, { useState } from 'react';

const Header: React.FC = () => {
  const [showSubscribe, setShowSubscribe] = useState(false);
  const [email, setEmail] = useState('');
  const [subscribed, setSubscribed] = useState(false);

  const handleSubscribe = (e: React.FormEvent) => {
    e.preventDefault();
    if (email) {
      // Here you would typically send the email to your backend
      console.log('Subscribing email:', email);
      setSubscribed(true);
      setTimeout(() => {
        setShowSubscribe(false);
        setSubscribed(false);
        setEmail('');
      }, 2000);
    }
  };

  return (
    <>
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Journal Watch
              </h1>
              <p className="text-gray-600 mt-2">
                Find the right journal for your research based on SJR impact, cost, and openness
              </p>
            </div>

            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowSubscribe(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
              >
                Subscribe for Updates
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Subscribe Modal */}
      {showSubscribe && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Subscribe for Updates</h3>
            {subscribed ? (
              <div className="text-center">
                <div className="text-green-600 text-2xl mb-2">✓</div>
                <p className="text-gray-600">Thank you for subscribing!</p>
              </div>
            ) : (
              <form onSubmit={handleSubscribe}>
                <p className="text-gray-600 mb-4">
                  Get notified when we add new data sources or features.
                </p>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mb-4"
                  required
                />
                <div className="flex gap-2">
                  <button
                    type="submit"
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
                  >
                    Subscribe
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowSubscribe(false)}
                    className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md font-medium"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default Header;
