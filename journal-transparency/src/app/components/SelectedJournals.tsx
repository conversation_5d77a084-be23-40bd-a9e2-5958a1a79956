'use client';

import React, { useMemo } from 'react';
import { useStore } from '../store';
import { Check<PERSON><PERSON>cle, XCircle, Trash2, Eye, EyeOff } from 'lucide-react';

const SelectedJournals: React.FC = () => {
  const { 
    filteredJournals, 
    selectedJournals, 
    removeSelectedJournal, 
    clearSelectedJournals 
  } = useStore();

  const { selected, deselected } = useMemo(() => {
    const selected = filteredJournals.filter(journal => selectedJournals.has(journal.issn_l));
    const deselected = filteredJournals.filter(journal => !selectedJournals.has(journal.issn_l));
    
    return { selected, deselected };
  }, [filteredJournals, selectedJournals]);

  const [showDeselected, setShowDeselected] = React.useState(false);

  if (filteredJournals.length === 0) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Journal Selection Status
        </h3>
        {selected.length > 0 && (
          <button
            onClick={clearSelectedJournals}
            className="flex items-center gap-1 px-3 py-1 text-sm text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors"
          >
            <Trash2 className="w-4 h-4" />
            Clear All
          </button>
        )}
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="bg-green-50 rounded-lg p-3">
          <div className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <div>
              <p className="text-sm font-medium text-green-800">Selected</p>
              <p className="text-lg font-bold text-green-900">{selected.length}</p>
            </div>
          </div>
        </div>
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="flex items-center gap-2">
            <XCircle className="w-5 h-5 text-gray-600" />
            <div>
              <p className="text-sm font-medium text-gray-700">Available</p>
              <p className="text-lg font-bold text-gray-900">{deselected.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Selected Journals List */}
      {selected.length > 0 && (
        <div className="mb-4">
          <h4 className="text-md font-medium text-gray-900 mb-2 flex items-center gap-2">
            <CheckCircle className="w-4 h-4 text-green-600" />
            Selected Journals ({selected.length})
          </h4>
          <div className="max-h-48 overflow-y-auto space-y-2">
            {selected
              .sort((a, b) => b.impact_proxy - a.impact_proxy) // Sort by impact
              .map((journal) => (
                <div
                  key={journal.issn_l}
                  className="flex items-center justify-between p-2 bg-green-50 rounded-md border border-green-200"
                >
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-green-900 truncate">
                      {journal.title}
                    </p>
                    <p className="text-xs text-green-700">
                      Impact: {journal.impact_proxy.toFixed(2)} | 
                      APC: ${journal.apc_usd?.toLocaleString() || 'N/A'}
                    </p>
                  </div>
                  <button
                    onClick={() => removeSelectedJournal(journal.issn_l)}
                    className="ml-2 p-1 text-green-600 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                    title="Remove from selection"
                  >
                    <XCircle className="w-4 h-4" />
                  </button>
                </div>
              ))}
          </div>
        </div>
      )}

      {/* Toggle for Deselected Journals */}
      <div className="border-t pt-4">
        <button
          onClick={() => setShowDeselected(!showDeselected)}
          className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-800 mb-2"
        >
          {showDeselected ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
          {showDeselected ? 'Hide' : 'Show'} Available Journals ({deselected.length})
        </button>

        {showDeselected && (
          <div className="max-h-48 overflow-y-auto space-y-1">
            {deselected
              .sort((a, b) => b.impact_proxy - a.impact_proxy) // Sort by impact
              .slice(0, 50) // Limit to first 50 to avoid performance issues
              .map((journal) => (
                <div
                  key={journal.issn_l}
                  className="p-2 bg-gray-50 rounded-md border border-gray-200"
                >
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {journal.title}
                  </p>
                  <p className="text-xs text-gray-600">
                    Impact: {journal.impact_proxy.toFixed(2)} | 
                    APC: ${journal.apc_usd?.toLocaleString() || 'N/A'}
                  </p>
                </div>
              ))}
            {deselected.length > 50 && (
              <p className="text-xs text-gray-500 text-center py-2">
                ... and {deselected.length - 50} more journals
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default SelectedJournals;
