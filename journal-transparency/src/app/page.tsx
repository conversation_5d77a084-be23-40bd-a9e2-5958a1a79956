'use client';

import { useEffect } from 'react';
import { useStore } from './store';
import { Journal } from './types';
import FilterPanel from './components/FilterPanel';
import ScatterPlot from './components/ScatterPlot';
import DataTable from './components/DataTable';
import Header from './components/Header';
import Footer from './components/Footer';
import Recommendations from './components/Recommendations';
import LoadingSkeleton from './components/LoadingSkeleton';
import SelectedJournals from './components/SelectedJournals';

export default function Home() {
  const { setJournals, setLoading, setError, isLoading, error } = useStore();

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        const response = await fetch('/journals_mvp.json');
        if (!response.ok) {
          throw new Error(`Failed to load journal data: ${response.status}`);
        }
        const data: Journal[] = await response.json();
        setJournals(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [setJournals, setLoading, setError]);

  if (isLoading) {
    return <LoadingSkeleton />;
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center text-red-600">
          <p className="text-xl font-semibold mb-2">Error loading data</p>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Filter Panel */}
          <div className="lg:col-span-1 order-2 lg:order-1">
            <div className="lg:sticky lg:top-6 space-y-4">
              <FilterPanel />
              <SelectedJournals />
            </div>
          </div>

          {/* Visualization Area */}
          <div className="lg:col-span-3 order-1 lg:order-2 flex flex-col gap-6">
            {/* Scatter Plot */}
            <div className="bg-white rounded-lg shadow-sm border p-4 h-[400px] lg:h-[500px]">
              <ScatterPlot />
            </div>

            {/* Data Table */}
            <div className="bg-white rounded-lg shadow-sm border p-4 h-[600px] lg:h-[700px]">
              <DataTable />
            </div>
          </div>
        </div>

        <Recommendations />
      </main>

      <Footer />
    </div>
  );
}
