/**
 * Zustand store for Journal Choice Transparency application.
 */

import { create } from 'zustand';
import { Journal, FilterState, AppState } from './types';

interface StoreActions {
  setJournals: (journals: Journal[]) => void;
  setFilters: (filters: Partial<FilterState>) => void;
  setSelectedJournals: (selected: Set<string>) => void;
  addSelectedJournal: (issn: string) => void;
  removeSelectedJournal: (issn: string) => void;
  clearSelectedJournals: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  applyFilters: () => void;
}

type Store = AppState & StoreActions;

const initialFilters: FilterState = {
  oaTypes: new Set(),
  apcRange: [0, 10000],
  impactRange: [0, 150], // Increased to include high-impact journals like Cell (22.6) and Nature (18.3)
  searchTerm: '',
  includeNaApc: false,
};

export const useStore = create<Store>((set, get) => ({
  // State
  journals: [],
  filteredJournals: [],
  selectedJournals: new Set(),
  filters: initialFilters,
  isLoading: false,
  error: null,

  // Actions
  setJournals: (journals) => {
    set({ journals });
    get().applyFilters();
  },

  setFilters: (newFilters) => {
    const currentFilters = get().filters;
    const updatedFilters = { ...currentFilters, ...newFilters };
    set({ filters: updatedFilters });
    // Debounce filter application for better performance
    setTimeout(() => get().applyFilters(), 100);
  },

  setSelectedJournals: (selected) => set({ selectedJournals: selected }),

  addSelectedJournal: (issn) => {
    const current = get().selectedJournals;
    const updated = new Set(current);
    updated.add(issn);
    set({ selectedJournals: updated });
  },

  removeSelectedJournal: (issn) => {
    const current = get().selectedJournals;
    const updated = new Set(current);
    updated.delete(issn);
    set({ selectedJournals: updated });
  },

  clearSelectedJournals: () => set({ selectedJournals: new Set() }),

  setLoading: (isLoading) => set({ isLoading }),

  setError: (error) => set({ error }),

  applyFilters: () => {
    const { journals, filters } = get();
    
    const filtered = journals.filter((journal) => {


      // OA Type filter
      if (filters.oaTypes.size > 0 && !filters.oaTypes.has(journal.oa_type)) {
        return false;
      }

      // APC range filter - handle null values
      if (journal.apc_usd != null) {
        if (journal.apc_usd < filters.apcRange[0] || journal.apc_usd > filters.apcRange[1]) {
          return false;
        }
      } else if (!filters.includeNaApc) {
        // Exclude journals with N/A APC unless user specifically includes them
        return false;
      }

      // Impact proxy range filter
      if (journal.impact_proxy < filters.impactRange[0] || journal.impact_proxy > filters.impactRange[1]) {
        return false;
      }

      // Search term filter
      if (filters.searchTerm && !journal.title.toLowerCase().includes(filters.searchTerm.toLowerCase())) {
        return false;
      }

      return true;
    });

    set({ filteredJournals: filtered });
  },
}));
