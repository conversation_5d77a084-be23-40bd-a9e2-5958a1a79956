/**
 * Type definitions for Journal Choice Transparency application.
 */

export interface Journal {
  issn_l: string;
  title: string;
  subject: string;
  sjr_impact: number;
  apc_usd: number;
  oa_type: string;
  license: string;
  waiver: boolean;
  cost_efficiency: number;
  last_verified: string;
  journal_url: string;
  publisher: string;
}

export interface FilterState {
  subjects: Set<string>;
  oaTypes: Set<string>;
  apcRange: [number, number];
  sjrRange: [number, number];
  searchTerm: string;
  includeNaApc: boolean;
}

export interface AppState {
  journals: Journal[];
  filteredJournals: Journal[];
  selectedJournals: Set<string>;
  filters: FilterState;
  isLoading: boolean;
  error: string | null;
}

export type OAType = 'Fully OA' | 'Hybrid' | 'Diamond' | 'Subscription' | 'Unknown';

export interface ChartDimensions {
  width: number;
  height: number;
  margin: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}
